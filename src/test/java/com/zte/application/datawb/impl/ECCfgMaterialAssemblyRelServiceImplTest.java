package com.zte.application.datawb.impl;

import com.zte.application.datawb.BarcodeContractService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * ECCfgMaterialAssemblyRelServiceImpl单元测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-05
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CollectionUtils.class, CommonUtils.class})
public class ECCfgMaterialAssemblyRelServiceImplTest {

    @InjectMocks
    private ECCfgMaterialAssemblyRelServiceImpl ecCfgMaterialAssemblyRelService;

    @Mock
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Mock
    private BarcodeContractService barcodeContractService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getAssemblyRelationList方法 - 成功场景
     * 验证正常流程下的数据处理和转换
     */
    @Test
    public void testGetAssemblyRelationList_Success() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001", "SERVER002");

        // Mock数据准备
        List<CpmConfigItemAssembleDTO> cpmConfigList1 = createMockCpmConfigItemAssembleList("1001");
        List<CpmConfigItemAssembleDTO> cpmConfigList2 = createMockCpmConfigItemAssembleList("1002");
        List<CpmConfigItemAssembleDTO> configDetailList = createMockConfigDetailList();
        List<ECMaterialAssemblyDTO> contractInfoList = createMockContractInfoList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> assemblyMaterialList = createMockAssemblyMaterialList();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList1);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1002))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(assemblyMaterialList);
        when(barcodeContractService.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(contractInfoList);

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为1", 1, result.size());

        ECMaterialAssemblyDTO firstResult = result.get(0);
        Assert.assertEquals("服务器SN应匹配", "SERVER001", firstResult.getServerSn());
        Assert.assertEquals("合同号应匹配", "CONTRACT001", firstResult.getContractNumber());
        Assert.assertEquals("任务号应匹配", "ENTITY001", firstResult.getEntityName());
        Assert.assertEquals("组织ID应匹配", "ORG001", firstResult.getOrgId());
        Assert.assertNotNull("装配列表不应为null", firstResult.getAssembleList());
    }

    /**
     * 测试getAssemblyRelationList方法 - 空输入列表
     * 验证空输入的处理
     */
    @Test
    public void testGetAssemblyRelationList_EmptyInput() {
        // Given
        List<String> serverSnList = new ArrayList<>();

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAssemblyRelationList方法 - 第一次查询返回空
     * 验证cpmConfigItemAssembleDTOList为空时的分支
     */
    @Test
    public void testGetAssemblyRelationList_FirstQueryEmpty() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");

        // Mock第一次查询返回空
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(new ArrayList<>());

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAssemblyRelationList方法 - 第二次查询返回空
     * 验证configDetailDTOList为空时的分支
     */
    @Test
    public void testGetAssemblyRelationList_SecondQueryEmpty() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<CpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleList("1001");

        // Mock第一次查询有结果，第二次查询返回空
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(new ArrayList<>());

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 成功场景
     * 验证四层递归查询的正常流程
     */
    @Test
    public void testGetAllWsmAssembleLines_Success() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> level2Results = createMockLevel2Results();
        List<ECWsmAssembleLinesEntityWithNameDTO> level3Results = createMockLevel3Results();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(level2Results)
            .thenReturn(level3Results)
            .thenReturn(new ArrayList<>());  // 第4层返回空，提前退出

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertNotNull("列表不应为null", wsmAssembleLinesList);
        // 验证列表被添加了新的元素（原始 + level2 + level3）
        Assert.assertTrue("列表应包含更多元素", wsmAssembleLinesList.size() > 2);
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 第二层查询返回空
     * 验证提前退出的分支
     */
    @Test
    public void testGetAllWsmAssembleLines_Level2Empty() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(new ArrayList<>());  // 第2层就返回空

        int originalSize = wsmAssembleLinesList.size();

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertEquals("列表大小不应改变", originalSize, wsmAssembleLinesList.size());
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 无需展开的条码
     * 验证当没有以"1"开头的物料代码和机框模组代码时的分支
     */
    @Test
    public void testGetAllWsmAssembleLines_NoExpandableBarcodes() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesListNoExpandable();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);

        int originalSize = wsmAssembleLinesList.size();

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertEquals("列表大小不应改变", originalSize, wsmAssembleLinesList.size());
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 达到最大层数
     * 验证查询到第4层的完整流程
     */
    @Test
    public void testGetAllWsmAssembleLines_MaxLevels() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> levelResults = createMockLevel2Results();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(levelResults);  // 每层都返回结果，直到第4层

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertNotNull("列表不应为null", wsmAssembleLinesList);
        // 验证经过4层查询后列表被扩展
        Assert.assertTrue("列表应包含更多元素", wsmAssembleLinesList.size() > 2);
    }

    // ==================== Mock数据创建方法 ====================

    /**
     * 创建Mock的CpmConfigItemAssembleDTO列表
     */
    private List<CpmConfigItemAssembleDTO> createMockCpmConfigItemAssembleList(String entityId) {
        List<CpmConfigItemAssembleDTO> list = new ArrayList<>();
        CpmConfigItemAssembleDTO dto = new CpmConfigItemAssembleDTO();
        dto.setEntityId(entityId);
        dto.setItemBarcode("BARCODE001");
        dto.setBarcodeQty("1");
        dto.setItemCode("1001001");
        dto.setBarcodeType("SN");
        dto.setItemName("测试物料");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的配置详情列表
     */
    private List<CpmConfigItemAssembleDTO> createMockConfigDetailList() {
        List<CpmConfigItemAssembleDTO> list = new ArrayList<>();

        CpmConfigItemAssembleDTO dto1 = new CpmConfigItemAssembleDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setBarcodeQty("1");
        dto1.setItemCode("1001001");  // 以1开头
        dto1.setBarcodeType("SN");
        dto1.setItemName("测试物料1");
        list.add(dto1);

        CpmConfigItemAssembleDTO dto2 = new CpmConfigItemAssembleDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setBarcodeQty("2");
        dto2.setItemCode("2001001");  // 不以1开头
        dto2.setBarcodeType("PN");
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的合同信息列表
     */
    private List<ECMaterialAssemblyDTO> createMockContractInfoList() {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();
        ECMaterialAssemblyDTO dto = new ECMaterialAssemblyDTO();
        dto.setContractNumber("CONTRACT001");
        dto.setEntityName("ENTITY001");
        dto.setOrgId("ORG001");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的数据字典值列表
     */
    private List<SysLookupValues> createMockLookupValues() {
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues lookup = new SysLookupValues();
        lookup.setDescription("MODULE001");  // 机框模组物料代码
        list.add(lookup);
        return list;
    }

    /**
     * 创建Mock的装配物料列表
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockAssemblyMaterialList() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("ASSEMBLY001");
        dto.setScanType("SN");
        dto.setItemCode("1002001");
        dto.setItemName("装配物料");
        dto.setItemQty(1);
        dto.setEntityName("ENTITY001");
        dto.setParentItemBarcode("PARENT001");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的装配关系列表（用于getAllWsmAssembleLines测试）
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockWsmAssembleLinesList() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();

        ECWsmAssembleLinesEntityWithNameDTO dto1 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setItemCode("1001001");  // 以1开头，需要展开
        dto1.setItemName("测试物料1");
        list.add(dto1);

        ECWsmAssembleLinesEntityWithNameDTO dto2 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setItemCode("MODULE001");  // 机框模组代码，需要展开
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的装配关系列表（无需展开的条码）
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockWsmAssembleLinesListNoExpandable() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();

        ECWsmAssembleLinesEntityWithNameDTO dto1 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setItemCode("2001001");  // 不以1开头，也不是机框模组代码
        dto1.setItemName("测试物料1");
        list.add(dto1);

        ECWsmAssembleLinesEntityWithNameDTO dto2 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setItemCode("3001001");  // 不以1开头，也不是机框模组代码
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的第2层查询结果
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockLevel2Results() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("LEVEL2_001");
        dto.setItemCode("1003001");  // 以1开头，继续展开
        dto.setItemName("第2层物料");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的第3层查询结果
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockLevel3Results() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("LEVEL3_001");
        dto.setItemCode("2003001");  // 不以1开头，不再展开
        dto.setItemName("第3层物料");
        list.add(dto);
        return list;
    }
}
