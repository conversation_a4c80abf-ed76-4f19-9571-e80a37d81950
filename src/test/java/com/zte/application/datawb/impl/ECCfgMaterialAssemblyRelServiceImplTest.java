package com.zte.application.datawb.impl;

import com.zte.application.datawb.BarcodeContractService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * ECCfgMaterialAssemblyRelServiceImpl单元测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-05
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CollectionUtils.class, CommonUtils.class})
public class ECCfgMaterialAssemblyRelServiceImplTest {

    @InjectMocks
    private ECCfgMaterialAssemblyRelServiceImpl ecCfgMaterialAssemblyRelService;

    @Mock
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Mock
    private BarcodeContractService barcodeContractService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getAssemblyRelationList方法 - 成功场景
     * 验证正常流程下的数据处理和转换
     */
    @Test
    public void testGetAssemblyRelationList_Success() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001", "SERVER002");

        // Mock数据准备
        List<ECCpmConfigItemAssembleDTO> cpmConfigList1 = createMockCpmConfigItemAssembleList("1001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList2 = createMockCpmConfigItemAssembleList("1002");
        List<ECCpmConfigItemAssembleDTO> configDetailList = createMockConfigDetailList();
        List<ECMaterialAssemblyDTO> contractInfoList = createMockContractInfoList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> assemblyMaterialList = createMockAssemblyMaterialList();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList1);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1002))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(assemblyMaterialList);
        when(barcodeContractService.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(contractInfoList);

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为1", 1, result.size());

        ECMaterialAssemblyDTO firstResult = result.get(0);
        Assert.assertEquals("服务器SN应匹配", "SERVER001", firstResult.getServerSn());
        Assert.assertEquals("合同号应匹配", "CONTRACT001", firstResult.getContractNumber());
        Assert.assertEquals("任务号应匹配", "ENTITY001", firstResult.getEntityName());
        Assert.assertEquals("组织ID应匹配", "ORG001", firstResult.getOrgId());
        Assert.assertNotNull("装配列表不应为null", firstResult.getAssembleList());
    }

    /**
     * 测试getAssemblyRelationList方法 - 空输入列表
     * 验证空输入的处理
     */
    @Test
    public void testGetAssemblyRelationList_EmptyInput() {
        // Given
        List<String> serverSnList = new ArrayList<>();

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAssemblyRelationList方法 - 第一次查询返回空
     * 验证ECCpmConfigItemAssembleDTOList为空时的分支
     */
    @Test
    public void testGetAssemblyRelationList_FirstQueryEmpty() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");

        // Mock第一次查询返回空
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(new ArrayList<>());

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAssemblyRelationList方法 - 第二次查询返回空
     * 验证configDetailDTOList为空时的分支
     */
    @Test
    public void testGetAssemblyRelationList_SecondQueryEmpty() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleList("1001");

        // Mock第一次查询有结果，第二次查询返回空
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(new ArrayList<>());

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getAssemblyRelationList方法 - ParentRecordId校验异常
     * 验证当ParentRecordId不为null且不为0时抛出异常
     */
    @Test(expected = MesBusinessException.class)
    public void testGetAssemblyRelationList_ParentRecordIdValidationException() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleListWithParentRecordId("1001", 1);

        // Mock静态方法
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(eq(MessageId.SERVER_SN_NOT_IN_FIRST_TWO_LAYERS), any(String[].class)))
            .thenReturn("输入的SERVER_SN在配置物料绑定关系中不属于前两层，SN=BARCODE001");

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);

        // When & Then - 期望抛出异常
        ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);
    }

    /**
     * 测试getAssemblyRelationList方法 - ParentRecordId为0的正常场景
     * 验证当ParentRecordId为0时不抛出异常，正常处理
     */
    @Test
    public void testGetAssemblyRelationList_ParentRecordIdZero() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleListWithParentRecordId("1001", 0);
        List<ECCpmConfigItemAssembleDTO> configDetailList = createMockConfigDetailList();
        List<ECMaterialAssemblyDTO> contractInfoList = createMockContractInfoList();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(barcodeContractService.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(contractInfoList);

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为1", 1, result.size());
    }

    /**
     * 测试getAssemblyRelationList方法 - ParentRecordId为null的正常场景
     * 验证当ParentRecordId为null时不抛出异常，正常处理
     */
    @Test
    public void testGetAssemblyRelationList_ParentRecordIdNull() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleListWithParentRecordId("1001", null);
        List<ECCpmConfigItemAssembleDTO> configDetailList = createMockConfigDetailList();
        List<ECMaterialAssemblyDTO> contractInfoList = createMockContractInfoList();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(1001))
            .thenReturn(configDetailList);
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(barcodeContractService.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(contractInfoList);

        // When
        List<ECMaterialAssemblyDTO> result = ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为1", 1, result.size());
    }

    /**
     * 测试getAssemblyRelationList方法 - 验证异常消息内容
     * 验证异常消息是否正确包含SN参数
     */
    @Test
    public void testGetAssemblyRelationList_ExceptionMessage() {
        // Given
        List<String> serverSnList = Arrays.asList("SERVER001");
        List<ECCpmConfigItemAssembleDTO> cpmConfigList = createMockCpmConfigItemAssembleListWithParentRecordId("1001", 1);
        String expectedMessage = "输入的SERVER_SN在配置物料绑定关系中不属于前两层，SN=BARCODE001";

        // Mock静态方法
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(eq(MessageId.SERVER_SN_NOT_IN_FIRST_TWO_LAYERS), any(String[].class)))
            .thenReturn(expectedMessage);

        // Mock方法调用
        when(wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList))
            .thenReturn(cpmConfigList);

        try {
            // When
            ecCfgMaterialAssemblyRelService.getAssemblyRelationList(serverSnList);
            Assert.fail("应该抛出异常");
        } catch (MesBusinessException e) {
            // Then
            Assert.assertEquals("异常代码应匹配", RetCode.BUSINESSERROR_CODE, e.getExCode());
            Assert.assertEquals("异常消息应匹配", expectedMessage, e.getMessage());
        }
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 成功场景
     * 验证四层递归查询的正常流程
     */
    @Test
    public void testGetAllWsmAssembleLines_Success() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> level2Results = createMockLevel2Results();
        List<ECWsmAssembleLinesEntityWithNameDTO> level3Results = createMockLevel3Results();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(level2Results)
            .thenReturn(level3Results)
            .thenReturn(new ArrayList<>());  // 第4层返回空，提前退出

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertNotNull("列表不应为null", wsmAssembleLinesList);
        // 验证列表被添加了新的元素（原始 + level2 + level3）
        Assert.assertTrue("列表应包含更多元素", wsmAssembleLinesList.size() > 2);
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 第二层查询返回空
     * 验证提前退出的分支
     */
    @Test
    public void testGetAllWsmAssembleLines_Level2Empty() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(new ArrayList<>());  // 第2层就返回空

        int originalSize = wsmAssembleLinesList.size();

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertEquals("列表大小不应改变", originalSize, wsmAssembleLinesList.size());
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 无需展开的条码
     * 验证当没有以"1"开头的物料代码和机框模组代码时的分支
     */
    @Test
    public void testGetAllWsmAssembleLines_NoExpandableBarcodes() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesListNoExpandable();
        List<SysLookupValues> lookupValues = createMockLookupValues();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);

        int originalSize = wsmAssembleLinesList.size();

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertEquals("列表大小不应改变", originalSize, wsmAssembleLinesList.size());
    }

    /**
     * 测试getAllWsmAssembleLines方法 - 达到最大层数
     * 验证查询到第4层的完整流程
     */
    @Test
    public void testGetAllWsmAssembleLines_MaxLevels() {
        // Given
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = createMockWsmAssembleLinesList();
        List<SysLookupValues> lookupValues = createMockLookupValues();
        List<ECWsmAssembleLinesEntityWithNameDTO> levelResults = createMockLevel2Results();

        // Mock方法调用
        when(wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE))
            .thenReturn(lookupValues);
        when(wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(anyList()))
            .thenReturn(levelResults);  // 每层都返回结果，直到第4层

        // When
        ecCfgMaterialAssemblyRelService.getAllWsmAssembleLines(wsmAssembleLinesList);

        // Then
        Assert.assertNotNull("列表不应为null", wsmAssembleLinesList);
        // 验证经过4层查询后列表被扩展
        Assert.assertTrue("列表应包含更多元素", wsmAssembleLinesList.size() > 2);
    }

    // ==================== Mock数据创建方法 ====================

    /**
     * 创建Mock的ECCpmConfigItemAssembleDTO列表
     */
    private List<ECCpmConfigItemAssembleDTO> createMockCpmConfigItemAssembleList(String entityId) {
        List<ECCpmConfigItemAssembleDTO> list = new ArrayList<>();
        ECCpmConfigItemAssembleDTO dto = new ECCpmConfigItemAssembleDTO();
        dto.setEntityId(entityId);
        dto.setItemBarcode("BARCODE001");
        dto.setBarcodeQty("1");
        dto.setItemCode("1001001");
        dto.setBarcodeType("SN");
        dto.setItemName("测试物料");
        dto.setParentRecordId(0);  // 新增：设置为0，表示正常情况
        list.add(dto);
        return list;
    }

    /**
     * 创建带有ParentRecordId的Mock ECCpmConfigItemAssembleDTO列表
     */
    private List<ECCpmConfigItemAssembleDTO> createMockCpmConfigItemAssembleListWithParentRecordId(String entityId, Integer parentRecordId) {
        List<ECCpmConfigItemAssembleDTO> list = new ArrayList<>();
        ECCpmConfigItemAssembleDTO dto = new ECCpmConfigItemAssembleDTO();
        dto.setEntityId(entityId);
        dto.setItemBarcode("BARCODE001");
        dto.setBarcodeQty("1");
        dto.setItemCode("1001001");
        dto.setBarcodeType("SN");
        dto.setItemName("测试物料");
        dto.setParentRecordId(parentRecordId);  // 设置ParentRecordId
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的配置详情列表
     */
    private List<ECCpmConfigItemAssembleDTO> createMockConfigDetailList() {
        List<ECCpmConfigItemAssembleDTO> list = new ArrayList<>();

        ECCpmConfigItemAssembleDTO dto1 = new ECCpmConfigItemAssembleDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setBarcodeQty("1");
        dto1.setItemCode("1001001");  // 以1开头
        dto1.setBarcodeType("SN");
        dto1.setItemName("测试物料1");
        list.add(dto1);

        ECCpmConfigItemAssembleDTO dto2 = new ECCpmConfigItemAssembleDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setBarcodeQty("2");
        dto2.setItemCode("2001001");  // 不以1开头
        dto2.setBarcodeType("PN");
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的合同信息列表
     */
    private List<ECMaterialAssemblyDTO> createMockContractInfoList() {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();
        ECMaterialAssemblyDTO dto = new ECMaterialAssemblyDTO();
        dto.setContractNumber("CONTRACT001");
        dto.setEntityName("ENTITY001");
        dto.setOrgId("ORG001");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的数据字典值列表
     */
    private List<SysLookupValues> createMockLookupValues() {
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues lookup = new SysLookupValues();
        lookup.setDescription("MODULE001");  // 机框模组物料代码
        list.add(lookup);
        return list;
    }

    /**
     * 创建Mock的装配物料列表
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockAssemblyMaterialList() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("ASSEMBLY001");
        dto.setScanType("SN");
        dto.setItemCode("1002001");
        dto.setItemName("装配物料");
        dto.setItemQty(1);
        dto.setEntityName("ENTITY001");
        dto.setParentItemBarcode("PARENT001");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的装配关系列表（用于getAllWsmAssembleLines测试）
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockWsmAssembleLinesList() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();

        ECWsmAssembleLinesEntityWithNameDTO dto1 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setItemCode("1001001");  // 以1开头，需要展开
        dto1.setItemName("测试物料1");
        list.add(dto1);

        ECWsmAssembleLinesEntityWithNameDTO dto2 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setItemCode("MODULE001");  // 机框模组代码，需要展开
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的装配关系列表（无需展开的条码）
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockWsmAssembleLinesListNoExpandable() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();

        ECWsmAssembleLinesEntityWithNameDTO dto1 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto1.setItemBarcode("BARCODE001");
        dto1.setItemCode("2001001");  // 不以1开头，也不是机框模组代码
        dto1.setItemName("测试物料1");
        list.add(dto1);

        ECWsmAssembleLinesEntityWithNameDTO dto2 = new ECWsmAssembleLinesEntityWithNameDTO();
        dto2.setItemBarcode("BARCODE002");
        dto2.setItemCode("3001001");  // 不以1开头，也不是机框模组代码
        dto2.setItemName("测试物料2");
        list.add(dto2);

        return list;
    }

    /**
     * 创建Mock的第2层查询结果
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockLevel2Results() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("LEVEL2_001");
        dto.setItemCode("1003001");  // 以1开头，继续展开
        dto.setItemName("第2层物料");
        list.add(dto);
        return list;
    }

    /**
     * 创建Mock的第3层查询结果
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> createMockLevel3Results() {
        List<ECWsmAssembleLinesEntityWithNameDTO> list = new ArrayList<>();
        ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
        dto.setItemBarcode("LEVEL3_001");
        dto.setItemCode("2003001");  // 不以1开头，不再展开
        dto.setItemName("第3层物料");
        list.add(dto);
        return list;
    }
}
