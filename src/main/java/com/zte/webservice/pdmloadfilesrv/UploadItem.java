package com.zte.webservice.pdmloadfilesrv;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/4/20
 */
public class UploadItem implements Serializable {
    /**
     * 序号
     */
    private int serial_id;
    /**
     * 文件名称
     */
    private String doc_name;
    /**
     * 格式
     */
    private String doc_format;
    /**
     * 大小
     */
    private int file_size;
    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attach_date;
    /**
     * 改变名称
     */
    private String change_name;
    /**
     * 上传IP
     */
    private String upload_server;
    /**
     * 上传路径
     */
    private String upload_path;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date last_updated_date;
    /**
     * 最后更新人工号
     */
    private String last_updated_by;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creation_date;
    /**
     * 创建人
     */
    private String creation_by;
    /**
     * 路径
     */
    private String path;
    /**
     * 备注
     */
    private String memo;

    /**
     * 临时文件名
     */
    private String fileName;
    /**
     * 后缀名
     */
    private String extendFileName;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件全路径
     */
    private String fileFullPath;

    /**
     *
     */
    private String dme_id;
    /**
     *
     */
    private String ecs_key;
    /**
     * 上传错误信息
     */
    private String errormsg;


    public int getSerial_id() {
        return serial_id;
    }

    public void setSerial_id(int serial_id) {
        this.serial_id = serial_id;
    }

    public String getDoc_name() {
        return doc_name;
    }

    public void setDoc_name(String doc_name) {
        this.doc_name = doc_name;
    }

    public String getDoc_format() {
        return doc_format;
    }

    public void setDoc_format(String doc_format) {
        this.doc_format = doc_format;
    }

    public int getFile_size() {
        return file_size;
    }

    public void setFile_size(int file_size) {
        this.file_size = file_size;
    }

    public Date getAttach_date() {
        return attach_date;
    }

    public void setAttach_date(Date attach_date) {
        this.attach_date = attach_date;
    }

    public String getChange_name() {
        return change_name;
    }

    public void setChange_name(String change_name) {
        this.change_name = change_name;
    }

    public String getUpload_server() {
        return upload_server;
    }

    public void setUpload_server(String upload_server) {
        this.upload_server = upload_server;
    }

    public String getUpload_path() {
        return upload_path;
    }

    public void setUpload_path(String upload_path) {
        this.upload_path = upload_path;
    }

    public Date getLast_updated_date() {
        return last_updated_date;
    }

    public void setLast_updated_date(Date last_updated_date) {
        this.last_updated_date = last_updated_date;
    }

    public String getLast_updated_by() {
        return last_updated_by;
    }

    public void setLast_updated_by(String last_updated_by) {
        this.last_updated_by = last_updated_by;
    }

    public Date getCreation_date() {
        return creation_date;
    }

    public void setCreation_date(Date creation_date) {
        this.creation_date = creation_date;
    }

    public String getCreation_by() {
        return creation_by;
    }

    public void setCreation_by(String creation_by) {
        this.creation_by = creation_by;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getExtendFileName() {
        return extendFileName;
    }

    public void setExtendFileName(String extendFileName) {
        this.extendFileName = extendFileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileFullPath() {
        return fileFullPath;
    }

    public void setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
    }

    public String getDme_id() {
        return dme_id;
    }

    public void setDme_id(String dme_id) {
        this.dme_id = dme_id;
    }

    public String getEcs_key() {
        return ecs_key;
    }

    public void setEcs_key(String ecs_key) {
        this.ecs_key = ecs_key;
    }

    public String getErrormsg() {
        return errormsg;
    }

    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }
}
