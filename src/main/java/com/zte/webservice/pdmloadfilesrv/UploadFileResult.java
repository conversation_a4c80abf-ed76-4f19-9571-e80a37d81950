package com.zte.webservice.pdmloadfilesrv;



import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/4/20
 */
public class UploadFileResult implements Serializable {
    private String process_Status;
    private UploadItem uploadItem;
    private String process_Message;
    private String instance_Id;


    public String getProcess_Status() {
        return process_Status;
    }

    public void setProcess_Status(String processStatus) {
        this.process_Status = processStatus;
    }

    public UploadItem getUploadItem() {
        return uploadItem;
    }

    public void setUploadItem(UploadItem uploadItem) {
        this.uploadItem = uploadItem;
    }

    public String getProcess_Message() {
        return process_Message;
    }

    public void setProcess_Message(String processMessage) {
        this.process_Message = processMessage;
    }

    public String getInstance_Id() {
        return instance_Id;
    }

    public void setInstance_Id(String instanceId) {
        this.instance_Id = instanceId;
    }


}
