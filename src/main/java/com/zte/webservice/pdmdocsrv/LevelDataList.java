package com.zte.webservice.pdmdocsrv;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/17 19:58
 */
public class LevelDataList implements Serializable {
    private List<DdlLevelModel> ddlDataModel;

    public List<DdlLevelModel> getDdlDataModel() {
        return ddlDataModel;
    }

    public void setDdlDataModel(List<DdlLevelModel> ddlDataModel) {
        this.ddlDataModel = ddlDataModel;
    }

    public void addDdlDataModel(DdlLevelModel model) {
        if (this.ddlDataModel == null) {
            this.ddlDataModel = new ArrayList<>();
        }
        this.ddlDataModel.add(model);
    }

}
