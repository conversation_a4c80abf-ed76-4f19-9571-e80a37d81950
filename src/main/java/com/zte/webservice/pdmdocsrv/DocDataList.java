package com.zte.webservice.pdmdocsrv;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/17 20:35
 */
public class DocDataList implements Serializable {
    public List<DdlDocModel> getDdlDataModel() {
        return ddlDataModel;
    }

    public void setDdlDataModel(List<DdlDocModel> ddlDataModel) {
        this.ddlDataModel = ddlDataModel;
    }

    public void addDdlDataModel(DdlDocModel ddlDataModel) {
        if(this.ddlDataModel==null){
            this.ddlDataModel = new ArrayList<>();
        }
        this.ddlDataModel.add(ddlDataModel) ;
    }

    private List<DdlDocModel> ddlDataModel;
}
