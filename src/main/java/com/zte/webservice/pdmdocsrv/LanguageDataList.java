package com.zte.webservice.pdmdocsrv;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/4/17 20:29
 */
public class LanguageDataList implements Serializable {
    private List<DdlLanguageModel> ddlDataModel;

    public List<DdlLanguageModel> getDdlDataModel() {
        return ddlDataModel;
    }

    public void setDdlDataModel(List<DdlLanguageModel> ddlDataModel) {
        this.ddlDataModel = ddlDataModel;
    }

    public void addDdlDataModel(DdlLanguageModel ddlDataModel) {
        if(this.ddlDataModel==null){
            this.ddlDataModel =new ArrayList<>();
        }
        this.ddlDataModel.add(ddlDataModel) ;
    }


}
