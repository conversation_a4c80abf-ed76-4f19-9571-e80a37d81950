/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-07-04
 * 修改历史 :
 * 1. [2019-07-04] 创建文件 by 10229661
 **/
package com.zte.application.wmsdt.impl;

import com.zte.application.wmsdt.ZyBoxupCbomService;
import com.zte.domain.model.wmsdt.ZyBoxupCbom;
import com.zte.domain.model.wmsdt.ZyBoxupCbomRepository;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.WMSPRODLMS)
public class ZyBoxupCbomServiceImpl implements ZyBoxupCbomService {

    @Autowired
    private ZyBoxupCbomRepository zyBoxupCbomRepository;

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertZyBoxupCbom(ZyBoxupCbom record) {

        zyBoxupCbomRepository.insertZyBoxupCbom(record);
    }

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertZyBoxupCbomSelective(ZyBoxupCbom record) {

        zyBoxupCbomRepository.insertZyBoxupCbomSelective(record);
    }

    /**
     * get all record
     * 
     * @return List<ZyBoxupCbom>
     **/
    @Override
    public java.util.List<ZyBoxupCbom> selectListbyBillnumbers(Map billnumbers) {

        return zyBoxupCbomRepository.selectListbyBillnumbers(billnumbers);
    }
}
