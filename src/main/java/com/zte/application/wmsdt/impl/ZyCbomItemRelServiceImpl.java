/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-07-11
 * 修改历史 :
 * 1. [2019-07-11] 创建文件 by 10229661
 **/
package com.zte.application.wmsdt.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.wmsdt.ZyCbomItemRelService;
import com.zte.domain.model.wmsdt.ZyCbomItemRel;
import com.zte.domain.model.wmsdt.ZyCbomItemRelRepository;
import com.zte.interfaces.wmsdt.dto.ZyCbomItemRelDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.WMSPRODLMS)
public class ZyCbomItemRelServiceImpl implements ZyCbomItemRelService {

    @Autowired
    private ZyCbomItemRelRepository zyCbomItemRelRepository;

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertZyCbomItemRel(ZyCbomItemRel record) {

        zyCbomItemRelRepository.insertZyCbomItemRel(record);
    }

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    @Override
    public void insertZyCbomItemRelSelective(ZyCbomItemRel record) {

        zyCbomItemRelRepository.insertZyCbomItemRelSelective(record);
    }

    /**
     * 增加实体数据
     * 
     * @param record
     * @return List<ZyCbomItemRel>
     **/
    @Override
    public java.util.List<ZyCbomItemRel> selectZyCbomItemList(ZyCbomItemRelDTO record) {

        return zyCbomItemRelRepository.selectZyCbomItemList(record);
    }
}
