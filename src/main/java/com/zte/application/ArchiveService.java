package com.zte.application;

import com.zte.interfaces.dto.CallbackArchiveFileReq;
import com.zte.domain.model.ArchiveTaskSend;


/**
 * <p>
 *  归档任务实现接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface ArchiveService {
    /**
     * 批量归档
     */
    void sendArchiveTask(String taskType);

    /**
     * 单个归档
     * @param taskSendArchive
     */
    void sendSignArchiveTask(ArchiveTaskSend taskSendArchive);

    /**
     * 归档回调
     *
     * @param req
     */
    void receiveCallback(CallbackArchiveFileReq req);

    /**
     * 归档文件清理
     */
    void archiveClearFile(String filePath);

    /**
     * 根据id查询归档任务
     * @param taskId 任务id
     * @return
     */
    ArchiveTaskSend selectById(String taskId);

}
