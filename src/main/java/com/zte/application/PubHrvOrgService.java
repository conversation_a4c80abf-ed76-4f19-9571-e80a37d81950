package com.zte.application;

import com.zte.domain.model.PubHrvOrg;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public interface PubHrvOrgService {

    /**
     *  根据用户ID获取部门信息
     * @return
     */
    PubHrvOrg getPubHrvOrgByUserNameId(@Param("userNameId") String userNameId);

    PubHrvOrg getPubHrvOrgByUserNamePlus(@Param("userName") String userName);
}
