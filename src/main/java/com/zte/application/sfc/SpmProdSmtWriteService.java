package com.zte.application.sfc;

import com.zte.interfaces.dto.kxbariii.ProdSmtWriteDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-14 16:35
 */
public interface SpmProdSmtWriteService {

    /**
     * 批量新增或更新料单级前加工数据
     *
     * @param list 新增参数
     */
    void insertPreBomBatch(List<ProdSmtWriteDTO> list) throws MesBusinessException;

    /**
     * 批量新增或更新物料级前加工数据
     *
     * @param list 新增参数
     */
    void insertPreItemBatch(List<ProdSmtWriteDTO> list) throws MesBusinessException;

    /**
     * 料单级前加工维护页面调用
     *
     * @param list 新增或者更新
     */
    void insertOrUpdateBomPre(ProdSmtWriteDTO updateData) throws MesBusinessException;

    /**
     * 分页料单级前加工查询
     *
     * @param page 分页查询数据
     */
    Page<ProdSmtWriteDTO> selectPreBomPage(Page<ProdSmtWriteDTO> page);

    /**
     * 物料查询数据
     *
     * @param page 分页物料查询数据
     */
    Page<ProdSmtWriteDTO> selectPreItemPage(Page<ProdSmtWriteDTO> page);

    /**
     * 根据物料代码删除
     *
     * @param prodSmtWriteDTO 请求参数
     */
    void deletePreItemByItemNoList(ProdSmtWriteDTO prodSmtWriteDTO);

    /**
     * 根据料单代码删除,物料代码
     *
     * @param prodSmtWriteDTO 请求参数
     */
    void deletePreBomByBomList(ProdSmtWriteDTO prodSmtWriteDTO);


}
