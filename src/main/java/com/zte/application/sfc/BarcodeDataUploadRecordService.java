package com.zte.application.sfc;

import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * 服务器SN数据上传记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24 15:53:30
 */
public interface BarcodeDataUploadRecordService {

    void batchInsertOrUpdate(List<BarcodeDataUploadRecordEntityDTO> list);

    List<BarcodeDataUploadRecordEntityDTO> getListByTaskNoList(List<String> list);

    void batchUpdateStatus(List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList);


}

