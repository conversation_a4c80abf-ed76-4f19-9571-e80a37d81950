package com.zte.application.sfc;

import com.zte.interfaces.dto.sfc.MtnRepairLinesDTO;
import com.zte.interfaces.dto.sfc.PmRepairInfoStatDTO;
import java.util.List;

public interface MtnRepairLinesService {

    /**
     * 根据维修小类统计维修信息
     * @param itemCode 12位料单代码
     * @param userFaultDesc 故障描述
     * @return
     */
    List<PmRepairInfoStatDTO> statByTroubleSmallCode(String itemCode, String userFaultDesc);

    /**
     * 根据维修小类和位号统计维修信息
     * @param itemCode 12位料单代码
     * @param userFaultDesc 故障描述
     * @return
     */
    List<PmRepairInfoStatDTO> statByTroubleSmallCodeAndSiteNo(String itemCode, String userFaultDesc);


    MtnRepairLinesDTO getLastRepairByItemBarcode(String itemBarcode);


}
