package com.zte.application.sfc.impl;

import com.google.common.collect.Lists;
import com.zte.application.sfc.BarcodeDataUploadRecordService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.sfc.BarcodeDataUploadRecordRepository;
import com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO;
import com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("barcodeDataUploadRecordService")
@DataSource(value = DatabaseType.WMES)
public class BarcodeDataUploadRecordServiceImpl implements BarcodeDataUploadRecordService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BarcodeDataUploadRecordRepository barcodeDataUploadRecordRepository;

    @Override
    public void batchUpdateStatus(List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList) {
        barcodeDataUploadRecordRepository.batchUpdateStatus(barcodeDataUploadRecordEntityDTOList);
    }

    /* Started by AICoder, pid:t7eb0kf7cew9b2c14a67086360e93117d0f912db */
    @Override
    public List<BarcodeDataUploadRecordEntityDTO> getListByTaskNoList(List<String> taskNoList) {
        // 使用ArrayList并指定初始容量以提高性能
        List<BarcodeDataUploadRecordEntityDTO> result = new ArrayList<>(taskNoList.size());

        if (CollectionUtils.isEmpty(taskNoList)) {
            return result;
        }

        // 分批处理任务编号列表，每批最多100个
        for (List<String> batch : Lists.partition(taskNoList, NumConstant.NUM_100)) {
            List<BarcodeDataUploadRecordEntityDTO> batchResults = barcodeDataUploadRecordRepository.getListByTaskNoList(batch);
            if (!CollectionUtils.isEmpty(batchResults)) {
                result.addAll(batchResults);
            }
        }

        return result;
    }

    /* Ended by AICoder, pid:t7eb0kf7cew9b2c14a67086360e93117d0f912db */


    /* Started by AICoder, pid:icd0cabfd0r52a0140840816c0d635161576c96c */
    @Override
    public void batchInsertOrUpdate(List<BarcodeDataUploadRecordEntityDTO> records) {
        // 早期返回，避免不必要的操作
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 使用常量定义批处理大小，提高代码可读性和可维护性
        final int batchSize = NumConstant.NUM_100;
        Map<String, BarcodeDataUploadRecordEntityDTO> map =  records.stream().collect(Collectors.toMap(e->(e.getEntityName()+ Constant.UNDER_LINE +e.getServerSn()+Constant.UNDER_LINE +e.getDataType()), a -> a, (k1, k2) -> k1));

        records = map.values().stream()
                .collect(Collectors.toList());
        // 使用更简洁的分页逻辑
        for (int i = 0; i < records.size(); i += batchSize) {
            List<BarcodeDataUploadRecordEntityDTO> batch = records.subList(i, Math.min(i + batchSize, records.size()));
            barcodeDataUploadRecordRepository.batchInsertOrUpdate(batch);
        }
    }

    /* Ended by AICoder, pid:icd0cabfd0r52a0140840816c0d635161576c96c */


}