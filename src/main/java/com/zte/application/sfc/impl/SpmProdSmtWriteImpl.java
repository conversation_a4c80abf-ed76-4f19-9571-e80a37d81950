package com.zte.application.sfc.impl;

import com.zte.application.sfc.SpmProdSmtWriteService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.sfc.SpmProdSmtWriteRepository;
import com.zte.interfaces.dto.kxbariii.ProdSmtWriteDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.RedisLockParamAnnotation;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-14 16:35
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class SpmProdSmtWriteImpl implements SpmProdSmtWriteService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private SpmProdSmtWriteRepository spmProdSmtWriteRepository;

    /**
     * 批量新增或更新料单级前加工数据
     *
     * @param list 新增参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPreBomBatch(List<ProdSmtWriteDTO> list) throws MesBusinessException {
        // 1. 设置料单锁
        List<String> bomList = list.stream().map(ProdSmtWriteDTO::getBomNo).distinct().collect(Collectors.toList());
        List<String> redisKeyList = new LinkedList<>();
        // 处理默认值
        this.dealDefaultValue(list);
        try {
            // 按料单加锁
            this.batchLockRedisKey(bomList, redisKeyList, Constant.PRE_BOM_REDIS_KEY);
            // 2. 查询料单前加工现有数据
            List<ProdSmtWriteDTO> existSmtList = this.getProdSmtWriteDTOS(bomList, Boolean.TRUE);
            // 3. 不存在新增料单级记录,并移除新增数据
            this.insertBatchThenRemove(list, existSmtList, Boolean.TRUE);
            // 4. 存在则更新对应的记录行
            this.updateBatch(list, Boolean.TRUE);
        } finally {
            if (CollectionUtils.isNotEmpty(redisKeyList)) {
                redisTemplate.delete(redisKeyList);
            }
        }
    }

    private void dealDefaultValue(List<ProdSmtWriteDTO> list) {
        list.stream().forEach(item -> {
            if (Objects.isNull(item.getIsMolding())) {
                item.setIsMolding(Constant.INT_0);
            }
            if (Objects.isNull(item.getIsWritetablet())) {
                item.setIsWritetablet(Constant.INT_0);
            }
            if (Objects.isNull(item.getIsRoast())) {
                item.setIsRoast(Constant.INT_0);
            }
        });
    }

    /**
     * 存在更新对应行
     *
     * @param list    更新行
     * @param bomFlag 更新料单级标识
     */
    private void updateBatch(List<ProdSmtWriteDTO> list, Boolean bomFlag) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<List<ProdSmtWriteDTO>> splitList = CommonUtils.splitList(list, Constant.INT_50);
            for (List<ProdSmtWriteDTO> updateList : splitList) {
                if (Boolean.TRUE.equals(bomFlag)) {
                    spmProdSmtWriteRepository.updatePreBomBatch(updateList);
                } else {
                    spmProdSmtWriteRepository.updatePreItemBatch(updateList);
                }
            }
        }
    }

    /**
     * 按料单加锁
     *
     * @param bomList        料单代码
     * @param redisKeyList   加锁集合
     * @param redisKeyFormat 加锁格式化
     * @throws MesBusinessException 业务异常报错
     */
    private void batchLockRedisKey(List<String> bomList, List<String> redisKeyList, String redisKeyFormat) throws MesBusinessException {
        for (String bomNo : bomList) {
            String bomRedisKey = String.format(redisKeyFormat, bomNo);
            Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(bomRedisKey, new Date(), Constant.INT_5, TimeUnit.MINUTES);
            if (Boolean.TRUE.equals(setIfAbsent)) {
                redisKeyList.add(bomRedisKey);
            } else {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.REDIS_LOCK_FAIL, new Object[]{bomRedisKey});
            }
        }
    }

    /**
     * 查询料单现有前加工数据
     *
     * @param bomList 料单集合
     * @param bomFlag 料单前加工标识
     * @return 料单前加工数据
     */
    private List<ProdSmtWriteDTO> getProdSmtWriteDTOS(List<String> bomList, Boolean bomFlag) {
        List<List<String>> splitList = CommonUtils.splitList(bomList, Constant.BATCH_QUERY_SIZE);
        List<ProdSmtWriteDTO> existSmtList = new LinkedList<>();
        List<ProdSmtWriteDTO> writeDTOList;
        for (List<String> list : splitList) {
            if (Boolean.TRUE.equals(bomFlag)) {
                writeDTOList = spmProdSmtWriteRepository.selectPreBomBatch(list);
            } else {
                writeDTOList = spmProdSmtWriteRepository.selectPreItemBatch(list);
            }
            if (CollectionUtils.isNotEmpty(writeDTOList)) {
                existSmtList.addAll(writeDTOList);
            }
        }
        return existSmtList;
    }

    /**
     * 存在新增料单级记录,并移除新增数据
     *
     * @param list         新增料单集合
     * @param existSmtList 存在料单数据
     * @param bomFlag      料单前加工数据
     */
    private void insertBatchThenRemove(List<ProdSmtWriteDTO> list, List<ProdSmtWriteDTO> existSmtList,
                                       Boolean bomFlag) {
        Map<String, List<ProdSmtWriteDTO>> existMap = existSmtList.stream()
                .collect(Collectors.groupingBy(item -> bomFlag ? item.getBomNo() : null + item.getItemNo()));
        List<ProdSmtWriteDTO> insertList = list.stream()
                .filter(item -> !existMap.containsKey(bomFlag ? item.getBomNo() : null + item.getItemNo()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<ProdSmtWriteDTO>> insertResult = CommonUtils.splitList(insertList, Constant.INT_50);
            for (List<ProdSmtWriteDTO> writeDTOList : insertResult) {
                if (Boolean.TRUE.equals(bomFlag)) {
                    spmProdSmtWriteRepository.insertPreBomBatch(writeDTOList);
                } else {
                    spmProdSmtWriteRepository.insertPreItemBatch(writeDTOList);
                }
            }
        }
        list.removeAll(insertList);
    }

    /**
     * 批量新增或更新物料级前加工数据
     *
     * @param list 新增参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPreItemBatch(List<ProdSmtWriteDTO> list) throws MesBusinessException {
        List<String> itemList = list.stream().map(ProdSmtWriteDTO::getItemNo).distinct().collect(Collectors.toList());
        List<String> redisKeyList = new LinkedList<>();
        this.dealDefaultValue(list);
        try {
            // 1. 设置物料级锁
            this.batchLockRedisKey(itemList, redisKeyList, Constant.PRE_ITEM_REDIS_KEY);
            // 2. 查询料单前加工现有数据
            List<ProdSmtWriteDTO> existSmtList = this.getProdSmtWriteDTOS(itemList, Boolean.FALSE);
            // 3. 不存在新增料单级记录,并移除新增数据
            this.insertBatchThenRemove(list, existSmtList, Boolean.FALSE);
            // 4. 存在则更新对应的记录行
            this.updateBatch(list, Boolean.FALSE);
        } finally {
            if (CollectionUtils.isNotEmpty(redisKeyList)) {
                redisTemplate.delete(redisKeyList);
            }
        }
    }

    /**
     * 料单级前加工维护页面调用
     *
     * @param updateData
     */
    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "insertOrUpdateBomPre", redisLockParam = {
            @RedisLockParamAnnotation(paramName = "updateData", propertiesString = "bomNo")
    })
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateBomPre(ProdSmtWriteDTO updateData) throws MesBusinessException {
        if (StringUtils.isBlank(updateData.getBomNo())) {
            return;
        }
        // 1. 查询料单级前加工数据
        List<ProdSmtWriteDTO> writeDTOList = spmProdSmtWriteRepository.selectPreBomBatch(Arrays.asList(updateData.getBomNo()));
        if (Objects.isNull(writeDTOList)) {
            writeDTOList = new LinkedList<>();
        }
        List<ProdSmtWriteDTO> child = updateData.getChild();
        if (Objects.isNull(child)) {
            child = new LinkedList<>();
        }
        this.dealDefaultValue(child);
        List<String> existList = child.stream().map(ProdSmtWriteDTO::getItemNo).collect(Collectors.toList());
        List<ProdSmtWriteDTO> deleteList = writeDTOList.stream()
                .filter(item -> !existList.contains(item.getItemNo())).collect(Collectors.toList());
        List<String> baseData = writeDTOList.stream().map(ProdSmtWriteDTO::getItemNo).collect(Collectors.toList());

        // 2. 删除不存物料
        if (CollectionUtils.isNotEmpty(deleteList)) {
            ProdSmtWriteDTO deleteEntry = new ProdSmtWriteDTO();
            deleteEntry.setBomList(Arrays.asList(updateData.getBomNo()));
            List<String> itemList = deleteList.stream().map(ProdSmtWriteDTO::getItemNo)
                    .collect(Collectors.toList());
            deleteEntry.setItemList(itemList);
            spmProdSmtWriteRepository.deletePreBomByBomList(deleteEntry);
        }

        // 3. 更新数据
        List<ProdSmtWriteDTO> updateList = child.stream()
                .filter(item -> baseData.contains(item.getItemNo())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateList)) {
            spmProdSmtWriteRepository.updatePreBomBatch(updateList);
        }

        // 4. 新增数据
        child.removeIf(item -> baseData.contains(item.getItemNo()));
        if (CollectionUtils.isNotEmpty(child)) {
            spmProdSmtWriteRepository.insertPreBomBatch(child);
        }
    }

    /**
     * 分页料单级前加工查询
     *
     * @param page 分页查询数据
     */
    @Override
    public Page<ProdSmtWriteDTO> selectPreBomPage(Page<ProdSmtWriteDTO> page) {
        page.setRows(spmProdSmtWriteRepository.selectPreBomPage(page));
        return page;
    }

    /**
     * 物料查询数据
     *
     * @param page 分页物料查询数据
     */
    @Override
    public Page<ProdSmtWriteDTO> selectPreItemPage(Page<ProdSmtWriteDTO> page) {
        page.setRows(spmProdSmtWriteRepository.selectPreItemPage(page));
        return page;
    }

    /**
     * 根据物料代码删除
     *
     * @param prodSmtWriteDTO 请求参数
     */
    @Override
    public void deletePreItemByItemNoList(ProdSmtWriteDTO prodSmtWriteDTO) {
        if (CollectionUtils.isEmpty(prodSmtWriteDTO.getItemList())) {
            return;
        }
        spmProdSmtWriteRepository.deletePreItemByItemNoList(prodSmtWriteDTO);
    }

    /**
     * 根据料单代码删除,物料代码
     *
     * @param prodSmtWriteDTO 请求参数
     */
    @Override
    public void deletePreBomByBomList(ProdSmtWriteDTO prodSmtWriteDTO) {
        if (CollectionUtils.isEmpty(prodSmtWriteDTO.getBomList())) {
            return;
        }
        spmProdSmtWriteRepository.deletePreBomByBomList(prodSmtWriteDTO);
    }
}
