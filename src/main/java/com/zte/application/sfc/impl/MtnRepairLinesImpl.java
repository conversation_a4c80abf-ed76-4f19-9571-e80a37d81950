package com.zte.application.sfc.impl;

import com.zte.application.sfc.MtnRepairLinesService;
import com.zte.domain.model.sfc.MtnRepairLinesRepository;
import com.zte.interfaces.dto.sfc.MtnRepairLinesDTO;
import com.zte.interfaces.dto.sfc.PmRepairInfoStatDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-14 16:35
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class MtnRepairLinesImpl implements MtnRepairLinesService {
    @Autowired
    private MtnRepairLinesRepository mtnRepairLinesRepository;

    @Override
    public List<PmRepairInfoStatDTO> statByTroubleSmallCode(String itemCode, String userFaultDesc) {
        if (StringUtils.isBlank(itemCode) || StringUtils.isBlank(userFaultDesc)) {
            return new ArrayList<>();
        }
        return mtnRepairLinesRepository.statByTroubleSmallCode(itemCode, userFaultDesc);
    }

    @Override
    public List<PmRepairInfoStatDTO> statByTroubleSmallCodeAndSiteNo(String itemCode, String userFaultDesc) {
        if (StringUtils.isBlank(itemCode) || StringUtils.isBlank(userFaultDesc)) {
            return new ArrayList<>();
        }
        return mtnRepairLinesRepository.statByTroubleSmallCodeAndSiteNo(itemCode, userFaultDesc);
    }

    @Override
    public MtnRepairLinesDTO getLastRepairByItemBarcode(String itemBarcode) {
        if (StringUtils.isBlank(itemBarcode)) {
            return null;
        }
        return mtnRepairLinesRepository.getLastRepairByItemBarcode(itemBarcode);
    }
}
