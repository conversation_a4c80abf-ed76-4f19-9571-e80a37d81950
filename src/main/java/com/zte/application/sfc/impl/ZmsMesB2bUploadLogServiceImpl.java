package com.zte.application.sfc.impl;

import com.zte.application.sfc.ZmsMesB2bUploadLogService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.ZmsMesB2bUploadLogRepository;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-10 16:33
 */
@Service
@DataSource(DatabaseType.SFC)
public class ZmsMesB2bUploadLogServiceImpl implements ZmsMesB2bUploadLogService {
    @Autowired
    private ZmsMesB2bUploadLogRepository zmsMesB2bUploadLogRepository;

    /* Started by AICoder, pid:t774d764d8ve61f1495c096ec0c248214f51aadc */
    /**
     * 批量失效数据
     *
     * @param idList id 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteById(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<List<String>> splitLists = CommonUtils.splitList(idList, Constant.BATCH_QUERY_SIZE);
        for (List<String> sublist : splitLists) {
            zmsMesB2bUploadLogRepository.batchDeleteById(sublist);
        }
    }
    /* Ended by AICoder, pid:t774d764d8ve61f1495c096ec0c248214f51aadc */
}
