package com.zte.application.sfc.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.CpmContractEntitiesService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsForwardTencentService;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.sfc.BarcodeDataUploadRecordService;
import com.zte.application.sfc.WholeMachineUpTestRecordService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MptTestLog;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsForwardTencentRepository;
import com.zte.domain.model.sfc.WholeMachineUpTestRecordRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.CpmContractEntitiesDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationTestingInfoDTO;
import com.zte.interfaces.dto.MdsProblemsDTO;
import com.zte.interfaces.dto.MdsStationsDTO;
import com.zte.interfaces.dto.ZmsCbomInfoDTO;
import com.zte.interfaces.dto.ZmsExtendedAttributeDTO;
import com.zte.interfaces.dto.ZmsForwardTencentBatchRuleDTO;
import com.zte.interfaces.dto.ZmsForwardTencentInDTO;
import com.zte.interfaces.dto.ZmsInputDTO;
import com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO;
import com.zte.interfaces.dto.sfc.CpmContractEntitiesQueryDTO;
import com.zte.interfaces.dto.sfc.WholeMachineUpTestLogEntityDTO;
import com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.DATA;
import static com.zte.common.utils.Constant.INT_0;


@Service("wholeMachineUpTestRecordService")
@DataSource(DatabaseType.WMES)
public class WholeMachineUpTestRecordServiceImpl implements WholeMachineUpTestRecordService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WholeMachineUpTestRecordRepository wholeMachineUpTestRecordRepository;
    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Autowired
    private IMESLogService imesLogService;
    @Autowired
    private CpmContractEntitiesService cpmContractEntitiesService;
    @Autowired
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;
    @Autowired
    private MdsRemoteService mdsRemoteService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private BarcodeDataUploadRecordService barcodeDataUploadRecordService;
    @Autowired
    private ZmsForwardTencentService zmsForwardTencentService;
    @Autowired
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Value("${whole.machine.up.b2b.size:100}")
    private String b2bSize;
    @Value("${whole.machine.up.uploadPhase:0,1,3,4,5,6}")
    private String uploadPhase;
    @Value("${whole.machine.up.task.page.size:100}")
    private String pageSize;
    @Value("${tencent.mpt.station:MTHC}")
    private String mptStation;
    @Value("${whole.machine.up.task.tencentName:腾讯}")
    private String tencentName;
    @Autowired
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Autowired
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadService;


    /* Started by AICoder, pid:u5cd90fff7ja3f614623096f806c1e45182632d5 */
    /**
     * 更新上传记录状态
     *
     * @param customerDataLogDTO 上传日志数据传输对象
     * @ 异常处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RecordLogAnnotation("整机数据上传回写状态")
    public void updateUploadStatus(CustomerDataLogDTO customerDataLogDTO) throws JsonProcessingException {
        String status = customerDataLogDTO.getStatus();
        String json = customerDataLogDTO.getJsonData();
        // 检查状态和JSON数据是否为空
        if (StringUtils.isEmpty(json) || StringUtils.isEmpty(status)) {
            return;
        }
        JsonNode node = com.zte.springbootframe.util.json.JacksonJsonConverUtil.getMapperInstance().readTree(json);
        JsonNode jsonNode = node.get(DATA);
        List<WholeMachineUpTestRecordEntityDTO> records = new ArrayList<>();
        List<String> snList = new ArrayList<>();
        WholeMachineUpTestRecordEntityDTO updateDTO = new WholeMachineUpTestRecordEntityDTO();
        if (jsonNode != null) {
            json = jsonNode.toString();
            records = JSON.parseArray(json, WholeMachineUpTestRecordEntityDTO.class);
            updateDTO.setPhaseList(Constant.MACHINE_PHASE);
            // 提取唯一的Station ID列表
            List<String> stationIdList = records.stream()
                    .map(WholeMachineUpTestRecordEntityDTO::getStationId)
                    .distinct()
                    .collect(Collectors.toList());
            updateDTO.setStationIdList(stationIdList);
        } else {
            WholeMachineUpTestRecordEntityDTO recordEntityDTO = JSON.parseObject(json, WholeMachineUpTestRecordEntityDTO.class);
            snList.add(recordEntityDTO.getServerSn());
            records.add(recordEntityDTO);
            updateDTO.setIsMpt(Constant.FLAG_N);
            updateDTO.setTestStationList(new ArrayList(){{add(recordEntityDTO.getTestStation());}});
            updateDTO.setPhaseList(Constant.MPT_PHASE);
            updateDTO.setTestLogName(customerDataLogDTO.getFileData());
        }
        // 解析JSON数据为实体列表
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        updateDTO.setSnList(snList);
        updateDTO.setStatus(customerDataLogDTO.getStatus());
        // 更新测试记录的状态
        wholeMachineUpTestRecordRepository.updateStatus(updateDTO);
        String dataType = customerDataLogDTO.getDataType();
        String taskNo = customerDataLogDTO.getTaskNo();

        Integer count = wholeMachineUpTestRecordRepository.getCountByEntityName(taskNo);
        BarcodeDataUploadRecordEntityDTO barcodeRecord = new BarcodeDataUploadRecordEntityDTO();
        barcodeRecord.setEntityName(taskNo);
        barcodeRecord.setServerSn(records.get(NumConstant.NUM_ZERO).getServerSn());
        barcodeRecord.setDataType(dataType);
        if(count > NumConstant.NUM_ZERO){
            barcodeRecord.setDataUpStatus(NumConstant.STR_ONE_NEGATIVE);
        }else{
            barcodeRecord.setDataUpStatus(NumConstant.STR_ONE);
        }
        barcodeRecord.setLastUpdatedBy(customerDataLogDTO.getLastUpdatedBy());

        // 批量更新BarcodeDataUploadRecordEntityDTO的状态
        barcodeDataUploadRecordService.batchUpdateStatus(Arrays.asList(barcodeRecord));
    }
    /**
     * 更新上传记录状态
     *
     * @param customerDataLogDTO 上传日志数据传输对象
     * @ 异常处理
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMptUploadStatus(CustomerDataLogDTO customerDataLogDTO) {
        String status = customerDataLogDTO.getStatus();
        String json = customerDataLogDTO.getJsonData();
        // 检查状态和JSON数据是否为空
        if (StringUtils.isEmpty(json) || StringUtils.isEmpty(status)) {
            return;
        }
        // 解析JSON数据为实体列表
        MptTestLog record = JSON.parseObject(json, MptTestLog.class);
        if (record == null) {
            return;
        }
        // 提取唯一的Station ID列表
        WholeMachineUpTestRecordEntityDTO updateDTO = new WholeMachineUpTestRecordEntityDTO();
        List<String> snList = new ArrayList(){{add(record.getServerSn());}};
        updateDTO.setSnList(snList);
        updateDTO.setTestStationList(new ArrayList(){{add(mptStation);}});
        updateDTO.setPhaseList(Constant.MPT_PHASE);
        updateDTO.setStatus(customerDataLogDTO.getStatus());
        updateDTO.setTestLogName(customerDataLogDTO.getFileData());
        // 更新测试记录的状态
        wholeMachineUpTestRecordRepository.updateStatus(updateDTO);
        String dataType = cfgCodeRuleItemService.getSmallBoxSize(Constant.LOOKUP_CODE_MPT_DATA_TYPE);
        // 构建BarcodeDataUploadRecordEntityDTO列表
        BarcodeDataUploadRecordEntityDTO barcodeRecord = new BarcodeDataUploadRecordEntityDTO();
        barcodeRecord.setServerSn(record.getServerSn());
        barcodeRecord.setDataType(dataType);
        barcodeRecord.setDataUpStatus(StringUtils.equals(status, Constant.PUSH_B2B_STATUS_CY) ? NumConstant.STR_ONE : NumConstant.STR_ONE_NEGATIVE);
        record.setUploadStatus(StringUtils.equals(status, Constant.PUSH_B2B_STATUS_CY) ? NumConstant.STR_ONE : NumConstant.STR_ONE_NEGATIVE);
        barcodeRecord.setLastUpdatedBy(customerDataLogDTO.getLastUpdatedBy());
        List<MptTestLog> list = new ArrayList<>();
        list.add(record);
        wholeMachineUpTestRecordRepository.batchMergeMptTestLog(list);
        // 批量更新BarcodeDataUploadRecordEntityDTO的状态
        barcodeDataUploadRecordService.batchUpdateStatus(new ArrayList(){{add(barcodeRecord);}});
    }

    /* Ended by AICoder, pid:u5cd90fff7ja3f614623096f806c1e45182632d5 */

    /* Started by AICoder, pid:9233d80464y08ba148ac0aacc02f314311891637 */
    @Override
    public String uploadTestRecords(String empNo, WholeMachineUpTestRecordEntityDTO entityDTO)  {
        // 获取腾讯客户的任务数据字典
        List<SysLookupValues> userAddressList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_3020041);
        if (CollectionUtils.isEmpty(userAddressList)) {
            return "";
        }

        // 提取并去重地址描述
        List<String> userAddresses = userAddressList.stream().filter(e->StringUtils.equals(tencentName,e.getLookupMeaning()))
                .map(SysLookupValues::getDescription)
                .distinct()
                .collect(Collectors.toList());

        // 初始化分页查询参数
        CpmContractEntitiesQueryDTO queryDTO = this.getCpmContractEntitiesQueryDTO(entityDTO.getEntityNameList(), userAddresses);
        queryDTO.setEmpNo(empNo);
        Page<CpmContractEntitiesQueryDTO> page = new Page<>();
        page.setParams(queryDTO);
        page.setPageSize(Integer.parseInt(pageSize));

        int currentPage = 1;
        StringBuilder stringBuilder = new StringBuilder();
        while (true) {
            page.setCurrent(currentPage);
            List<CpmContractEntitiesDTO> contractEntities = cpmContractEntitiesService.getTaskInfoList(page);

            if (CollectionUtils.isEmpty(contractEntities)) {
                break;  // 没有更多数据时退出循环
            }

            stringBuilder.append(this.batchProcessing(queryDTO, contractEntities, userAddressList)).append(Constant.SEMICOLON);

            currentPage++;
        }
        return stringBuilder.toString();
    }

    private CpmContractEntitiesQueryDTO getCpmContractEntitiesQueryDTO(List<String> taskNoList, List<String> userAddresses) {
        CpmContractEntitiesQueryDTO queryDTO = new CpmContractEntitiesQueryDTO();

        List<SysLookupValues> sysLookupValuesList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_FORWARD_TENCENT);
        if (CollectionUtils.isEmpty(sysLookupValuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_FORWARD_TENCENT});
        }
        for (SysLookupValues sysLookupValues : sysLookupValuesList) {
            BigDecimal code = sysLookupValues.getLookupCode();
            if (code == null){
                continue;
            }
            String lookupCode = code.toString();
            if(StringUtils.equals(lookupCode,Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD)){
                queryDTO.setCustomer(sysLookupValues.getDescription());
            }else if(StringUtils.equals(lookupCode,Constant.LOOKUP_TYPE_FORWARD_TENCENT_ENAME)){
                queryDTO.setEntityNameLike(sysLookupValues.getDescription());
            }else if(StringUtils.equals(lookupCode,Constant.LOOKUP_TYPE_FORWARD_TENCENT_STAT)){
                queryDTO.setStatusList(Arrays.asList(sysLookupValues.getDescription().split(Constant.COMMA)));
            }else if(StringUtils.equals(lookupCode,Constant.LOOKUP_TYPE_FORWARD_TENCENT_BOQ)){
                queryDTO.setDescLike(sysLookupValues.getDescription());
            }else if(StringUtils.equals(lookupCode, Constant.LOOKUP_TYPE_FORWARD_TENCENT_INTERCEPT)){
                queryDTO.setIntercept(sysLookupValues.getDescription());
            }
        }
        //消息类型
        List<SysLookupValues> messageTypeList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240063);
        if (CollectionUtils.isEmpty(messageTypeList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_8240063});
        }
        for (SysLookupValues sysLookupValues : messageTypeList) {
            if(sysLookupValues.getLookupCode() != null && StringUtils.equals(sysLookupValues.getLookupCode().toString(),Constant.LOOKUP_TYPE_824006300013)){
                queryDTO.setMessageType(sysLookupValues.getDescription());
                break;
            }
        }

        //数据类型
        String dataType = this.getDataType();
        queryDTO.setDataType(dataType);
        queryDTO.setTaskNoList(taskNoList);
        queryDTO.setUserAddressList(userAddresses);
        return queryDTO;
    }

    private String getDataType() {
        String dataType = "";
        List<SysLookupValues> dataTypeList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240092);
        if (CollectionUtils.isEmpty(dataTypeList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,
                    new Object[]{Constant.LOOKUP_TYPE_8240092});
        }
        for (SysLookupValues sysLookupValues : dataTypeList) {
            if(sysLookupValues.getLookupCode() != null && StringUtils.equals(sysLookupValues.getLookupCode().toString(),Constant.LOOKUP_TYPE_824009200004)){
                dataType = sysLookupValues.getLookupMeaning();
                break;
            }
        }
        return dataType;
    }

    private String batchProcessing(CpmContractEntitiesQueryDTO cpmContractEntitiesQueryDTO, List<CpmContractEntitiesDTO> contractEntities, List<SysLookupValues> userAddressList) {
        try {
            WholeMachineUpTestRecordService wholeMachineUpTestRecordService =
                    SpringContextUtil.getBean(WholeMachineUpTestRecordService.class);
            if(wholeMachineUpTestRecordService == null){
                return Constant.INITIALIZATION_SERVICE_FAILED;
            }
            wholeMachineUpTestRecordService.batchProcessingOfData(cpmContractEntitiesQueryDTO, contractEntities, userAddressList);
        } catch (Exception e) {
            imesLogService.log(e.getMessage(), "上传征集测试数据异常，异常信息");
            logger.error("上传征集测试数据异常，异常信息: {}", e.getMessage(), e);
            return e.getMessage();
        }
        return "";
    }

    /* Ended by AICoder, pid:9233d80464y08ba148ac0aacc02f314311891637 */

    @Override
    @RecordLogAnnotation("批量上传整机测试数据")
    public void batchProcessingOfData(CpmContractEntitiesQueryDTO cpmContractEntitiesQueryDTO, List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList, List<SysLookupValues> userAddressList)  {
        String customer = cpmContractEntitiesQueryDTO.getCustomer();
        String descLike = cpmContractEntitiesQueryDTO.getDescLike();
        String empNo = cpmContractEntitiesQueryDTO.getEmpNo();
        String intercept = cpmContractEntitiesQueryDTO.getIntercept();
        List<String> listEntityName = cpmContractEntitiesDTOList.stream().map(CpmContractEntitiesDTO::getEntityName).collect(Collectors.toList());
        //机型
        List<ZmsCbomInfoDTO> configDescList = zmsDeviceInventoryUploadRepository.getConfigDesc(listEntityName, descLike, intercept);
        Map<String, ZmsCbomInfoDTO> configDescMap =  CollectionUtils.isEmpty(configDescList) ? new HashMap<>() : configDescList.stream().collect(Collectors.toMap(ZmsCbomInfoDTO::getEntityName,  a -> a, (k1, k2) -> k1));
        //客户
        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            cpmContractEntitiesDTO.setCustomer(customer);
            cpmContractEntitiesDTO.setEmpNo(empNo);
            this.setServerSn(cpmContractEntitiesDTO, customer);
        }

        //中试信息
        Map<String, MdsFeedbackProductionStationTestingInfoDTO> mdfMap = this.getFeedbackProductionStationTestingInfoDTOMap(cpmContractEntitiesDTOList);
        //写记录表以及推送B2B
        this.writeVariousData(cpmContractEntitiesQueryDTO,cpmContractEntitiesDTOList, mdfMap, configDescMap);
    }

    private Map<String, MdsFeedbackProductionStationTestingInfoDTO> getFeedbackProductionStationTestingInfoDTOMap(List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList) {
        List<String> snList = cpmContractEntitiesDTOList.stream().filter(e->StringUtils.isNotEmpty(e.getServiceSn())).map(CpmContractEntitiesDTO::getServiceSn).collect(Collectors.toList());
        List<MdsFeedbackProductionStationTestingInfoDTO> mdfList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(snList)) {
            mdfList = mdsRemoteService.feedbackOfProductionStationTestingInformation(snList);
        }
        Map<String, MdsFeedbackProductionStationTestingInfoDTO> mdfMap = mdfList.stream().collect(Collectors.toMap(MdsFeedbackProductionStationTestingInfoDTO::getServerSn, e -> e, (k1, k2) -> k1));
        return mdfMap;
    }

    private void setServerSn(CpmContractEntitiesDTO cpmContractEntitiesDTO, String customer)  {
        //任务装配物料
        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList =
                wsmAssembleLinesService.getAssemblyMaterialsByEntityId(cpmContractEntitiesDTO.getEntityId());
        if (CollectionUtils.isEmpty(cpmConfigItemAssembleDTOList)) {
            return;
        }
        cpmContractEntitiesDTO.setCpmConfigItemAssembleDTOList(cpmConfigItemAssembleDTOList);
        List<String> itemCodeList = cpmConfigItemAssembleDTOList.stream().map(CpmConfigItemAssembleDTO::getItemCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        //调iMES获取物料代码客户基础信息
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService
                .getCustomerItemsInfo(customer, itemCodeList);
        //没物料基础信息的返回
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        cpmContractEntitiesDTO.setCustomerItemsDTOList(customerItemsDTOList);
        //过滤出服务器sn
        CustomerItemsDTO serviceSnCustomerItemsDTO = customerItemsDTOList.stream()
                .filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.STR_3)).findFirst().orElse(null);
        if (serviceSnCustomerItemsDTO == null) {
            return;
        }
        //服务器SN对应客户物料信息
        cpmContractEntitiesDTO.setServiceSnCustomerItemsDTO(serviceSnCustomerItemsDTO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = cpmConfigItemAssembleDTOList.stream()
                .filter(e -> StringUtils.equals(e.getItemCode(), serviceSnCustomerItemsDTO.getZteCode())).findFirst().orElse(null);
        if (cpmConfigItemAssembleDTO == null) {
            return;
        }
        cpmContractEntitiesDTO.setServiceSn(cpmConfigItemAssembleDTO.getItemBarcode());
        cpmContractEntitiesDTO.setCpmConfigItemAssembleDTO(cpmConfigItemAssembleDTO);
    }

    /**
     * 写数据
     *
     * @param cpmContractEntitiesDTOList
     * @param mdfMap
     * @param configDescMap
     * @return
     * @
     */
    private void writeVariousData(CpmContractEntitiesQueryDTO cpmContractEntitiesQueryDTO,List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList, Map<String, MdsFeedbackProductionStationTestingInfoDTO> mdfMap,
                                  Map<String, ZmsCbomInfoDTO> configDescMap)  {
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = zmsForwardTencentRepository.getBatchRuleDTOList();
        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            cpmContractEntitiesDTO.setBatchRuleDTOList(batchRuleDTOList);
            this.generateWholeMachineUpTestRecordEntityDTOList(mdfMap, configDescMap, cpmContractEntitiesDTO);
        }
        //过滤工站已上传数据
        this.filterUploadedData(cpmContractEntitiesDTOList);
        List<WholeMachineUpTestRecordEntityDTO> allList = new ArrayList<>();
        List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList = cpmContractEntitiesDTO.getWholeMachineUpTestRecordEntityDTOList();
            if (CollectionUtils.isEmpty(wholeMachineUpTestRecordEntityDTOList)) {
                continue;
            }
            CustomerDataLogDTO customerDataLogDTO = this.getCustomerDataLogDTO(cpmContractEntitiesQueryDTO.getMessageType(),cpmContractEntitiesDTO, wholeMachineUpTestRecordEntityDTOList);
            dataList.add(customerDataLogDTO);
            allList.addAll(wholeMachineUpTestRecordEntityDTOList);
            BarcodeDataUploadRecordEntityDTO barcodeDataUploadRecordEntityDTO = this.generateBarcodeDataUploadRecordEntityDTO(cpmContractEntitiesQueryDTO,cpmContractEntitiesDTO);
            barcodeDataUploadRecordEntityDTOList.add(barcodeDataUploadRecordEntityDTO);

        }
        this.dealData(allList, barcodeDataUploadRecordEntityDTOList,dataList);
        //调中试生成文档
        this.generateDocumentsForPilotTesting(cpmContractEntitiesDTOList);
    }

    private void dealData(List<WholeMachineUpTestRecordEntityDTO> allList, List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList,List<CustomerDataLogDTO> dataList ) {
        WholeMachineUpTestRecordService service = SpringContextUtil.getBean(WholeMachineUpTestRecordService.class);
        if(service !=null){
            service.insertData(allList, barcodeDataUploadRecordEntityDTOList,dataList);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertData(List<WholeMachineUpTestRecordEntityDTO> allList, List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList,List<CustomerDataLogDTO> dataList ) {
        //写业务数据记录表
        this.batchInsert(allList);
        //写通用上传日志
        barcodeDataUploadRecordService.batchInsertOrUpdate(barcodeDataUploadRecordEntityDTOList);
        //推送B2B
        this.pushDataToB2B(dataList);
    }

    private void generateDocumentsForPilotTesting(List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList) {
        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList = cpmContractEntitiesDTO.getWholeMachineUpTestRecordEntityDTOList();
            if (StringUtils.isEmpty(cpmContractEntitiesDTO.getServiceSn()) || CollectionUtils.isEmpty(wholeMachineUpTestRecordEntityDTOList)) {
                continue;
            }
            mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(cpmContractEntitiesDTO.getServiceSn(), wholeMachineUpTestRecordEntityDTOList.stream().map(e -> e.getStationId()).distinct().collect(Collectors.toList()), false);
        }
    }

    private void generateWholeMachineUpTestRecordEntityDTOList(Map<String, MdsFeedbackProductionStationTestingInfoDTO> mdfMap, Map<String, ZmsCbomInfoDTO> configDescMap, CpmContractEntitiesDTO cpmContractEntitiesDTO) {
        MdsFeedbackProductionStationTestingInfoDTO mdsDTO = mdfMap.get(cpmContractEntitiesDTO.getServiceSn());
        if (mdsDTO == null) {
            return;
        }
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = cpmContractEntitiesDTO.getCpmConfigItemAssembleDTO();
        if (cpmConfigItemAssembleDTO == null) {
            return;
        }
        CustomerItemsDTO serviceSnCustomerItemsDTO = cpmContractEntitiesDTO.getServiceSnCustomerItemsDTO();
        if (serviceSnCustomerItemsDTO == null) {
            return;
        }
        List<CustomerItemsDTO> customerItemsDTOList = cpmContractEntitiesDTO.getCustomerItemsDTOList();
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        List<MdsStationsDTO> stationsDTOS = mdsDTO.getStations();
        if (CollectionUtils.isEmpty(stationsDTOS)) {
            return;
        }
        List<CustomerItemsDTO> problemsCustomerItemsInfoList = this.getProblemsCustomerItemsInfoList(cpmContractEntitiesDTO, stationsDTOS);
        Map<String, CustomerItemsDTO> customerItemsDTOMap = problemsCustomerItemsInfoList.stream().collect(Collectors.toMap(CustomerItemsDTO::getZteCode, a -> a, (k1, k2) -> k1));

        List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList = new ArrayList<>();
        for (MdsStationsDTO stationsDTO : stationsDTOS) {
            WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO = this.generateDTO();
            wholeMachineUpTestRecordEntityDTO.setEntityName(cpmContractEntitiesDTO.getEntityName());
            wholeMachineUpTestRecordEntityDTO.setManufacturer(Constant.ZTE_EN);
            wholeMachineUpTestRecordEntityDTO.setProductionLineInfo(Constant.ZTE_CORPORATION);
            this.setStrDeviceClassName(cpmContractEntitiesDTO, configDescMap, wholeMachineUpTestRecordEntityDTO);
            wholeMachineUpTestRecordEntityDTO.setServerSn(this.getNonNullValue(cpmConfigItemAssembleDTO.getItemBarcode()));
            //厂商机型 用整机代码从‘基础信息表——客户物料名称’取值
            wholeMachineUpTestRecordEntityDTO.setTdtName(this.getNonNullValue(serviceSnCustomerItemsDTO.getCustomerSpecification()));
            wholeMachineUpTestRecordEntityDTO.setItemCode(serviceSnCustomerItemsDTO.getZteCode());

            wholeMachineUpTestRecordEntityDTO.setInterceptedPartDesc(this.getNonNullValue(serviceSnCustomerItemsDTO.getCustomerComponentType()));
            wholeMachineUpTestRecordEntityDTO.setTestStation(this.getNonNullValue(stationsDTO.getName()));
            this.setTestTime(stationsDTO, wholeMachineUpTestRecordEntityDTO);
            wholeMachineUpTestRecordEntityDTO.setTestResult(StringUtils.equals(stationsDTO.getState(), Constant.tencentUpload.PASS) ? Constant.tencentUpload.P : Constant.tencentUpload.F);
            List<MdsProblemsDTO> problemsDTOS = stationsDTO.getProblems();
            boolean isRunIn = StringUtils.equalsAnyIgnoreCase(Constant.tencentUpload.RUN_IN,stationsDTO.getName());
            this.setInterceptedPartTestDuration(stationsDTO, isRunIn, wholeMachineUpTestRecordEntityDTO);

            if (CollectionUtils.isNotEmpty(problemsDTOS)) {
                this.setInterceptedPartTestCase(isRunIn, wholeMachineUpTestRecordEntityDTO, problemsDTOS);
                wholeMachineUpTestRecordEntityDTO.setFaultDescription(this.getNonNullValue(problemsDTOS.get(NumConstant.NUM_ZERO).getPhenomenonDetail()));
                wholeMachineUpTestRecordEntityDTO.setServerFaultType(this.getNonNullValue(problemsDTOS.get(NumConstant.NUM_ZERO).getPhenomenonType()));
                List<String> itemCodeList = problemsDTOS.stream().map(MdsProblemsDTO::getComponentItemcode).distinct().collect(Collectors.toList());
                List<CustomerItemsDTO> tempItemList = problemsCustomerItemsInfoList.stream().filter(e -> itemCodeList.contains(e.getZteCode())).collect(Collectors.toList());
                this.setProdlemItemInfo(tempItemList, wholeMachineUpTestRecordEntityDTO);

                this.setInterceptedPartDc(cpmContractEntitiesDTO, problemsDTOS, customerItemsDTOMap, wholeMachineUpTestRecordEntityDTO);
                //供应商拦截部件SN
                wholeMachineUpTestRecordEntityDTO.setInterceptedPartSn(this.getNonNullValue(problemsDTOS.stream().map(MdsProblemsDTO::getComponentSn).filter(e -> StringUtils.isNotEmpty(e)).collect(Collectors.joining(Constant.COMMA))));
                //拦截部件槽位信息
                wholeMachineUpTestRecordEntityDTO.setInterceptedPartSlot(Constant.NONE);
            }

            wholeMachineUpTestRecordEntityDTO.setStationId(stationsDTO.getStationId());
            wholeMachineUpTestRecordEntityDTO.setUploadPhase(NumConstant.STR_ZERO);
            wholeMachineUpTestRecordEntityDTO.setTestProgramVersion(Constant.NONE);
            wholeMachineUpTestRecordEntityDTO.setInterceptedPartDesc(this.getNonNullValue(serviceSnCustomerItemsDTO.getCustomerComponentType()));
            wholeMachineUpTestRecordEntityDTO.setCreatedBy(cpmContractEntitiesDTO.getEmpNo());
            wholeMachineUpTestRecordEntityDTO.setLastUpdatedBy(cpmContractEntitiesDTO.getEmpNo());
            wholeMachineUpTestRecordEntityDTOList.add(wholeMachineUpTestRecordEntityDTO);
        }
        cpmContractEntitiesDTO.setWholeMachineUpTestRecordEntityDTOList(wholeMachineUpTestRecordEntityDTOList);
    }

    /* Started by AICoder, pid:oa6b1p3565ebdda149830a6fb09084222df9b4e3 */
    private void setInterceptedPartDc(CpmContractEntitiesDTO cpmContractEntitiesDTO,
                                      List<MdsProblemsDTO> problemsDTOS,
                                      Map<String, CustomerItemsDTO> customerItemsDTOMap,
                                      WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO) {

        // 使用更高效的流操作来处理列表
        List<String> interceptedPartDcList = problemsDTOS.stream()
                .map(problemsDTO -> {
                    CustomerItemsDTO customerItemsDTO = customerItemsDTOMap.get(problemsDTO.getComponentItemcode());
                    if (customerItemsDTO == null) {
                        return null;
                    }
                    String productionBatch = zmsForwardTencentService.getProductionBatch(
                            cpmContractEntitiesDTO.getBatchRuleDTOList(),
                            customerItemsDTO.getCustomerComponentType(),
                            customerItemsDTO.getCustomerSupplier(),
                            problemsDTO.getComponentSn()
                    );
                    return StringUtils.isEmpty(productionBatch) ? null : productionBatch;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 只有在列表不为空时才设置值
        if (!interceptedPartDcList.isEmpty()) {
            String joinedProductionBatches = StringUtils.join(interceptedPartDcList, Constant.COMMA);
            wholeMachineUpTestRecordEntityDTO.setInterceptedPartDc(this.getNonNullValue(joinedProductionBatches));
        }
    }

    /* Ended by AICoder, pid:oa6b1p3565ebdda149830a6fb09084222df9b4e3 */

    private void setProdlemItemInfo(List<CustomerItemsDTO> tempItemList, WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO) {
        if (CollectionUtils.isNotEmpty(tempItemList)) {
            //故障部件类别，到‘基础信息表’取‘客户部件类型’
            String customerComponentType = tempItemList.stream().map(CustomerItemsDTO::getCustomerComponentType).filter(e -> StringUtils.isNotEmpty(e)).collect(Collectors.joining(Constant.COMMA));
            wholeMachineUpTestRecordEntityDTO.setPartFaultType(this.getNonNullValue(customerComponentType));
            //故障部件厂商，到‘基础信息表’取‘客户供应商’
            wholeMachineUpTestRecordEntityDTO.setPartFaultVendor(this.getNonNullValue(tempItemList.stream().map(CustomerItemsDTO::getCustomerSupplier).filter(e -> StringUtils.isNotEmpty(e)).collect(Collectors.joining(Constant.COMMA))));
            //供应商拦截部件PN，到‘基础信息表’取‘客户部件类型’
            wholeMachineUpTestRecordEntityDTO.setInterceptedPartPn(this.getNonNullValue(customerComponentType));
        }
    }

    private List<CustomerItemsDTO> getProblemsCustomerItemsInfoList(CpmContractEntitiesDTO cpmContractEntitiesDTO, List<MdsStationsDTO> stationsDTOS) {
        List<String> problemsItemCodeList = new ArrayList<>();
        for (MdsStationsDTO stationsDTO : stationsDTOS) {
            List<MdsProblemsDTO> problems = stationsDTO.getProblems();
            if(CollectionUtils.isNotEmpty(problems)){
                problemsItemCodeList.addAll(problems.stream().filter(e->StringUtils.isNotEmpty(e.getComponentItemcode())).map(e->e.getComponentItemcode()).distinct().collect(Collectors.toList()));
            }
        }
        //调iMES获取物料代码客户基础信息
        List<CustomerItemsDTO> problemsCustomerItemsInfoList = centerfactoryRemoteService
                .getCustomerItemsInfo(cpmContractEntitiesDTO.getCustomer(), problemsItemCodeList);
        return CollectionUtils.isEmpty(problemsCustomerItemsInfoList)?new ArrayList<>():problemsCustomerItemsInfoList;
    }

    private WholeMachineUpTestRecordEntityDTO generateDTO() {
        WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO = new WholeMachineUpTestRecordEntityDTO();
        wholeMachineUpTestRecordEntityDTO.setStrDeviceClassName(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setTdtName(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setStrVersion(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setServerSn(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setTestStation(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setTestProgramVersion(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setTestStartTime("");
        wholeMachineUpTestRecordEntityDTO.setTestEndTime("");
        wholeMachineUpTestRecordEntityDTO.setTestResult(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setFaultDescription(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setServerFaultType(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setPartFaultType(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setPartFaultVendor(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartPn(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartSn(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartSlot(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartDc(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartDesc(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartTestCase(Constant.NONE);
        wholeMachineUpTestRecordEntityDTO.setInterceptedPartTestDuration("");
        wholeMachineUpTestRecordEntityDTO.setTestLogName(Constant.NONE);
        return wholeMachineUpTestRecordEntityDTO;

    }

    private String getNonNullValue(String value) {
        return StringUtils.isEmpty(value)?Constant.NONE:value;
    }

    private void setInterceptedPartTestDuration(MdsStationsDTO stationsDTO, boolean isRunIn, WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO) {
        if(isRunIn){
             wholeMachineUpTestRecordEntityDTO.setInterceptedPartTestDuration(stationsDTO.getFailTime());
        }
    }

    private void setInterceptedPartTestCase(boolean isRunIn, WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO, List<MdsProblemsDTO> problemsDTOS) {
        if(isRunIn){
            wholeMachineUpTestRecordEntityDTO.setInterceptedPartTestCase(this.getNonNullValue(problemsDTOS.get(NumConstant.NUM_ZERO).getPhenomenonDetail()));
        }
    }

    private void pushDataToB2B(List<CustomerDataLogDTO> dataList)  {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        Integer size = Integer.parseInt(b2bSize);
        for (List<CustomerDataLogDTO> tempList : CommonUtils.splitList(dataList, size)) {
            centerfactoryRemoteService.pushDataToB2B(tempList);
        }
    }

    private void setTestTime(MdsStationsDTO stationsDTO, WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO) {
        if (stationsDTO.getStartTime() != null) {
            wholeMachineUpTestRecordEntityDTO.setTestStartTime(DateUtil.convertDateToString(new Date(stationsDTO.getStartTime() * NumConstant.NUM_ONE_THOUSAND),DateUtil.DATE_FORMATE_FULL));
        }
        if (stationsDTO.getEndTime() != null) {
            wholeMachineUpTestRecordEntityDTO.setTestEndTime(DateUtil.convertDateToString(new Date(stationsDTO.getEndTime() * NumConstant.NUM_ONE_THOUSAND),DateUtil.DATE_FORMATE_FULL));
        }
    }

    private BarcodeDataUploadRecordEntityDTO generateBarcodeDataUploadRecordEntityDTO(CpmContractEntitiesQueryDTO cpmContractEntitiesQueryDTO,CpmContractEntitiesDTO cpmContractEntitiesDTO) {
        BarcodeDataUploadRecordEntityDTO barcodeDataUploadRecordEntityDTO = new BarcodeDataUploadRecordEntityDTO();
        barcodeDataUploadRecordEntityDTO.setCustomerName(cpmContractEntitiesDTO.getCustomer());
        barcodeDataUploadRecordEntityDTO.setEntityName(cpmContractEntitiesDTO.getEntityName());
        barcodeDataUploadRecordEntityDTO.setEntityId(cpmContractEntitiesDTO.getEntityId() + "");
        barcodeDataUploadRecordEntityDTO.setContractNumber(cpmContractEntitiesDTO.getContractNumber());
        barcodeDataUploadRecordEntityDTO.setContractHeaderId(cpmContractEntitiesDTO.getContractHeaderId());
        barcodeDataUploadRecordEntityDTO.setServerSn(cpmContractEntitiesDTO.getServiceSn());
        barcodeDataUploadRecordEntityDTO.setDataType(cpmContractEntitiesQueryDTO.getDataType());
        barcodeDataUploadRecordEntityDTO.setDataUpStatus(Constant.STR_NUMBER_ZERO);
        barcodeDataUploadRecordEntityDTO.setCreatedBy(cpmContractEntitiesDTO.getEmpNo());
        barcodeDataUploadRecordEntityDTO.setLastUpdatedBy(cpmContractEntitiesDTO.getEmpNo());
        return barcodeDataUploadRecordEntityDTO;
    }

    private void filterUploadedData(List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList) {
        //工站ID
        List<String> stationIdList = new ArrayList<>();
        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList = cpmContractEntitiesDTO.getWholeMachineUpTestRecordEntityDTOList();
            if (CollectionUtils.isEmpty(wholeMachineUpTestRecordEntityDTOList)) {
                continue;
            }
            stationIdList.addAll(wholeMachineUpTestRecordEntityDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getStationId())).map(e -> e.getStationId()).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(stationIdList)) {
            return;
        }
        List<WholeMachineUpTestRecordEntityDTO> entityDTOList = new ArrayList<>();

        for (List<String> tempIdList : Lists.partition(stationIdList, NumConstant.INT_999)) {
            List<WholeMachineUpTestRecordEntityDTO> tempList = wholeMachineUpTestRecordRepository.getListByStationIdList(tempIdList, Arrays.asList(uploadPhase.split(Constant.COMMA)));
            if (CollectionUtils.isNotEmpty(tempList)) {
                entityDTOList.addAll(tempList);
            }
        }


        //按stationId分组
        Map<String, WholeMachineUpTestRecordEntityDTO> map = entityDTOList.stream().collect(Collectors.toMap(e -> e.getStationId(), e -> e, (k1, k2) -> k1));

        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList = cpmContractEntitiesDTO.getWholeMachineUpTestRecordEntityDTOList();
            if (CollectionUtils.isEmpty(wholeMachineUpTestRecordEntityDTOList)) {
                continue;
            }
            cpmContractEntitiesDTO.setWholeMachineUpTestRecordEntityDTOList(wholeMachineUpTestRecordEntityDTOList.stream().filter(e -> !map.keySet().contains(e.getStationId())).collect(Collectors.toList()));
        }
    }


    private CustomerDataLogDTO getCustomerDataLogDTO(String messageType,CpmContractEntitiesDTO cpmContractEntitiesDTO, List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordEntityDTOList) {
        CustomerItemsDTO serviceSnCustomerItemsDTO = cpmContractEntitiesDTO.getServiceSnCustomerItemsDTO();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = cpmContractEntitiesDTO.getCpmConfigItemAssembleDTO();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        String id =UUID.randomUUID().toString();
        customerDataLogDTO.setId(id);
        customerDataLogDTO.setOrigin(Constant.MES);
        if (serviceSnCustomerItemsDTO != null) {
            customerDataLogDTO.setCustomerName(serviceSnCustomerItemsDTO.getCustomerName());
            customerDataLogDTO.setProjectName(serviceSnCustomerItemsDTO.getProjectName());
            customerDataLogDTO.setProjectPhase(serviceSnCustomerItemsDTO.getProjectPhase());
            customerDataLogDTO.setCooperationMode(serviceSnCustomerItemsDTO.getCooperationMode());
        }
        if (cpmConfigItemAssembleDTO != null) {
            customerDataLogDTO.setItemNo(cpmConfigItemAssembleDTO.getItemCode());
        }
        customerDataLogDTO.setContractNo(cpmContractEntitiesDTO.getContractNumber());
        customerDataLogDTO.setTaskNo(cpmContractEntitiesDTO.getEntityName());
        customerDataLogDTO.setSn(cpmContractEntitiesDTO.getServiceSn());
        Map<String, Object> map = new HashMap<>();
        map.put(DATA, wholeMachineUpTestRecordEntityDTOList);
        customerDataLogDTO.setJsonData(JSON.toJSONString(map));

        customerDataLogDTO.setMessageType(messageType);
        customerDataLogDTO.setFactoryId(Constant.INT_51);
        customerDataLogDTO.setCreateBy(cpmContractEntitiesDTO.getEmpNo());
        customerDataLogDTO.setLastUpdatedBy(cpmContractEntitiesDTO.getEmpNo());
        return customerDataLogDTO;
    }


    /**
     * 设置腾讯机型
     *
     * @param cpmContractEntitiesDTO
     * @param configDescMap
     * @param wholeMachineUpTestRecordEntityDTO
     */
    private void setStrDeviceClassName(CpmContractEntitiesDTO cpmContractEntitiesDTO, Map<String, ZmsCbomInfoDTO> configDescMap, WholeMachineUpTestRecordEntityDTO wholeMachineUpTestRecordEntityDTO) {
        ZmsCbomInfoDTO zmsCbomInfoDTO = configDescMap.get(cpmContractEntitiesDTO.getEntityName());
        if (zmsCbomInfoDTO != null) {
            wholeMachineUpTestRecordEntityDTO.setStrDeviceClassName(zmsCbomInfoDTO.getCbomNameCn());
            wholeMachineUpTestRecordEntityDTO.setStrVersion(zmsCbomInfoDTO.getCbomVerison());
        }else{
            wholeMachineUpTestRecordEntityDTO.setStrDeviceClassName(Constant.NONE);
            wholeMachineUpTestRecordEntityDTO.setStrVersion(Constant.NONE);
        }
    }


    /* Started by AICoder, pid:379be0e985i40f114c440b4f106e8411d2679ca0 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordList){
        // 如果列表为空或为null，直接返回0
        if (CollectionUtils.isEmpty(wholeMachineUpTestRecordList)) {
            return 0;
        }

        Map<String,WholeMachineUpTestRecordEntityDTO> map =  wholeMachineUpTestRecordList.stream().collect(Collectors.toMap(WholeMachineUpTestRecordEntityDTO::getStationId,  a -> a, (k1, k2) -> k1));

        List<WholeMachineUpTestRecordEntityDTO> valueList = map.values().stream()
                .collect(Collectors.toList());
        int result = 0;

        // 使用Lists.partition分批处理数据，每批100条记录
        for (List<WholeMachineUpTestRecordEntityDTO> partition : Lists.partition(valueList, NumConstant.NUM_100)) {
            result += wholeMachineUpTestRecordRepository.batchInsert(partition);
        }

        return result;
    }

    /* Ended by AICoder, pid:379be0e985i40f114c440b4f106e8411d2679ca0 */
    /**
     *<AUTHOR>
     * MPT测试日志上传重推
     *@Date 2025/2/19 9:51
     *@Param [com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO, java.lang.String]
     *@return
     **/
    @Override
    public void mptTestLogReUpload(WholeMachineUpTestRecordEntityDTO record, String empNo) throws Exception {
        record.setIsMpt(Constant.FLAG_Y);
        // 手动调用需传入条码
        if (CollectionUtils.isEmpty(record.getSnList())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BARCODE_NEED_ENTERED);
        }
        // 条码数量不能大于200条
        if (record.getSnList().size() > Constant.INT_200) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PAGE_TOO_BIG);
        }
        uploadMachineTestLogs(record, empNo);
    }

    @Override
    public void uploadMachineTestLogs(WholeMachineUpTestRecordEntityDTO record, String empNo) throws Exception{
        Page<WholeMachineUpTestRecordEntityDTO> pageInfo = new Page<>();
        List<WholeMachineUpTestRecordEntityDTO> pageList;
        // MPT测试日志上传或整机测试日志上传及重推时设置mpt工站进行过滤
        if (Constant.FLAG_Y.equals(record.getIsMpt()) || Constant.STR_NUMBER_ONE.equals(record.getUploadPhase())
                || Constant.STR_NUMBER_FOUR.equals(record.getUploadPhase())) {
            record.setTestStation(mptStation);
        }
        String dataType = getMptDataType(record);
        int page = Constant.INT_0;
        pageInfo.setPageSize(Constant.INT_200);
        do {
            page++;
            pageInfo.setParams(record);
            pageInfo.setCurrent(page);
            // 获取处理数据
            pageList = wholeMachineUpTestRecordRepository.pageList(pageInfo);
            if (CollectionUtils.isEmpty(pageList)) {
                break;
            }
            // 整机测试日志上传
            this.pushMachineTestLogs(pageList, record.getUploadPhase(), empNo, dataType, record.getIsMpt());
            // 整机测试记录二次上传
            this.pushMachineRecordLogsAgain(pageList, record.getUploadPhase(), empNo);
            // MPT测试日志上传
            this.pushMptTestLog(pageList, record.getIsMpt(), record.getUploadPhase(), empNo, dataType);
        } while (pageList.size() >= Constant.INT_200);

    }

    /**
     *<AUTHOR>
     * 获取版本idap
     *@Date 2025/2/19 10:31
     *@Param [java.util.List<com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO>]
     *@return
     **/
    private Map<String, String> getVersionIdMap (List<WholeMachineUpTestRecordEntityDTO> pageList) throws Exception {
        List<String> entityNameList = pageList.stream().map(WholeMachineUpTestRecordEntityDTO::getEntityName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        zmsForwardTencentService.getLookupMeanList(dto);
        zmsInputDTO.setCustomerId(dto.getCustomerId());
        zmsInputDTO.setCpqdUrl(dto.getCpqdUrl());
        //机型
        List<ZmsCbomInfoDTO> configDescList = zmsDeviceInventoryUploadRepository.getConfigDesc(entityNameList, dto.getDescLike(), dto.getIntercept());
        if (CollectionUtils.isEmpty(configDescList)) {
            return new HashMap<>();
        }
        configDescList.forEach(e -> e.setCbomNameCn(e.getQuotationMaterialNameCn()));
        // 获取套餐代码及整机物料数据
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = this.zmsDeviceInventoryUploadService.getCbomInfo(configDescList, zmsInputDTO);
        if (CollectionUtils.isEmpty(zmsCbomInfoDTOList)) {
            return new HashMap<>();
        }
        Map<String, List<ZmsExtendedAttributeDTO>> versionIdDTOMap = zmsCbomInfoDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getCbomNameCn()) && CollectionUtils.isNotEmpty(e.getExtendedAttributes()))
                .collect(Collectors.toMap(ZmsCbomInfoDTO::getCbomNameCn, ZmsCbomInfoDTO::getExtendedAttributes, (k, v) -> k));
        Map<String, String> versionIdMap = new HashMap<>();
        for (ZmsCbomInfoDTO zmsCbomInfoDTO : configDescList) {
            List<ZmsExtendedAttributeDTO> zmsExtendedAttributeDTOS = versionIdDTOMap.get(zmsCbomInfoDTO.getCbomNameCn());
            if (CollectionUtils.isEmpty(zmsExtendedAttributeDTOS)) {
                continue;
            }
            versionIdMap.put(zmsCbomInfoDTO.getEntityName(), this.getVersionId(zmsExtendedAttributeDTOS));
        }
        return versionIdMap;
    }

    /**
     *<AUTHOR>
     * 获取数据字典
     *@Date 2025/2/19 11:10
     *@Param [com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO]
     *@return
     **/
    private String getMptDataType (WholeMachineUpTestRecordEntityDTO record) {
        String dataType = "";
        if (Constant.FLAG_Y.equals(record.getIsMpt())) {
            dataType = cfgCodeRuleItemService.getSmallBoxSize(Constant.LOOKUP_CODE_MPT_DATA_TYPE);
        } else if (Constant.STR_NUMBER_ONE.equals(record.getUploadPhase())
                || Constant.STR_NUMBER_FOUR.equals(record.getUploadPhase())) {
            dataType = cfgCodeRuleItemService.getSmallBoxSize(Constant.LOOKUP_CODE_MACHINE_TEST_LOG_DATA_TYPE);
        }
        return dataType;
    }

    /**
     *<AUTHOR>
     * MPT测试日志上传
     *@Date 2025/2/18 13:58
     *@Param [java.util.List<com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO>, java.lang.String, java.lang.String]
     *@return
     **/
    private void pushMptTestLog(List<WholeMachineUpTestRecordEntityDTO> pageList, String isMpt, String uploadPhase, String empNo, String dataType) throws Exception {
        if (!Constant.FLAG_Y.equals(isMpt) || !Constant.MPT_PHASE.contains(uploadPhase)) {
            return;
        }
        // 获取版本id
        Map<String, String> versionIdMap = this.getVersionIdMap(pageList);
        if (MapUtils.isEmpty(versionIdMap)) {
            return;
        }
        List<SysLookupValues> addressList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_3020041);
        //客户
        String customer = addressList.get(Constant.INT_0).getLookupMeaning();
        List<String> itemCodeList = pageList.stream().map(WholeMachineUpTestRecordEntityDTO::getItemCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        // 获取客户物料信息
        Map<String, CustomerItemsDTO> customerMap = this.getCustomerItemsDTOMap(customer, itemCodeList);
        if (MapUtils.isEmpty(customerMap)) {
            return;
        }
        List<BarcodeDataUploadRecordEntityDTO> barcodeDataList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<MptTestLog> mptList = new ArrayList<>();
        for (WholeMachineUpTestRecordEntityDTO entityDTO : pageList) {
            // 版本id
            String versionId = versionIdMap.get(entityDTO.getEntityName());
            if (StringUtils.isEmpty(versionId)) {
                continue;
            }
            CustomerItemsDTO customerItemsDTO = customerMap.get(entityDTO.getItemCode());
            if (customerItemsDTO == null) {
                continue;
            }
            List<String> stationIdList = new ArrayList<>();
            stationIdList.add(entityDTO.getStationId());
            List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(entityDTO.getServerSn(), stationIdList, true);
            if (CollectionUtils.isEmpty(stationFileDTOList)) {
                continue;
            }
            MdsFeedbackProductionStationFileDTO stationFileDTO = stationFileDTOList.get(Constant.INT_0);
            if (StringUtils.isEmpty(stationFileDTO.getStationLog())) {
                continue;
            }
            MptTestLog record = this.getMptLogData(entityDTO, empNo, customerItemsDTO, versionId, stationFileDTO.getStationLog());
            mptList.add(record);
            BarcodeDataUploadRecordEntityDTO recordEntityDTO = this.getMptUploadDataMain(entityDTO, empNo, dataType, customer);
            CustomerDataLogDTO customerDataLogDTO = this.getCustomerDataLogDTO(record, entityDTO, customerItemsDTO, null, uploadPhase);
            dataList.add(customerDataLogDTO);
            barcodeDataList.add(recordEntityDTO);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        WholeMachineUpTestRecordServiceImpl service = SpringContextUtil.getBean(WholeMachineUpTestRecordServiceImpl.class);
        if (service != null) {
            service.pushMachineUpTestLogDataToB2B(dataList, barcodeDataList, mptList);
        }
    }

    private String getVersionId (List<ZmsExtendedAttributeDTO> dtoList) {
        List<ZmsExtendedAttributeDTO> queryCode = dtoList.stream().filter(f -> Constant.CBOM_EXTENTION_ZH_CODE.equals(f.getCbomExtentionZh())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryCode)) {
            return queryCode.get(INT_0).getCbomExtentionValue();
        }
        return Constant.STRING_EMPTY;
    }

    private BarcodeDataUploadRecordEntityDTO getMptUploadDataMain(WholeMachineUpTestRecordEntityDTO entityDTO, String empNo, String dataType, String customer) {
        BarcodeDataUploadRecordEntityDTO dto = new BarcodeDataUploadRecordEntityDTO();
        dto.setRecordId(idGenerator.snowFlakeIdStr());
        dto.setCustomerName(customer);
        dto.setEntityName(entityDTO.getEntityName());
        dto.setEntityId(entityDTO.getEntityId());
        dto.setContractNumber(entityDTO.getContractNumber());
        dto.setContractHeaderId(entityDTO.getContractHeaderId());
        dto.setServerSn(entityDTO.getServerSn());
        dto.setDataType(dataType);
        dto.setDataUpStatus(Constant.STR_NUMBER_ZERO);
        dto.setCreatedBy(empNo);
        dto.setLastUpdatedBy(empNo);
        dto.setTestResult(entityDTO.getTestResult());
        return dto;
    }

    private MptTestLog getMptLogData (WholeMachineUpTestRecordEntityDTO entityDTO, String empNo, CustomerItemsDTO customerItemsDTO, String versionId, String stationLog) {
        MptTestLog record = new MptTestLog();
        record.setLogType(Constant.TYPE_MPT);
        record.setAction(Constant.MPT_ACTION_DATA);
        record.setStartCompany(Constant.MPT_START_COMPANY_DATA);
        record.setOrderNo(getDefaultEmptyData(entityDTO.getCustomerPo()));
        record.setServerSn(getDefaultEmptyData(entityDTO.getServerSn()));
        record.setStrModelName(getDefaultEmptyData(customerItemsDTO.getCustomerSpecification()));
        record.setStrDeviceClassName(getDefaultEmptyData(entityDTO.getStrDeviceClassName()));
        record.setStrVersion(getDefaultEmptyData(entityDTO.getStrVersion()));
        record.setUploadStatus(Constant.STR_NUMBER_ZERO);
        record.setVersionId(getDefaultEmptyData(versionId));
        record.setFile(getDefaultEmptyData(stationLog));
        record.setCreateBy(empNo);
        record.setLastUpdateBy(empNo);
        record.setCreateDate(new Date());
        record.setLastUpdateDate(new Date());
        return record;
    }

    private String getDefaultEmptyData(String sourceData) {
        return StringUtils.isEmpty(sourceData) ? Constant.NONE : sourceData;
    }

    private Map<String, Object> getMptLogDataMap (MptTestLog record) {
        Map<String, Object> map = new HashMap<>();
        map.put(Constant.MPT_LOG_TYPE, record.getLogType());
        map.put(Constant.MPT_ACTION, record.getAction());
        map.put(Constant.MPT_START_COMPANY, record.getStartCompany());
        map.put(Constant.MPT_ORDER_NO, record.getOrderNo());
        map.put(Constant.MPT_SERVER_SN, record.getServerSn());
        map.put(Constant.MPT_STR_MODEL_NAME, record.getStrModelName());
        map.put(Constant.MPT_STR_DEVICE_CLASS_NAME, record.getStrDeviceClassName());
        map.put(Constant.MPT_STR_VERSION, record.getStrVersion());
        map.put(Constant.MPT_FILE, record.getFile());
        map.put(Constant.MPT_VERSION_ID, record.getVersionId());
        return map;
    }
    /**
     *<AUTHOR>
     * 整机测试数据二次上传
     *@Date 2025/2/13 16:42
     *@Param [java.util.List<com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO>, java.lang.String, java.lang.String]
     *@return
     **/
    private void pushMachineRecordLogsAgain (List<WholeMachineUpTestRecordEntityDTO> pageList, String uploadPhase, String empNo) throws Exception {
        if (!Constant.STR_NUMBER_THREE.equals(uploadPhase) && !Constant.STR_NUMBER_SIX.equals(uploadPhase)) {
            return;
        }
        List<SysLookupValues> userAddressList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_3020041);
        if(CollectionUtils.isEmpty(userAddressList)){
            return;
        }
        //客户
        String customer = userAddressList.get(0).getLookupMeaning();
        List<String> itemCodeList = pageList.stream().map(WholeMachineUpTestRecordEntityDTO::getItemCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        // 获取客户物料信息
        Map<String, CustomerItemsDTO> customerMap = this.getCustomerItemsDTOMap(customer, itemCodeList);
        if (MapUtils.isEmpty(customerMap)) {
            return;
        }
        List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (WholeMachineUpTestRecordEntityDTO entityDTO : pageList) {
            CustomerItemsDTO customerItemsDTO = customerMap.get(entityDTO.getItemCode());
            if (customerItemsDTO == null) {
                continue;
            }
            BarcodeDataUploadRecordEntityDTO recordEntityDTO = new BarcodeDataUploadRecordEntityDTO();
            recordEntityDTO.setLastUpdatedBy(empNo);
            recordEntityDTO.setServerSn(entityDTO.getServerSn());
            recordEntityDTO.setEntityName(entityDTO.getEntityName());
            recordEntityDTO.setDataType(entityDTO.getDataType());
            CustomerDataLogDTO customerDataLogDTO = this.getCustomerDataLogDTO(null, entityDTO, customerItemsDTO, null, uploadPhase);
            dataList.add(customerDataLogDTO);
            barcodeDataUploadList.add(recordEntityDTO);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        WholeMachineUpTestRecordServiceImpl service = SpringContextUtil.getBean(WholeMachineUpTestRecordServiceImpl.class);
        if (service != null) {
            service.pushMachineUpTestLogDataToB2B(dataList, barcodeDataUploadList, new ArrayList<>());
        }
    }

    /**
     *<AUTHOR>
     * 整机测试日志上传
     *@Date 2025/2/13 16:42
     *@Param [java.util.List<com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO>, java.lang.String, java.lang.String]
     *@return
     **/
    private void pushMachineTestLogs (List<WholeMachineUpTestRecordEntityDTO> pageList, String uploadPhase, String empNo, String dataType, String isMpt) throws Exception {
        if (Constant.FLAG_Y.equals(isMpt) || !Constant.MPT_PHASE.contains(uploadPhase)) {
            return;
        }
        List<SysLookupValues> userAddressList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_3020041);
        if (CollectionUtils.isEmpty(userAddressList)) {
            return;
        }
        //客户
        String customer = userAddressList.get(0).getLookupMeaning();
        List<String> itemCodeList = pageList.stream().map(WholeMachineUpTestRecordEntityDTO::getItemCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, CustomerItemsDTO> customerMap = getCustomerItemsDTOMap(customer, itemCodeList);
        if (customerMap == null) {
            return;
        }
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadList = new ArrayList<>();
        //中试信息
        for (WholeMachineUpTestRecordEntityDTO recordEntityDTO : pageList) {
            List<String> stationIdList = new ArrayList<>();
            stationIdList.add(recordEntityDTO.getStationId());
            List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(recordEntityDTO.getServerSn(), stationIdList, true);
            if (CollectionUtils.isEmpty(stationFileDTOList)) {
                continue;
            }
            MdsFeedbackProductionStationFileDTO stationFileDTO = stationFileDTOList.get(Constant.INT_0);
            if (StringUtils.isEmpty(stationFileDTO.getStationLog())) {
                continue;
            }
            CustomerItemsDTO customerItemsDTO = customerMap.get(recordEntityDTO.getItemCode());
            if (customerItemsDTO == null) {
                continue;
            }
            BarcodeDataUploadRecordEntityDTO barcodeData = this.getBarcodeData(empNo, recordEntityDTO, customer, dataType);
            barcodeDataUploadList.add(barcodeData);
            recordEntityDTO.setLastUpdatedBy(empNo);
            WholeMachineUpTestLogEntityDTO entityDTO = this.getWholeMachineUpTestLogEntityDTO(recordEntityDTO, stationFileDTO.getStationLog());
            CustomerDataLogDTO customerDataLogDTO = this.getCustomerDataLogDTO(null, recordEntityDTO, customerItemsDTO, entityDTO, uploadPhase);
            dataList.add(customerDataLogDTO);
        }
        if (CollectionUtils.isEmpty(barcodeDataUploadList)) {
            return;
        }
        WholeMachineUpTestRecordServiceImpl service = SpringContextUtil.getBean("wholeMachineUpTestRecordService", WholeMachineUpTestRecordServiceImpl.class);
        if (service != null) {
            service.pushMachineUpTestLogDataToB2B(dataList, barcodeDataUploadList, new ArrayList<>());
        }
    }

    /**
     *<AUTHOR>
     * 获取客户物料信息
     *@Date 2025/2/13 16:42
     *@Param [java.lang.String, java.util.List<java.lang.String>]
     *@return
     **/
    private Map<String, CustomerItemsDTO> getCustomerItemsDTOMap(String customer, List<String> itemCodeList) throws Exception {
        //调iMES获取物料代码客户基础信息
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService
                .getCustomerItemsInfo(customer, itemCodeList);
        //没物料基础信息的返回
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return new HashMap<>();
        }
        //过滤出服务器sn
        List<CustomerItemsDTO> completeMachineList = customerItemsDTOList.stream()
                .filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.STR_3)).collect(Collectors.toList());
        return completeMachineList.stream().filter(e -> StringUtils.isNotEmpty(e.getZteCode()))
                .collect(Collectors.toMap(CustomerItemsDTO::getZteCode, e -> e, (k, v) -> k));
    }

    /**
     *<AUTHOR>
     * 获取上传保存数据
     *@Date 2025/2/13 16:41
     *@Param [java.lang.String, com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO, java.lang.String]
     *@return
     **/
    private BarcodeDataUploadRecordEntityDTO getBarcodeData (String empNo, WholeMachineUpTestRecordEntityDTO recordEntityDTO, String customer, String dataType) {
        BarcodeDataUploadRecordEntityDTO dto = new BarcodeDataUploadRecordEntityDTO();
        dto.setCustomerName(customer);
        dto.setEntityName(recordEntityDTO.getEntityName());
        dto.setEntityId(recordEntityDTO.getEntityId());
        dto.setContractNumber(recordEntityDTO.getContractNumber());
        dto.setContractHeaderId(recordEntityDTO.getContractHeaderId());
        dto.setServerSn(recordEntityDTO.getServerSn());
        dto.setDataType(dataType);
        dto.setDataUpStatus(Constant.STR_NUMBER_ONE);
        dto.setCreatedBy(empNo);
        dto.setLastUpdatedBy(empNo);
        return dto;
    }

    /**
     *<AUTHOR>
     * 获取上传B2B数据
     *@Date 2025/2/13 16:40
     *@Param [java.lang.String, com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO, com.zte.interfaces.dto.CustomerItemsDTO, com.zte.interfaces.dto.sfc.WholeMachineUpTestLogEntityDTO]
     *@return
     **/
    private CustomerDataLogDTO getCustomerDataLogDTO(MptTestLog record, WholeMachineUpTestRecordEntityDTO recordEntityDTO,
                                                     CustomerItemsDTO customerItemsDTO, WholeMachineUpTestLogEntityDTO entityDTO, String uploadPhase) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        // 获取消息类型列表
        List<SysLookupValues> messageTypeList = wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPE_8240063);
        if (CollectionUtils.isEmpty(messageTypeList)) {
            return customerDataLogDTO;
        }
        Map<BigDecimal, String> mesaageTypeMap = messageTypeList.stream().filter(e -> e.getLookupCode() != null && StringUtils.isNotEmpty(e.getDescription()))
                .collect(Collectors.toMap(SysLookupValues::getLookupCode, SysLookupValues::getDescription, (k, v) -> k));
        customerDataLogDTO.setId(UUID.randomUUID().toString().replace("-", ""));
        customerDataLogDTO.setOrigin(Constant.MES);
        customerDataLogDTO.setCustomerName(customerItemsDTO.getCustomerName());
        customerDataLogDTO.setProjectName(Constant.ENTITY_ZH);
        customerDataLogDTO.setProjectName(customerItemsDTO.getProjectName());
        customerDataLogDTO.setProjectPhase(customerItemsDTO.getProjectPhase());
        customerDataLogDTO.setTaskNo(recordEntityDTO.getEntityName());
        customerDataLogDTO.setCreateBy(recordEntityDTO.getLastUpdatedBy());
        customerDataLogDTO.setLastUpdatedBy(recordEntityDTO.getLastUpdatedBy());
        if (Constant.STR_NUMBER_THREE.equals(uploadPhase) || Constant.STR_NUMBER_SIX.equals(uploadPhase)) {
            Map<String, Object> map = new HashMap<>();
            map.put(DATA, new ArrayList(){{add(recordEntityDTO);}});
            customerDataLogDTO.setJsonData(JSON.toJSONString(map));
            customerDataLogDTO.setMessageType(mesaageTypeMap.get(new BigDecimal(Constant.LOOKUP_TYPE_824006300013)));
        } else if (record == null && Constant.MPT_PHASE.contains(uploadPhase)) {
            customerDataLogDTO.setMessageType(mesaageTypeMap.get(new BigDecimal(Constant.LOOKUP_TYPE_824006300014)));
            customerDataLogDTO.setJsonData(JSON.toJSONString(entityDTO));
        } else if (record != null) {
            customerDataLogDTO.setMessageType(mesaageTypeMap.get(new BigDecimal(Constant.LOOKUP_TYPE_824006300015)));
            customerDataLogDTO.setCreateBy(record.getLastUpdateBy());
            customerDataLogDTO.setLastUpdatedBy(record.getLastUpdateBy());
            customerDataLogDTO.setJsonData(JSON.toJSONString(getMptLogDataMap(record)));
        }
        customerDataLogDTO.setFactoryId(Constant.INT_51);
        return customerDataLogDTO;
    }

    /**
     *<AUTHOR>
     * 获取上传B2B详情数据
     *@Date 2025/2/13 16:40
     *@Param [com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO, java.lang.String]
     *@return
     **/
    private WholeMachineUpTestLogEntityDTO getWholeMachineUpTestLogEntityDTO(WholeMachineUpTestRecordEntityDTO recordEntityDTO, String fileUrl) throws Exception{
        WholeMachineUpTestLogEntityDTO entityDTO = new WholeMachineUpTestLogEntityDTO();
        entityDTO.setFile(fileUrl);
        entityDTO.setManufacturer(Constant.ZTE_EN);
        entityDTO.setServerSn(recordEntityDTO.getServerSn());
        entityDTO.setTestResult(recordEntityDTO.getTestResult());
        entityDTO.setTestStartTime(recordEntityDTO.getTestStartTime());
        entityDTO.setTestStation(recordEntityDTO.getTestStation());
        entityDTO.setTestEndTime(recordEntityDTO.getTestEndTime());
        entityDTO.setFaultDescription(recordEntityDTO.getFaultDescription());
        entityDTO.setProductionLineInfo(Constant.ZTE_CORPORATION);
        entityDTO.setTestProgramVersion(recordEntityDTO.getTestProgramVersion());
        return entityDTO;
    }

    /**
     *<AUTHOR>
     * 上传B2B，保存上传记录
     *@Date 2025/2/13 16:41
     *@Param [java.util.List<com.zte.interfaces.dto.CustomerDataLogDTO>, java.util.List<com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO>]
     *@return
     **/
    @Transactional(rollbackFor = Exception.class)
    public void pushMachineUpTestLogDataToB2B (List<CustomerDataLogDTO> dataList,
                                               List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadList, List<MptTestLog> mptList) throws Exception {
        // 新增或修改上传信息
        barcodeDataUploadRecordService.batchInsertOrUpdate(barcodeDataUploadList);
        // 推送B2B
        centerfactoryRemoteService.pushDataToB2B(dataList);
        // 新增或修改MPT上传日志数据
        if (CollectionUtils.isNotEmpty(mptList)) {
            wholeMachineUpTestRecordRepository.batchMergeMptTestLog(mptList);
        }
    }

}