package com.zte.application.sfc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.CompleteSetDataDTO;
import com.zte.interfaces.dto.CpmContractEntitiesDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO;
import com.zte.interfaces.dto.sfc.CpmContractEntitiesQueryDTO;
import com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO;

import java.util.List;

/**
 * 整机测试数据数据上传记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-06 10:09:04
 */
public interface WholeMachineUpTestRecordService {

    String uploadTestRecords(String empNo, WholeMachineUpTestRecordEntityDTO entityDTO);

    void updateUploadStatus(CustomerDataLogDTO customerDataLogDTO) throws JsonProcessingException;

    void updateMptUploadStatus(CustomerDataLogDTO customerDataLogDTO);

    public int batchInsert(List<WholeMachineUpTestRecordEntityDTO> wholeMachineUpTestRecordlist);

    void batchProcessingOfData(CpmContractEntitiesQueryDTO cpmContractEntitiesQueryDTO, List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList, List<SysLookupValues> userAddressList);

    void mptTestLogReUpload(WholeMachineUpTestRecordEntityDTO record, String empNo) throws Exception;

    void uploadMachineTestLogs(WholeMachineUpTestRecordEntityDTO record, String empNo) throws Exception;

    void insertData(List<WholeMachineUpTestRecordEntityDTO> allList, List<BarcodeDataUploadRecordEntityDTO> barcodeDataUploadRecordEntityDTOList,List<CustomerDataLogDTO> dataList );
}

