package com.zte.application.api;

import com.zte.interfaces.dto.api.ContractAndTaskDataDTO;
import com.zte.interfaces.dto.api.PackingDataDTO;
import com.zte.interfaces.dto.api.PackingDetailsDTO;
import com.zte.interfaces.dto.api.PalletDataDTO;
import com.zte.interfaces.dto.api.PickListQueryDTO;
import com.zte.interfaces.dto.api.PickListResultDTO;
import com.zte.interfaces.dto.api.ProdPickListMainDTO;
import com.zte.interfaces.dto.api.QueryParamBO;
import com.zte.interfaces.dto.wmes.ProcPicklistDetail;
import com.zte.itp.msa.core.model.PageRows;

import java.util.List;

/**
 * MES 对外提供服务接口
 *
 * <AUTHOR>
 * @date 2024-03-11 15:41
 */
public interface ExternalInterfacesService {

    /**
     * @param queryParamBO 查询参数
     * @return 托盘信息接口
     */
    PageRows<PalletDataDTO> queryPalletData(QueryParamBO queryParamBO);

    /**
     * 查询MES箱单明细数据
     *
     * @param queryParamBO 查询MES箱单明细数据
     * @return MES箱单明细数据
     */
    PageRows<PackingDetailsDTO> queryPackingListDetails(QueryParamBO queryParamBO);

    /**
     * MES箱单数据
     *
     * @param queryParamBO 查询参数
     * @return
     */
    PageRows<PackingDataDTO> queryPackingData(QueryParamBO queryParamBO);

    /**
     * 查询MES合同任务数据
     * @param queryParamBO
     * @return
     */
    PageRows<ContractAndTaskDataDTO> queryContractAndTaskData(QueryParamBO queryParamBO) throws Exception;

    /**
     * 根据任务号查询发料计划
     * @param taskNos
     * @return
     */
    List<PickListResultDTO> queryMaterialOrderNoByTaskNo(List<String> taskNos);

    /**
     * 根据任务号查询领料单信息
     * @param taskNos
     * @return
     */
    List<ProdPickListMainDTO> queryPickListByTaskNos(PickListQueryDTO queryDTO);

    /**
     * 查询领料单信息
     * @param dto queryDTO
     * @return List<ProdPickListMainDTO>
     */
    List<ProdPickListMainDTO> queryPickListCondition(PickListQueryDTO dto);

    /**
     * 获取领料单明细信息
     * @param billNumberList billNumberList
     * @return List<ProcPicklistDetail>
     */
    List<ProcPicklistDetail> queryProcPickDetailBatch(List<String> billNumberList);
}
