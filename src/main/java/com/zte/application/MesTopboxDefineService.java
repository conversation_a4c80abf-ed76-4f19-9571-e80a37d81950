package com.zte.application;

import com.zte.springbootframe.common.model.Page;
import com.zte.interfaces.dto.MesTopboxDefineEntityDTO;

import java.util.List;
import java.util.Map;

/**
 * 顶层箱型定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-23 15:39:39
 */
public interface MesTopboxDefineService {

     List<MesTopboxDefineEntityDTO> getTopBoxByCondition(MesTopboxDefineEntityDTO record) throws Exception;

     long getCount(MesTopboxDefineEntityDTO record);

}

