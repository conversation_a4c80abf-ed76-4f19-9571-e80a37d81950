package com.zte.application.erpdt.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.erpdt.ItemListService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.erpdt.ItemListRepository;
import com.zte.interfaces.dto.CFItemSingleDosageDTO;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.ItemListEntityForAuxDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service("erpTestService")
@DataSource(DatabaseType.ERP)
public class ItemListServiceImpl implements ItemListService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ItemListRepository erpTestrepository;

    @Override
    public Page<ItemListEntityDTO> pageList(ItemListEntityDTO record) throws Exception{
        Page<ItemListEntityDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<ItemListEntityDTO> erpTestlist = erpTestrepository.pageList(pageInfo);
        pageInfo.setRows(erpTestlist);
        return pageInfo;
    }

    @Override
    public List<String> batchSelectByTaskNoList(List<String> taskNoList) throws Exception{
        List<String> itemNoList = erpTestrepository.batchSelectByTaskNoList(taskNoList);
        return itemNoList;
    }

    /**
     * 获取未完工的任务号
     * @param map
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getTaskNoList(Map<String,Object> map) throws Exception{
        List<Map> dosageDTOList = JSONObject.parseArray(JSON.toJSONString(map.get(Constant.LIST_STR)),Map.class);
        List<List<Map>> lists = CommonUtils.splitList(dosageDTOList, NumConstant.NUM_FIVE_HUNDRED);
        List<CFItemSingleDosageDTO> singleDosageList = new ArrayList<>(NumConstant.NUM_TEN);
        // 查询获取 对应的任务号
        lists.forEach(list->{
            List<CFItemSingleDosageDTO> taskNoList = erpTestrepository.getTaskNoList(list);
            singleDosageList.addAll(taskNoList);
        });
        // 获取未完工的任务号
        if(CollectionUtils.isNotEmpty(singleDosageList)){
         return singleDosageList.stream()
                 .filter(singleDosage-> !Constant.STR_NUMBER_FOUR.equals(singleDosage.getStatus()))
                 .map(CFItemSingleDosageDTO::getTaskNo)
                 .collect(Collectors.toList());
        }
        return new ArrayList<>(NumConstant.NUM_TEN);
    };

    @Override
    public long checkItemNoAndTask(ItemListEntityDTO record) throws Exception{
        logger.info("查询ERP物料清单数据，参数：{}",record);
        Page<ItemListEntityDTO> itemListEntityDTO = pageList(record);
        if(CollectionUtils.isNotEmpty(itemListEntityDTO.getRows()))
        {
            return Constant.INT_1;
        }
        return Constant.INT_0;
    }

    /**
     * 获取单个用量
     * @param record
     * @return
     * @throws Exception
     */
    @Override
    public ServiceData<CFItemSingleDosageDTO> getItemSingleDosage(CFItemSingleDosageDTO record) throws Exception {
        logger.info("查询ERP物料单个用量，参数：{}",record);
        ServiceData<CFItemSingleDosageDTO> serviceData = new ServiceData<>();
        CFItemSingleDosageDTO singleDosage = erpTestrepository.getItemSingleDosage(record);
        if(Objects.isNull(singleDosage)){
            serviceData.setBo(new CFItemSingleDosageDTO ());
            serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
            return serviceData;
        }
        serviceData.setBo(singleDosage);
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
        return serviceData;
    }

    /**
     * 获取单个物料已发数量、需求数量
     *
     * @param taskNo 任务
     * @param itemNo 物料代码
     * @return
     */
    @Override
    public ItemListEntityDTO getItemiLstTaskItem(String taskNo, String itemNo) {
        return erpTestrepository.getItemiLstTaskItem(taskNo, itemNo);
    }

    /**
     * 根据任务号批量获取物料清单
     *
     * @param taskNoList
     * @return
     * @throws Exception
     */
    @Override
    public List<ItemListEntityForAuxDTO> getItemListByTaskList(List<String> taskNoList) {
        // 防止内存溢出，一次只查10个任务，在调用端使用分批查询
        List<String> listsOnlyTen = taskNoList.stream().limit(Constant.INT_10).collect(Collectors.toList());
        return erpTestrepository.getItemListByTaskList(listsOnlyTen);
    }

    @Override
    public List<CfInventoryDTO> getItemUsageCount(String taskNo) {
        logger.info("查询ERP任务对应辅料单套用量，参数：{}",taskNo);
        return erpTestrepository.getItemUsageCount(taskNo);
    }

    @Override
    public List<ItemListEntityDTO> getListByTaskList(List<String> taskNoList) {
        // 防止内存溢出，一次只查10个任务，在调用端使用分批查询
        List<String> listsOnlyTen = taskNoList.stream().limit(Constant.INT_10).collect(Collectors.toList());
        return erpTestrepository.getListByTaskList(listsOnlyTen);
    }
}
