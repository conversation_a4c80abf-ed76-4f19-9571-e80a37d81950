/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt.impl;

import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.ZteWipMoveMtlTxnDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.application.erpdt.WipRequirementOperationsService;
import com.zte.domain.model.erpdt.WipRequirementOperations;
import com.zte.domain.model.erpdt.WipRequirementOperationsRepository;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.ERP)
public class WipRequirementOperationsServiceImpl implements WipRequirementOperationsService {
    @Autowired
    private WipRequirementOperationsRepository wipRequirementOperationsRepository;

    public void setWipRequirementOperationsRepository(WipRequirementOperationsRepository wipRequirementOperationsRepository) {
        this.wipRequirementOperationsRepository = wipRequirementOperationsRepository;
    }

    /**
     * 增加实体数据
     * 
     * @param record
     **/

    public void insertWipRequirementOperations(WipRequirementOperations record) {
        wipRequirementOperationsRepository.insertWipRequirementOperations(record);
    }

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    public void insertWipRequirementOperationsSelective(WipRequirementOperations record) {
        wipRequirementOperationsRepository.insertWipRequirementOperationsSelective(record);
    }

    @Override
    public List<WipRequirementOperations> selectWipRequirementOperationsByWipEntityId(Map<String, Object> record){
        return wipRequirementOperationsRepository.selectWipRequirementOperationsByWipEntityId(record);
    }

    /**
     * ERP出入库查询
     */
    @Override
    public ServiceData getErpWarehousing(ZteWipMoveMtlTxnDTO dto) {
        ServiceData<Object> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        if (null == dto) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.QUERY_CONDITION_EMPTY));
            return ret;
        }
        if (StringUtils.isEmpty(dto.getSubinventoryCodes())) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ERP_SUBINVENTORY_CODE_EMPTY));
            return ret;
        }
        if (StringUtils.isEmpty(dto.getLocatorIds())) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.ERP_LOCATOR_ID_EMPTY));
            return ret;
        }
        if (StringUtils.isEmpty(dto.getStartTime()) || StringUtils.isEmpty(dto.getEndTime())) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TRANSACTION_DATE_EMPTY));
            return ret;
        }
        if (StringUtils.isEmpty(dto.getTransactionType())) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.TRANSACTION_TYPE_EMPTY));
            return ret;
        }
        Page<ZteWipMoveMtlTxnDTO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        pageInfo.setParams(dto);
        List<ZteWipMoveMtlTxnDTO> zteWipMoveMtlTxnDTOList = wipRequirementOperationsRepository.getErpWarehousing(pageInfo);
        pageInfo.setRows(zteWipMoveMtlTxnDTOList);
        ret.setBo(pageInfo);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    /**
     * 根据 SourceLineId 查询 apps.mtl_material_transactions 确认幂等信息
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public List<ZteWipMoveMtlTxnDTO> getErpMtlInfoBySourceLineId(ZteWipMoveMtlTxnDTO dto)throws Exception{
        return wipRequirementOperationsRepository.getErpMtlInfoBySourceLineId(dto);
    }

    /**
     * 根据 SourceLineId 查询 apps.mtl_material_transactions 确认幂等信息--批量
     * @param dto
     * @return
     * @throws Exception
     */
    @Override
    public List<ZteWipMoveMtlTxnDTO> getErpMtlInfoBySourceLineIdBatch(ZteWipMoveMtlTxnDTO dto)throws Exception{
        return wipRequirementOperationsRepository.getErpMtlInfoBySourceLineIdBatch(dto);
    }
}