/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt.impl;

import com.zte.application.erpdt.ZteLmsOrgTransInterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.erpdt.ZteLmsOrgTransInter;
import com.zte.domain.model.erpdt.ZteLmsOrgTransInterRepository;
import com.zte.interfaces.assembler.ZteLmsOrgTransInterAssembler;
import com.zte.interfaces.dto.ZteLmsOrgTransInterDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.ERP1)
public class ZteLmsOrgTransInterServiceImpl implements ZteLmsOrgTransInterService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ZteLmsOrgTransInterRepository zteLmsOrgTransInterRepository;

    public void setZteLmsOrgTransInterRepository(ZteLmsOrgTransInterRepository zteLmsOrgTransInterRepository) {
        this.zteLmsOrgTransInterRepository = zteLmsOrgTransInterRepository;
    }

    @Override
    public void insertZteLmsOrgTransInter(ZteLmsOrgTransInter record) {
        zteLmsOrgTransInterRepository.insertZteLmsOrgTransInter(record);
    }

    @Override
    public void insertZteLmsOrgTransInterSelective(ZteLmsOrgTransInter record) {
        zteLmsOrgTransInterRepository.insertZteLmsOrgTransInterSelective(record);
    }

    @Override
    public void updateZteLmsOrgTransInterById(ZteLmsOrgTransInter record) {

    }

    /**
     * 批量插入实体数据
     * @param list
     * @return
     */
    @Override
    public int insertZteLmsOrgTransInterBatch(List<ZteLmsOrgTransInterDTO> list) {
        int insertNum = 0;
        if (!CollectionUtils.isEmpty(list)) {
            insertNum = zteLmsOrgTransInterRepository.insertZteLmsOrgTransInterBatch(list);
            logger.info("写ERP子库存转移，插入ERP的子库存转移:{}", insertNum);
        }
        return insertNum;
    }

    /**
     * 子库存转移查询
     * @param
     * @return ServiceData
     */
    public ServiceData getSubInventoryTransferList(ZteLmsOrgTransInterDTO conditions) throws Exception {
        ServiceData serviceData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        //校验交易类型
        if (StringHelper.isEmpty(conditions.getLookupType())){
            retCode.setMsg(MessageId.CONDITION_NOT_FOUND);
        }
        if (RetCode.SUCCESS_CODE.equals(retCode.getCode())){
            //截取前12位料单代码进行查询
            String inventory=conditions.getInventoryItem();
            if (inventory.length()> Constant.BOM_LENGTH) {
                conditions.setInventoryItem(inventory.substring(0, 12));
            }
            // 声明翻页模板
            Page<ZteLmsOrgTransInterDTO> pageInfo = new Page<>(conditions.getPage(), conditions.getRows());
            // 放入查询参数
            pageInfo.setParams(conditions);
            // 获取查询结果列表
            List<ZteLmsOrgTransInter> warehouse = zteLmsOrgTransInterRepository.getSubInventoryTransferList(pageInfo);
            // 将ErpQuery类型转换成ZteMrpWipIssueDTO类型
            List<ZteLmsOrgTransInterDTO> warehouseList = ZteLmsOrgTransInterAssembler.toZteLmsOrgTransInterDTOList(warehouse);
            for (ZteLmsOrgTransInterDTO list : warehouseList) {
                list.setLookupType(conditions.getLookupType());
            }
            pageInfo.setRows(warehouseList);
            serviceData.setBo(pageInfo);
        }
        return serviceData;
    }
}
