/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt.impl;

import com.zte.application.datawb.OtherSysProdBarcodeListService;
import com.zte.application.erpdt.ZteWipMoveMtlTxnService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MtlSystemItemsB;
import com.zte.domain.model.MtlSystemItemsBRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.datawb.BoardOnlinelist;
import com.zte.domain.model.erpdt.ZteMrpWipIssue;
import com.zte.domain.model.erpdt.ZteMrpWipIssueRepository;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxn;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxnRepository;
import com.zte.interfaces.assembler.ZteWipMoveMtlTxnAssembler;
import com.zte.interfaces.dto.ZteWipMoveMtlTxnDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.AlarmAnnotation;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.transaction.SpringBeanFactory;
import com.zte.springbootframe.transaction.TransactionManagerHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.ERP1)
public class ZteWipMoveMtlTxnServiceImpl implements ZteWipMoveMtlTxnService {
    @Autowired
    private ZteWipMoveMtlTxnRepository zteWipMoveMtlTxnRepository;

    @Autowired
    private MtlSystemItemsBRepository mtlSystemItemsBRepository;

    @Autowired
    private ZteMrpWipIssueRepository zteMrpWipIssueRepository;

    @Autowired
    private OtherSysProdBarcodeListService otherSysProdBarcodeListService;

    @Autowired
    private SpringBeanFactory springBeanFactory;

    private static Logger logger = LoggerFactory.getLogger(ZteWipMoveMtlTxnServiceImpl.class);

    public void setZteWipMoveMtlTxnRepository(ZteWipMoveMtlTxnRepository zteWipMoveMtlTxnRepository) {
        this.zteWipMoveMtlTxnRepository = zteWipMoveMtlTxnRepository;
    }

    public void setZteMrpWipIssueRepository(ZteMrpWipIssueRepository zteMrpWipIssueRepository) {
    	this.zteMrpWipIssueRepository = zteMrpWipIssueRepository;
    }

    public void setMtlSystemItemsBRepository(MtlSystemItemsBRepository mtlSystemItemsBRepository) {
    	this.mtlSystemItemsBRepository = mtlSystemItemsBRepository;
    }

    /**
     * 增加实体数据
     *
     * @param record
     **/
    public void insertZteWipMoveMtlTxn(ZteWipMoveMtlTxn record) {
        zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxn(record);
    }

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    public void insertZteWipMoveMtlTxnSelective(ZteWipMoveMtlTxn record) {
        zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxnSelective(record);
    }

    @Override
    public void deleteZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record) {

    }

    @Override
    public void updateZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record) {

    }

    @Override
    public void selectZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record) {

    }
    /**
     * 入库查询
     * @return ServiceData
     */
    @Override
    public ServiceData getWarehouseQueryList(ZteWipMoveMtlTxnDTO conditions) throws Exception {
        ServiceData serviceData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        //校验交易类型
        if (StringHelper.isEmpty(conditions.getLookupType())){
                retCode.setMsg(MessageId.CONDITION_NOT_FOUND);
            }
        if (RetCode.SUCCESS_CODE.equals(retCode.getCode())){
            // 声明翻页模板
            Page<ZteWipMoveMtlTxnDTO> pageInfo = new Page<>(conditions.getPage(), conditions.getRows());
            // 放入查询参数
            pageInfo.setParams(conditions);
            // 获取查询结果列表
            List<ZteWipMoveMtlTxn> warehouse = zteWipMoveMtlTxnRepository.getWarehouseQueryList(pageInfo);
            // 将ErpQuery类型转换成ZteWipMoveMtlTxnDTO类型
            List<ZteWipMoveMtlTxnDTO> warehouseList = ZteWipMoveMtlTxnAssembler.toZteWipMoveMtlTxnDTOList(warehouse);
            for (ZteWipMoveMtlTxnDTO list : warehouseList) {
                list.setLookupType(conditions.getLookupType());
            }
            pageInfo.setRows(warehouseList);
            serviceData.setBo(pageInfo);
        }
        return serviceData;
    }

    /**
	 * 回写erp
	 * @param warehouseEntryInfo
	 * @return 0 未找到物料ID，1.插入入库表失败  2。插入出库表失败 3.入库表出库表都插入成功
	 * @throws Exception
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int writeErpTransaction(WarehouseEntryInfo warehouseEntryInfo) throws Exception{
		int count = 0;
		ZteWipMoveMtlTxn wipMoveResult = zteWipMoveMtlTxnRepository.getWipMoveMtlByBillNo(warehouseEntryInfo.getBillNo());
		ZteMrpWipIssue  mrpWipResult = zteMrpWipIssueRepository.getMrpWipByBillNo(warehouseEntryInfo.getBillNo());
		MtlSystemItemsB queryCond = new MtlSystemItemsB();
		queryCond.setSegment1(warehouseEntryInfo.getItemNo().substring(0, 12));
		BigDecimal itemId = mtlSystemItemsBRepository.getItemIdBySegment1(queryCond);
		if (null == itemId) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_ID_FOUND_ERROR));
		}
		if (null == wipMoveResult) {
			count = insertWipMoveMtl(warehouseEntryInfo,itemId);
			if (count < 1) {
				throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_MOVE_MTL_INSERT_ERROR));
			}
		}
		if (null == mrpWipResult) {
			count = insertMrpWipIssue(warehouseEntryInfo,itemId);
			if (count < 1) {
				throw new Exception(CommonUtils.getLmbMessage(MessageId.MRP_WIP_ISSUE_INSERT_ERROR));
			}
		}
		return count;
	}

	/**
	 * 回写erp
	 * @param warehouseEntryInfo
	 * @return 0 未找到物料ID，1.插入入库表失败  2。插入出库表失败 3.入库表出库表都插入成功
	 * @throws Exception
	 */
	@Transactional(rollbackFor = Exception.class)
	public int writeErpTransactionNew(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
		MtlSystemItemsB queryCond = new MtlSystemItemsB();
		queryCond.setSegment1(warehouseEntryInfo.getItemNo().substring(0, 12));
		BigDecimal itemId = mtlSystemItemsBRepository.getItemIdBySegment1(queryCond);
		if (null == itemId) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_ID_FOUND_ERROR));
		}

		int count = insertWipMoveMtl(warehouseEntryInfo, itemId);
		if (count < 1) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.WIP_MOVE_MTL_INSERT_ERROR));
		}
		count += insertMrpWipIssue(warehouseEntryInfo, itemId);
		if (count < 1) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.MRP_WIP_ISSUE_INSERT_ERROR));
		}

		return count;
	}

	/**
	 * 回写erp-子卡扫描专用接口-支持幂等
	 * @param warehouseEntryInfo
	 * @throws Exception
	 */
	public void writeErpIdempotent(WarehouseEntryInfo warehouseEntryInfo) throws Exception{
		MtlSystemItemsB queryCond = new MtlSystemItemsB();
		queryCond.setSegment1(warehouseEntryInfo.getItemNo().substring(0, 12));
		BigDecimal itemId = mtlSystemItemsBRepository.getItemIdBySegment1(queryCond);
		if (null == itemId) {
			throw new Exception(CommonUtils.getLmbMessage(MessageId.ITEM_ID_FOUND_ERROR));
		}
		insertWipMoveMtlIdempotent(warehouseEntryInfo, itemId);
		insertMrpWipIssueIdempotent(warehouseEntryInfo, itemId);
		return;
	}

    /**
     * 回写erp并更新IMU（子卡入库专用接口）
     * @param warehouseEntryInfo
     * @param boards
     * @return 0 未找到物料ID，1.插入入库表失败  2。插入出库表失败 3.入库表出库表都插入成功
     * @throws Exception
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
	@AlarmAnnotation(alarmName = "write_erp_and_update_imu_error", alarmKey = "9001", alarmTitle = "入库单回写erp异常告警")
    public void writeErpAndUpdateImu(WarehouseEntryInfo warehouseEntryInfo,List<BoardOnlinelist> boards) throws Exception{
    	// 事务管理器。使支持多数据源事务
    	TransactionManagerHelper transactionManagerHelper = new TransactionManagerHelper();
    	try {
    		transactionManagerHelper.createTransactionManager(DatabaseType.ERP1, springBeanFactory);
    		this.writeErpTransactionNew(warehouseEntryInfo);
    		transactionManagerHelper.createTransactionManager(DatabaseType.DB1, springBeanFactory);
    		// 更新IMU
    		otherSysProdBarcodeListService.updateImuBySnNoTx(boards,BusinessConstant.SUB_BOARD_IN_WAREHOUSE_IMU_ID);
    		// 提交事务
    		transactionManagerHelper.commit();
		} catch (Exception e) {
			// 回滚事务
			transactionManagerHelper.rollback();
			throw e;
		}
    }

	/**
	 * 子卡入库
	 * @param boards
	 * @return 子卡入库。IMU更新为 42
	 * @throws Exception
	 */
	@Override
	public void updateImuForSubCard(List<BoardOnlinelist> boards) throws Exception{
		// 事务管理器。使支持多数据源事务
		TransactionManagerHelper transactionManagerHelper = new TransactionManagerHelper();
		try {
			transactionManagerHelper.createTransactionManager(DatabaseType.DB1, springBeanFactory);
			// 更新IMU
			otherSysProdBarcodeListService.updateImuBySnNoTx(boards,BusinessConstant.BOARD_IN_WAREHOUSE_IMU_ID);
			// 提交事务
			transactionManagerHelper.commit();
		} catch (Exception e) {
			// 回滚事务
			transactionManagerHelper.rollback();
			throw e;
		}
	}


	/**
	 * 回写erp（子卡入库专用接口）
	 * @param warehouseEntryInfo
	 * @return 0 未找到物料ID，1.插入入库表失败  2。插入出库表失败 3.入库表出库表都插入成功
	 * @throws Exception
	 */
	@Override
//    @Transactional(rollbackFor = Exception.class)
	public void writeErpForSubCard(WarehouseEntryInfo warehouseEntryInfo) throws Exception{
		// 事务管理器。使支持多数据源事务
		TransactionManagerHelper transactionManagerHelper = new TransactionManagerHelper();
		try {
			transactionManagerHelper.createTransactionManager(DatabaseType.ERP1, springBeanFactory);
			this.writeErpTransaction(warehouseEntryInfo);
			// 提交事务
			transactionManagerHelper.commit();
		} catch (Exception e) {
			// 回滚事务
			transactionManagerHelper.rollback();
			throw e;
		}
	}

    /**
     * 插入erp入库表
     * @param warehouseEntryInfo
     * @param itemId
     * @return
     */
    public int insertWipMoveMtl(WarehouseEntryInfo warehouseEntryInfo,BigDecimal itemId) {
		ZteWipMoveMtlTxn wipMoveMtl = getZteWipMoveMtlTxn(warehouseEntryInfo);
		wipMoveMtl.setSourceTableName(Constant.SOURCE_TATLE_NAME_WIP_MOVE);
    	wipMoveMtl.setSourceTableDetailid(Constant.SOURCE_TABLE_DETAIL_ID);
    	wipMoveMtl.setOrganizationId(warehouseEntryInfo.getOrgId());
    	wipMoveMtl.setItemId(itemId);
    	int count = zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxnSelective(wipMoveMtl);
    	return count;
    }

	/**
	 * 插入erp入库表-子卡幂等
	 * @param warehouseEntryInfo
	 * @param itemId
	 * @return
	 */
	public void insertWipMoveMtlIdempotent(WarehouseEntryInfo warehouseEntryInfo,BigDecimal itemId) {
		ZteWipMoveMtlTxn wipMoveMtl = getZteWipMoveMtlTxn(warehouseEntryInfo);
		//系统标识 IMES
		wipMoveMtl.setSourceTableName(Constant.IMES);
		//存入幂等ID
		wipMoveMtl.setSourceTableDetailid(warehouseEntryInfo.getErpId());
		wipMoveMtl.setOrganizationId(warehouseEntryInfo.getOrgId());
		wipMoveMtl.setItemId(itemId);

		//根据transaction_reference source_table_detailid source_table_name
		//查询 ZTE_WIP_MOVE_MTL_TXN 以及 zte_wip_move_mtl_txn_BAK，确认是否已经推送过ERP
		long countAll= zteWipMoveMtlTxnRepository.getZteWipMoveTxnAndBakCount(wipMoveMtl);
		if(countAll>0){
			return;
		}
		zteWipMoveMtlTxnRepository.insertZteWipMoveMtlTxnSelective(wipMoveMtl);
	}

	private ZteWipMoveMtlTxn getZteWipMoveMtlTxn(WarehouseEntryInfo warehouseEntryInfo) {
		ZteWipMoveMtlTxn wipMoveMtl = new ZteWipMoveMtlTxn();
		wipMoveMtl.setSourceCode(Constant.SOURCE_CODE);
		wipMoveMtl.setOrganizationId(warehouseEntryInfo.getOrgId());
		wipMoveMtl.setTransactionQuantity(warehouseEntryInfo.getCommitedQty());
		wipMoveMtl.setSubinventoryCode(warehouseEntryInfo.getSubStock());
		wipMoveMtl.setLocatorId(warehouseEntryInfo.getLocatorId());
		// 写空,根据存储过程 kabariii.PKG_MANU_SUBMITII.PRO_DATA_TO_ERP设置
		wipMoveMtl.setWipEntityId(null);
		wipMoveMtl.setTransactionDate(warehouseEntryInfo.getCreateDate());
		// 批次号
		wipMoveMtl.setWipEntityName(warehouseEntryInfo.getProdplanNo());
		wipMoveMtl.setTransactionTypeId(new BigDecimal(Constant.TRANSACTION_TYPE_ID));
		wipMoveMtl.setRevision(warehouseEntryInfo.getItemNo().substring(12));
		wipMoveMtl.setProcessStatus(Constant.FLAG_N);
		wipMoveMtl.setCreatedBy(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		wipMoveMtl.setLastUpdatedBy(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		wipMoveMtl.setLastUpdateLogin(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		wipMoveMtl.setReasonId(new BigDecimal(Constant.REASON_ID));
		wipMoveMtl.setTransactionReference(warehouseEntryInfo.getBillNo());
		wipMoveMtl.setSourceLineId(new BigDecimal(warehouseEntryInfo.getBillNo().substring(2)).add(new BigDecimal(System.currentTimeMillis())));
		wipMoveMtl.setSourceHeaderId(new BigDecimal(warehouseEntryInfo.getBillNo().substring(2)));
		return wipMoveMtl;
	}

	/**
     * 插入erp出库表
     * @param warehouseEntryInfo
     * @param itemId
     * @return
     */
    public int insertMrpWipIssue (WarehouseEntryInfo warehouseEntryInfo,BigDecimal itemId) {
		ZteMrpWipIssue erpWipIssue = getZteMrpWipIssue(warehouseEntryInfo);
		erpWipIssue.setSourceTableName(Constant.SOURCE_TATLE_NAME_MRP_WIP);
    	erpWipIssue.setSourceTableDetailid(Constant.SOURCE_TABLE_DETAIL_ID);
    	erpWipIssue.setItemId(itemId);
    	erpWipIssue.setTransactionReference(warehouseEntryInfo.getBillNo());
    	erpWipIssue.setOrganizationId(warehouseEntryInfo.getOrgId());
    	int count = zteMrpWipIssueRepository.insertZteMrpWipIssueSelective(erpWipIssue);
    	return count;
    }

	/**
	 * 插入erp出库表-子卡幂等
	 * @param warehouseEntryInfo
	 * @param itemId
	 * @return
	 */
	public void insertMrpWipIssueIdempotent (WarehouseEntryInfo warehouseEntryInfo,BigDecimal itemId) {
		ZteMrpWipIssue erpWipIssue = getZteMrpWipIssue(warehouseEntryInfo);
		//系统标识
		erpWipIssue.setSourceTableName(Constant.IMES);
		//幂等ID
		erpWipIssue.setSourceTableDetailid(warehouseEntryInfo.getErpId());
		erpWipIssue.setItemId(itemId);
		erpWipIssue.setTransactionReference(warehouseEntryInfo.getBillNo());
		erpWipIssue.setOrganizationId(warehouseEntryInfo.getOrgId());

		//根据transaction_reference source_table_detailid source_table_name
		//查询 ZTE_MRP_WIP_ISSUE 以及 ZTE_MRP_WIP_ISSUE_BAK，确认是否已经推送过ERP
		long countAll= zteMrpWipIssueRepository.getZteMrpWipIssueAndBakCount(erpWipIssue);
		if(countAll>0){
			return ;
		}
		zteMrpWipIssueRepository.insertZteMrpWipIssueSelective(erpWipIssue);
	}

	private ZteMrpWipIssue getZteMrpWipIssue(WarehouseEntryInfo warehouseEntryInfo) {
		ZteMrpWipIssue erpWipIssue = new ZteMrpWipIssue();
		erpWipIssue.setTransactionType(Constant.TRANSACTON_TYPE_FL);
		erpWipIssue.setTransactionDate(warehouseEntryInfo.getCreateDate());
		erpWipIssue.setOrganizationId(warehouseEntryInfo.getOrgId());
		// 出库写入父任务批次号。
		if (StringUtils.isNotBlank(warehouseEntryInfo.getParentProdplanNo())) {
			erpWipIssue.setWipEntityName(warehouseEntryInfo.getParentProdplanNo());
		} else {
			logger.warn("子卡任务的父任务批次不存在");
			erpWipIssue.setWipEntityName(warehouseEntryInfo.getProdplanNo());
		}
		// 写空,根据存储过程 kabariii.PKG_MANU_SUBMITII.prod_out_data_to_erp设置
		erpWipIssue.setWipEntityId(null);
		erpWipIssue.setItemCode(warehouseEntryInfo.getItemNo().substring(0, 12));
		erpWipIssue.setOperationSeqNum(new BigDecimal(Constant.OPER_SEQ_NUM));
		erpWipIssue.setSubinventoryCode(warehouseEntryInfo.getSubStock());
		erpWipIssue.setTransactionQty(warehouseEntryInfo.getCommitedQty());
		erpWipIssue.setProcessFlag(new BigDecimal(Constant.STR_NUMBER_ONE));
		erpWipIssue.setCreationDate(warehouseEntryInfo.getCreateDate());
		erpWipIssue.setCreatedBy(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		erpWipIssue.setLastUpdateLogin(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		erpWipIssue.setLastUpdatedBy(new BigDecimal(warehouseEntryInfo.getCreateBy()));
		erpWipIssue.setLastUpdateDate(warehouseEntryInfo.getCreateDate());
		erpWipIssue.setItemRevision(warehouseEntryInfo.getItemNo().substring(12));
		erpWipIssue.setRenewPriceFlag(Constant.FLAG_N);
		return erpWipIssue;
	}

	/**
	 * 单板入库只写ERP 入库表
	 * @param warehouseEntryInfo
	 * @return
	 * @throws Exception
	 */
	@Override
	public int onlyWriteErp(WarehouseEntryInfo warehouseEntryInfo) throws Exception {
		int count = 0;
		MtlSystemItemsB queryCond = new MtlSystemItemsB();
		queryCond.setSegment1(warehouseEntryInfo.getItemNo().substring(0, 12));
		BigDecimal itemId = mtlSystemItemsBRepository.getItemIdBySegment1(queryCond);
		if (null == itemId) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ITEM_ID_FOUND_ERROR);
		}
		count = insertWipMoveMtl(warehouseEntryInfo,itemId);
		if (count < 1) {
			throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.WIP_MOVE_MTL_INSERT_ERROR);
		}
		return count;
	}

	@Override
	public ServiceData getItemVersionBySegment1(String segment1) {
		ServiceData<Object> ret = new ServiceData<>();
		ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
		List<String> versionList = mtlSystemItemsBRepository.getItemVersionBySegment1(segment1);
		if (CollectionUtils.isEmpty(versionList)){
			return ret;
		}
		ret.setBo(versionList.get(0));
		return ret;
	}
}
