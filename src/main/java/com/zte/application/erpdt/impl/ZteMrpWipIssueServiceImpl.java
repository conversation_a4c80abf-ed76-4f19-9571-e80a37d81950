/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt.impl;

import com.zte.application.erpdt.ZteMrpWipIssueService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.erpdt.ZteMrpWipIssue;
import com.zte.domain.model.erpdt.ZteMrpWipIssueRepository;
import com.zte.interfaces.assembler.ZteMrpWipIssueAssembler;
import com.zte.interfaces.dto.ZteMrpWipIssueDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.ERP1)
public class ZteMrpWipIssueServiceImpl implements ZteMrpWipIssueService {
    @Autowired
    private ZteMrpWipIssueRepository zteMrpWipIssueRepository;

    public void setZteMrpWipIssueRepository(ZteMrpWipIssueRepository zteMrpWipIssueRepository) {
        this.zteMrpWipIssueRepository = zteMrpWipIssueRepository;
    }

    /**
     * 增加实体数据
     *
     * @param record
     **/
    public void insertZteMrpWipIssue(ZteMrpWipIssue record) {
        zteMrpWipIssueRepository.insertZteMrpWipIssue(record);
    }

    /**
     * 增加实体数据
     *
     * @param records
     **/
    @Override
    public void insertZteMrpWipIssues(List<ZteMrpWipIssue> records) {
        zteMrpWipIssueRepository.insertBatch(records);
    }

    /**
     * 出库查询
     * @return ServiceData     */
    public ServiceData getOutWarehouseList(ZteMrpWipIssueDTO conditions) throws Exception {
        ServiceData serviceData = new ServiceData();
        RetCode retCode = new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        //校验交易类型
        if (StringHelper.isEmpty(conditions.getLookupType())){
            retCode.setMsg(MessageId.CONDITION_NOT_FOUND);
        }
        if (RetCode.SUCCESS_CODE.equals(retCode.getCode())){
            // 声明翻页模板
            Page<ZteMrpWipIssueDTO> pageInfo = new Page<>(conditions.getPage(), conditions.getRows());
            // 放入查询参数
            pageInfo.setParams(conditions);
            // 获取查询结果列表
            List<ZteMrpWipIssue> warehouse = zteMrpWipIssueRepository.getOutWarehouseQueryList(pageInfo);
            // 将ErpQuery类型转换成ZteMrpWipIssueDTO类型
            List<ZteMrpWipIssueDTO> warehouseList = ZteMrpWipIssueAssembler.toZteMrpWipIssueDTOList(warehouse);
            for (ZteMrpWipIssueDTO list : warehouseList) {
                list.setLookupType(conditions.getLookupType());
            }
            pageInfo.setRows(warehouseList);
            serviceData.setBo(pageInfo);
        }
        return serviceData;
    }

}


