/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-07-16
 * 修改历史 :
 *   1. [2019-07-16] 创建文件 by 6396000647
 **/
package com.zte.application.erpdt.impl;

import com.zte.application.erpdt.WipEntitiesService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.erpdt.WipEntitiesRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
@Service
@DataSource(DatabaseType.ERP)
public class WipEntitiesServiceImpl implements WipEntitiesService {

    @Autowired
    private WipEntitiesRepository wipEntitiesRepository;

    /**
     * get all record
     *
     * @return List<WipEntities>
     **/
    @Override
    public List<WipEntitiesGetDTO> selectErpSendEnitity(WipEntitiesSetDTO dto) {
        return wipEntitiesRepository.selectErpSendEnitity(dto);
    }

    /*
     * return money
     * */
    @Override
    public ErpMoneyDTO selectErpMoney(WipEntitiesSetDTO dto){
    	return wipEntitiesRepository.selectErpMoney(dto);
    }

    /**
     * 获取ERP完工数、预计投产日期
     */
    @Override
    public List<WipEntitiesDTO> selectMpsNetQty(String wipEntityName) {
        if (StringUtils.isEmpty(wipEntityName)) {
            return null;
        }
        return wipEntitiesRepository.selectMpsNetQty(wipEntityName);
    }

    /**
     * 获取erp可移动数量
     * @param wipEntityName
     * @return
     */
    @Override
    public WipOperationsDto getMovableQuantity(String wipEntityName) {
        return  wipEntitiesRepository.getMovableQuantity(wipEntityName);
    }

    /**
     * Gets sm report count.
     *
     * @param dto the dto
     * @return the sm report count
     */
    @Override
    public Integer getSmReportCount(WipEntitiesReportGetDTO dto) {
        return wipEntitiesRepository.getSmReportCount(dto);
    }

    /**
     * Gets sm report.
     *
     * @param dto the dto
     * @return the sm report
     */
    @Override
    public List<SmDailyStatisticReportDTO> getSmReport(WipEntitiesReportGetDTO dto) {
        return wipEntitiesRepository.getSmReport(dto);
    }

    /**
     * 获取ERP状态枚举
     *
     * @return the erp status
     */
    @Override
    public List<Map> getErpStatus() {
        return wipEntitiesRepository.getErpStatus();
    }

    /**
     * 根据任务号获取版本信息
     * @param dto
     * @return
     */
    @Override
    public List<PDMProductMaterialResultDTO> getBomVerByTaskNo(PDMProductMaterialResultDTO dto)throws Exception {
        if(dto == null){
            return new ArrayList<>();
        }
        List<String> entityNameList=dto.getEntityNameList();
        if(CollectionUtils.isEmpty(entityNameList)){
            return new ArrayList<>();
        }
        List<PDMProductMaterialResultDTO> resultDTOS=new ArrayList<>();
        List<List<String>> splistList= CommonUtils.splitList(entityNameList, NumConstant.NUM_HUNDRED);
        for(List<String> tempList:splistList){
            PDMProductMaterialResultDTO pdmProductMaterialResultDTO=new PDMProductMaterialResultDTO();
            pdmProductMaterialResultDTO.setEntityNameList(tempList);
            List<PDMProductMaterialResultDTO> pdmProductMaterialResultDTOList= wipEntitiesRepository.getBomVerByTaskNo(pdmProductMaterialResultDTO);
            if(CollectionUtils.isNotEmpty(pdmProductMaterialResultDTOList)){
                resultDTOS.addAll(pdmProductMaterialResultDTOList);
            }
        }
        return resultDTOS;
    }


    /**
     * 首备、低位、退料查ERP确认计划跟踪单状态
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public List<WipEntitiesDTO> getTaskNoStatusByErp(List<WipEntitiesDTO> list)throws Exception{
        return wipEntitiesRepository.getTaskNoStatusByErp(list);
    }


}
