package com.zte.application.erpdt.impl;

import com.zte.application.erpdt.ZteLmsMtlTransInterService;
import com.zte.domain.model.erpdt.ZteLmsMtlTransInterRepository;
import com.zte.interfaces.dto.ZteLmsMtlTransInterDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: panXu
 * @Date: 2020/5/18 14:58
 * @Description:
 */
@Service
@DataSource(DatabaseType.ERP1)
public class ZteLmsMtlTransInterServiceImpl implements ZteLmsMtlTransInterService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ZteLmsMtlTransInterRepository zteLmsMtlTransInterRepository;

    @Override
    public int insertZteLmsMtlTransInterBatch(List<ZteLmsMtlTransInterDTO> list) {
        int insertNum = 0;
        if (!CollectionUtils.isEmpty(list)) {
            insertNum = zteLmsMtlTransInterRepository.insertZteLmsMtlTransInterBatch(list);
            logger.info("写ERP子库存转移，插入ERP的子库存转移:{}", insertNum);
        }
        return insertNum;
    }
}
