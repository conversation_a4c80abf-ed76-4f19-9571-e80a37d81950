/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt;

import com.zte.domain.model.erpdt.ZteMrpWipIssue;
import com.zte.interfaces.dto.ZteMrpWipIssueDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
public interface ZteMrpWipIssueService {

    /**
     * 增加实体数据
     *
     * @param record
     **/
    void insertZteMrpWipIssue(ZteMrpWipIssue record);

    /**
     * 增加实体数据
     *
     * @param records
     **/
    void insertZteMrpWipIssues(List<ZteMrpWipIssue> records);


    ServiceData getOutWarehouseList(ZteMrpWipIssueDTO conditions) throws Exception;
}
