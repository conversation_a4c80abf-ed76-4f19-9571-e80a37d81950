/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt;

import com.zte.domain.model.erpdt.ZteLmsOrgTransInter;
import com.zte.interfaces.dto.ZteLmsOrgTransInterDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;


/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
public interface ZteLmsOrgTransInterService {

    /**
     * 增加实体数据
     *
     * @param record
     **/
    void insertZteLmsOrgTransInter(ZteLmsOrgTransInter record);

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    void insertZteLmsOrgTransInterSelective(ZteLmsOrgTransInter record);

    void updateZteLmsOrgTransInterById(ZteLmsOrgTransInter record);

    /**
     * 批量插入实体数据
     * @param list
     * @return
     */
    int insertZteLmsOrgTransInterBatch(List<ZteLmsOrgTransInterDTO> list);

    ServiceData getSubInventoryTransferList(ZteLmsOrgTransInterDTO conditions) throws Exception;

}
