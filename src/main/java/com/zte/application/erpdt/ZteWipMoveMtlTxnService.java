/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt;

import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.datawb.BoardOnlinelist;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxn;
import com.zte.interfaces.dto.ZteWipMoveMtlTxnDTO;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.List;

/**
 * // TODO 添加类/接口功能描述
 *
 * <AUTHOR>
 **/
public interface ZteWipMoveMtlTxnService {

    /**
     * 增加实体数据
     *
     * @param record
     **/
    void insertZteWipMoveMtlTxn(ZteWipMoveMtlTxn record);

    /**
     * 有选择性的增加实体数据
     *
     * @param record
     **/
    void insertZteWipMoveMtlTxnSelective(ZteWipMoveMtlTxn record);

    void deleteZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record);

    void updateZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record);

    void selectZteWipMoveMtlTxnById(ZteWipMoveMtlTxn record);

    ServiceData getWarehouseQueryList(ZteWipMoveMtlTxnDTO conditions) throws Exception;
    
    public int writeErpTransaction(WarehouseEntryInfo warehouseEntryInfo) throws Exception;
    
    /**
     * 回写ERP并更新imu
     * @param warehouseEntryInfo
     * @param boards
     * @throws Exception
     */
	void writeErpAndUpdateImu(WarehouseEntryInfo warehouseEntryInfo, List<BoardOnlinelist> boards) throws Exception;

    void updateImuForSubCard(List<BoardOnlinelist> boards) throws Exception;

    void writeErpForSubCard(WarehouseEntryInfo warehouseEntryInfo) throws Exception;

    int onlyWriteErp(WarehouseEntryInfo warehouseEntryInfo) throws Exception;

    /**
     * 通过物料代码获取物料版本
     * @param segment1
     * @return
     */
    ServiceData getItemVersionBySegment1(String segment1);

    /**
     * 回写erp
     * @param warehouseEntryInfo
     * @return 0 未找到物料ID，1.插入入库表失败  2。插入出库表失败 3.入库表出库表都插入成功
     * @throws Exception
     */
    int writeErpTransactionNew(WarehouseEntryInfo warehouseEntryInfo) throws Exception;

    /**
     * 回写erp-子卡扫描专用接口-支持幂等
     * @param warehouseEntryInfo
     * @throws Exception
     */
    void writeErpIdempotent(WarehouseEntryInfo warehouseEntryInfo) throws Exception;

}
