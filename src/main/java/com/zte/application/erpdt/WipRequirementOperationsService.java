/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.application.erpdt;

import java.util.List;
import java.util.Map;

import com.zte.domain.model.erpdt.WipRequirementOperations;
import com.zte.interfaces.dto.ZteWipMoveMtlTxnDTO;
import com.zte.itp.msa.core.model.ServiceData;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public interface WipRequirementOperationsService {

    /**
     * 增加实体数据
     * 
     * @param record
     **/
    void insertWipRequirementOperations(WipRequirementOperations record);

    /**
     * 有选择性的增加实体数据
     * 
     * @param record
     **/
    void insertWipRequirementOperationsSelective(WipRequirementOperations record);



    List<WipRequirementOperations> selectWipRequirementOperationsByWipEntityId(Map<String, Object> record);

    /**
     * ERP出入库查询
     */
    ServiceData getErpWarehousing(ZteWipMoveMtlTxnDTO dto);

    /**
     * 根据 SourceLineId 查询 apps.mtl_material_transactions 确认幂等信息
     * @param dto
     * @return
     * @throws Exception
     */
    List<ZteWipMoveMtlTxnDTO> getErpMtlInfoBySourceLineId(ZteWipMoveMtlTxnDTO dto)throws Exception;

    /**
     * 根据 SourceLineId 查询 apps.mtl_material_transactions 确认幂等信息--批量
     * @param dto
     * @return
     * @throws Exception
     */
    List<ZteWipMoveMtlTxnDTO> getErpMtlInfoBySourceLineIdBatch(ZteWipMoveMtlTxnDTO dto)throws Exception;
}