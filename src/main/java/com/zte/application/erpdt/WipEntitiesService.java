/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-07-16
 * 修改历史 :
 *   1. [2019-07-16] 创建文件 by 6396000647
 **/
package com.zte.application.erpdt;

import com.zte.interfaces.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 添加类/接口功能描述
 *
 * <AUTHOR>
 */
public interface WipEntitiesService {

	/**
	 * get all record
	 *
	 * @param dto the dto
	 * @return List<WipEntities> list
	 */
	List<WipEntitiesGetDTO> selectErpSendEnitity(WipEntitiesSetDTO dto);

	/**
	 * Select erp money erp money dto.
	 *
	 * @param dto the dto
	 * @return the erp money dto
	 */
	ErpMoneyDTO selectErpMoney(WipEntitiesSetDTO dto);

	/**
	 * 获取ERP完工数、计划投产时间
	 *
	 * @param wipEntityName the wip entity name
	 * @return the list
	 */
	List<WipEntitiesDTO> selectMpsNetQty(String wipEntityName);

	/**
	 * Gets movable quantity.
	 *
	 * @param wipEntityName the wip entity name
	 * @return the movable quantity
	 */
	WipOperationsDto getMovableQuantity(String wipEntityName);

	/**
	 * Gets sm report count.
	 *
	 * @param dto the dto
	 * @return the sm report count
	 */
	Integer getSmReportCount(WipEntitiesReportGetDTO dto);

	/**
	 * Gets sm report.
	 *
	 * @param dto the dto
	 * @return the sm report
	 */
	List<SmDailyStatisticReportDTO> getSmReport(WipEntitiesReportGetDTO dto);

	List<PDMProductMaterialResultDTO> getBomVerByTaskNo(PDMProductMaterialResultDTO dto) throws Exception;

	/**
	 * Gets erp status.
	 *
	 * @return the erp status
	 */
	List<Map> getErpStatus();


	/**
	 * 首备、低位、退料查ERP确认计划跟踪单状态
	 * @param dto
	 * @return
	 * @throws Exception
	 */
	List<WipEntitiesDTO> getTaskNoStatusByErp(List<WipEntitiesDTO> list)throws Exception;
}
