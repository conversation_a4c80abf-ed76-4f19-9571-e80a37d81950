package com.zte.application.erpdt;

import com.zte.interfaces.dto.CFItemSingleDosageDTO;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.interfaces.dto.ItemListEntityForAuxDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.Page;

import java.util.List;
import java.util.Map;

/**
 *  物料清单查询
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-05 10:16:54
 */
public interface ItemListService {

    Page<ItemListEntityDTO> pageList(ItemListEntityDTO record) throws Exception;

    List<String> batchSelectByTaskNoList(List<String> taskNoList) throws Exception;

    List<String> getTaskNoList(Map<String,Object> map) throws Exception;

    long checkItemNoAndTask(ItemListEntityDTO record) throws Exception;

    ServiceData<CFItemSingleDosageDTO> getItemSingleDosage(CFItemSingleDosageDTO record) throws Exception;

    /**
     * 获取单个物料已发数量、需求数量
     *
     * @param taskNo 任务
     * @param itemNo 物料代码
     * @return
     */
    ItemListEntityDTO getItemiLstTaskItem(String taskNo, String itemNo);

    List<ItemListEntityForAuxDTO> getItemListByTaskList(List<String> taskNoList);

    /**
     * 获取任务辅料用量
     *
     * @param taskNo
     * @return
     */
    List<CfInventoryDTO> getItemUsageCount(String taskNo);

    List<ItemListEntityDTO> getListByTaskList(List<String> taskNoList);
}

