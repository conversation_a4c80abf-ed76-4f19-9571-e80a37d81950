package com.zte.application;


import com.zte.domain.model.ArchiveTaskConfig;

/**
 * @ClassName: ArchiveExtractService
 * @Description: 归档数据同步接口
 * @author: 6407003374
 */
public interface ArchiveExtractService {

    /**
     * 同步待归档数据方法
     */
    void extractArchiveData();

    /**
     * 同步待归档数据
     * @param taskConfig 同步配置信息
     */
    void syncArchiveData(ArchiveTaskConfig taskConfig);

    /**
     * 根据ID查询归档类型
     * @param id
     * @return
     */
    ArchiveTaskConfig selectById(String id);
}
