package com.zte.application;

import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDetailDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/9
 * @description : 装箱单退库申请归档服务接口
 */
public interface ArchiveMesEntryBillsDataService {

    /**
     * 根据入库单号查询装箱单退库申请归档DTO
     * @param entryNumber 入库单号
     * @return 装箱单退库申请归档DTO
     */
    ArchiveMesEntryBillsDTO getByEntryNumber(String entryNumber);

    /**
     * 根据入库单号查询入库详情信息
     * @param entryNumber 入库单号
     * @return 装箱单退库申请归档DTO
     */
    List<ArchiveMesEntryBillsDTO> getListByEntryNumber(String entryNumber);

    /**
     * 根据时间范围查询归档数据
     * @param archiveQueryParamDTO 归档参数
     * @return 归档数据
     */
    Page<ArchiveMesEntryBillsDTO> getEntryBillsByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO);

    /**
     * 根据入库单号查询入库详细列表
     * @param entryBillId 入库单号
     * @return 入库详细列表
     */
    List<ArchiveMesEntryBillsDetailDTO> getDetailListByEntryBillId(String entryBillId);
}
