package com.zte.application;

import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.springbootframe.common.model.Page;

/**
 * <p>
 *  归档业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface ArchiveBusinessService {

    /**
     * 根据业务类型归档
     * @param taskSendArchive 归档任务实体
     * @return 归档业务参数
     * @throws Exception 异常
     */
    ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception;

    /**
     * 获取待归档同步数据
     * @param archiveQueryParamDTO 查询条件
     * @return
     */
    Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO);
}

