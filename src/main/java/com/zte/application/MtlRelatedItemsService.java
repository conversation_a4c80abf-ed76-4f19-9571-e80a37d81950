package com.zte.application;

import com.zte.springbootframe.common.model.Page;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;

import java.util.List;
import java.util.Map;

/**
 * 物料替代关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-06-01 09:12:50
 */
public interface MtlRelatedItemsService {

     List<MtlRelatedItemsEntityDTO> getList(MtlRelatedItemsEntityDTO record) throws Exception;

     List<MtlRelatedItemsEntityDTO> getItemInfoList(MtlRelatedItemsEntityDTO record) throws Exception;
}

