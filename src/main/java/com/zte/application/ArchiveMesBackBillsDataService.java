package com.zte.application;

import com.zte.interfaces.dto.ArchiveMesBackBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/15
 * @description : 装箱单退库申请归档服务接口
 */
public interface ArchiveMesBackBillsDataService {

    /**
     * 根据时间范围查询分页数据
     * @param archiveQueryParamDTO 归档参数
     * @return 分页数据
     */
    Page<ArchiveMesBackBillsDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO);

    /**
     * 根据退库单号查询装箱单退库申请归档DTO
     * @param billNo 退库单号
     * @return 装箱单退库申请归档DTO
     */
    ArchiveMesBackBillsDTO getByBackNumber(String billNo);

    /**
     * 根据退困单号查询退库申请详情
     * @param backBillId 退困单Id
     * @return
     */
    List<ArchiveMesBackBillsDTO> getListByBackBillId(String backBillId);
}
