package com.zte.application.impl;

import com.zte.application.ArchiveMesEntryBillsDataService;
import com.zte.domain.model.ArchiveMesEntryBillsRepository;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDetailDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/11
 * @description : 装箱单入库申请归档服务接口实现
 */
@Service
public class ArchiveMesEntryBillsDataServiceImpl implements ArchiveMesEntryBillsDataService {

    @Autowired
    ArchiveMesEntryBillsRepository repository;

    @Override
    public ArchiveMesEntryBillsDTO getByEntryNumber(String entryNumber) {
        if(StringUtils.isBlank(entryNumber)){
            return null;
        }
        return repository.getByEntryNumber(entryNumber);
    }

    @Override
    public List<ArchiveMesEntryBillsDTO> getListByEntryNumber(String entryNumber) {
        if(StringUtils.isBlank(entryNumber)){
            return Collections.emptyList();
        }
        return repository.getListByEntryNumber(entryNumber);
    }

    @Override
    public Page<ArchiveMesEntryBillsDTO> getEntryBillsByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO) {
        if(null == archiveQueryParamDTO || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }

        Page<ArchiveMesEntryBillsDTO> page = new Page<>(archiveQueryParamDTO.getPage(), archiveQueryParamDTO.getRows());
        page.setParams(archiveQueryParamDTO);
        List<ArchiveMesEntryBillsDTO> rows = repository.getEntryBillsByDateRange(page);
        page.setRows(rows);
        return page;
    }

    @Override
    public List<ArchiveMesEntryBillsDetailDTO> getDetailListByEntryBillId(String entryNumber) {
        if(StringUtils.isBlank(entryNumber)){
            return Collections.emptyList();
        }
        return repository.getDetailListByEntryBillId(entryNumber);
    }

}
