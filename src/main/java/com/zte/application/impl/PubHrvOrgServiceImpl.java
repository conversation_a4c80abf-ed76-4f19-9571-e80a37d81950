package com.zte.application.impl;

import com.zte.application.PubHrvOrgService;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
@Service
@DataSource(value = DatabaseType.MES)
public class PubHrvOrgServiceImpl implements PubHrvOrgService {

    @Autowired
    PubHrvOrgRepository repository;

    @Override
    public PubHrvOrg getPubHrvOrgByUserNameId(String userNameId) {
        PubHrvOrg pubHrvOrg = null;
        if(StringUtils.isNotBlank(userNameId)){
            pubHrvOrg = repository.getPubHrvOrgByUserId(userNameId);
        }
        if(null == pubHrvOrg){
            pubHrvOrg = new PubHrvOrg();
            pubHrvOrg.setOrgFullName(ArchiveConstant.DEFAULT_DEPT_NAME);
            pubHrvOrg.setOrgNo(ArchiveConstant.DEFAULT_DEPT_CODE);
            pubHrvOrg.setUserId(ArchiveConstant.DEFAULT_DEPT_CODE);
            pubHrvOrg.setUserName(ArchiveConstant.DEFAULT_DEPT_NAME);
            pubHrvOrg.setUserNameId(ArchiveConstant.DEFAULT_DEPT_NAME+ArchiveConstant.DEFAULT_DEPT_CODE);
        }
        return pubHrvOrg;
    }

    @Override
    public PubHrvOrg getPubHrvOrgByUserNamePlus(String userName) {
        PubHrvOrg pubHrvOrg = null;
        if(StringUtils.isNotBlank(userName)){
            pubHrvOrg = repository.getPubHrvOrgByUserNamePlus(userName);
        }
        if(null == pubHrvOrg){
            pubHrvOrg = new PubHrvOrg();
            pubHrvOrg.setOrgFullName(ArchiveConstant.DEFAULT_DEPT_NAME);
            pubHrvOrg.setOrgNo(ArchiveConstant.DEFAULT_DEPT_CODE);
            pubHrvOrg.setUserId(ArchiveConstant.DEFAULT_DEPT_CODE);
            pubHrvOrg.setUserName(ArchiveConstant.DEFAULT_DEPT_NAME);
            pubHrvOrg.setUserNameId(ArchiveConstant.DEFAULT_DEPT_NAME+ArchiveConstant.DEFAULT_DEPT_CODE);
        }
        return pubHrvOrg;
    }
}
