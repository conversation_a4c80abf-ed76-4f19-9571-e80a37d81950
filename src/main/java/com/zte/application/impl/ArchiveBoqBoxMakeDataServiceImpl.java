package com.zte.application.impl;

import com.zte.application.ArchiveBoqBoxMakeDataService;
import com.zte.common.enums.BoxMakeTypeEnum;
import com.zte.domain.model.ArchiveBoqBoxMakeDataRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/7
 * @description : BOQ-BOM装箱单拟制归档数据查询服务实现类
 */
@Service
@DataSource(DatabaseType.WMSPRODLMS)
public class ArchiveBoqBoxMakeDataServiceImpl implements ArchiveBoqBoxMakeDataService {

    @Autowired
    ArchiveBoqBoxMakeDataRepository repository;

    @Override
    public ArchiveBoqBoxMakeDTO getByEntityName(String billNo) {
        if(StringUtils.isBlank(billNo)){
            return null;
        }
        return repository.getByEntityName(billNo);
    }

    @Override
    public List<ArchiveBoqUnitBoxMakeDTO> getArchiveUnitBoxMakeByBillId(String billId) {
        if(StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getArchiveUnitBoxMakeByBillId(billId);
    }

    @Override
    public List<ArchiveBoqBoxMakeItemDTO> getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId) {
        if(StringUtils.isBlank(organizationId) || StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(organizationId,billId);
    }

    @Override
    public List<ArchiveBoxMakeItemDTO> getArchiveBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId) {
        if(StringUtils.isBlank(organizationId) || StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getArchiveBoxMakeItemByOrganizationIdAndBillId(organizationId,billId);
    }

    @Override
    public List<ArchiveBoqBoxMakeItemDTO> getArchiveEuBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId) {
        if(StringUtils.isBlank(organizationId) || StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getArchiveEuBoxMakeItemByOrganizationIdAndBillId(organizationId,billId);
    }

    @Override
    public List<ArchiveEuUnitBoxMakeDTO> getArchiveEuUnitBoxMakeByBillid(String billId) {
        if(StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getArchiveEuUnitBoxMakeByBillid(billId);
    }

    @Override
    public Page<ArchiveBoqBoxMakeDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO, String boxMakeType) {
        if(null == archiveQueryParamDTO || StringUtils.isBlank(boxMakeType) || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }
        Page<ArchiveBoqBoxMakeDTO> page = new Page<>(archiveQueryParamDTO.getPage(),archiveQueryParamDTO.getRows());

        ArchiveBoqBoxMakeQueryDTO queryDTO = new ArchiveBoqBoxMakeQueryDTO();
        queryDTO.setStartDate(archiveQueryParamDTO.getStartDate());
        queryDTO.setEndDate(archiveQueryParamDTO.getEndDate());
        queryDTO.setBoxType(BoxMakeTypeEnum.getNameByCode(boxMakeType));
        page.setParams(queryDTO);

        page.setRows(repository.getPageByDateRange(page));
        return page;
    }

    @Override
    public ArchiveBoqBoxMakeItemDTO getSecondItem(String configDetailId) {
        if(StringUtils.isBlank(configDetailId)){
            return new ArchiveBoqBoxMakeItemDTO();
        }
        return repository.getSecondItem(configDetailId);
    }

    @Override
    public List<ArchiveBoqBoxMakeItemDTO> getFourMakeItem(String billItemId, String organizationId) {
        if(StringUtils.isBlank(organizationId) || StringUtils.isBlank(billItemId)){
            return Collections.emptyList();
        }
        return repository.getFourMakeItem(organizationId,billItemId);
    }

    @Override
    public List<ArchiveBoqBoxMakeItemDTO> getEUAdnBoqMakeItem(String organizationId, String billId) {
        if(StringUtils.isBlank(organizationId) || StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        //查询箱物料列表
        List<ArchiveBoqBoxMakeItemDTO> dtos = getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(organizationId,billId);

        //所有三层四层物料代码集合
        List<ArchiveBoqBoxMakeItemDTO> allDtocs = new ArrayList<>();

        dtos.forEach(o->{
            //查询二层物料代码和二层物料名称
            ArchiveBoqBoxMakeItemDTO bbm = getSecondItem(o.getConfigDetailId());
            o.setSecondItemName(bbm.getItemName());
            o.setSecondItemCode(bbm.getItemCode());
            allDtocs.add(o);
            List<ArchiveBoqBoxMakeItemDTO> fourItems = getFourMakeItem(o.getBillItemId(),o.getOrganizationId());
            fourItems.forEach(item->{
                item.setSecondItemCode(bbm.getItemCode());
                item.setSecondItemName(bbm.getItemName());
            });
            allDtocs.addAll(fourItems);
        });

        return allDtocs;
    }



}
