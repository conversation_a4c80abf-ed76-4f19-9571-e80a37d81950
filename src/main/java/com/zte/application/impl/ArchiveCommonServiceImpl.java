package com.zte.application.impl;


import cn.hutool.extra.ssh.Sftp;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveCommonService;
import com.zte.application.MesGetDictInforService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveErrorEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ArchiveConstant;
import com.zte.common.utils.ExportUtils;
import com.zte.common.utils.SftpConfig;
import com.zte.domain.model.ArchiveBillFileRelation;
import com.zte.domain.model.ArchiveBillFileRelationRepository;
import com.zte.domain.model.ArchiveSendLog;
import com.zte.domain.model.ArchiveSendLogRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.infrastructure.feign.ArchiveClient;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.CloudDiskHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 * <p>
 *  归档公共类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Slf4j
@Service
public class ArchiveCommonServiceImpl implements ArchiveCommonService {

    private static final String CHARSET = "UTF-8";
    private final static String WINDOW_TOOL_PATH = "D:/wkhtmltopdf/bin/wkhtmltopdf.exe";
    private final static String LINUX_TOOL_PATH = "/usr/local/bin/wkhtmltopdf";
    private static int len_seven=7;
    private static int len_four=4;

    @Autowired
    private CloudDiskHelper cloudDiskHelper;

    @Autowired
    ArchiveFileProperties archiveFileProperties ;

    @Resource
    private ArchiveClient archiveClient;

    @Autowired
    private ArchiveBillFileRelationRepository archiveBillFileRelationRepository;

    @Autowired
    private ArchiveSendLogRepository sendArchiveLogRepository;

    @Autowired
    private MesGetDictInforService mesGetDictInforService;

    @Autowired
    private SftpConfig sftpConfig;



    /**
     * 保存文件到文档云
     * @param pathName  上传的文件的路径
     * @param taskSendArchive 业务单据归档任务
     * @param empNo empNo
     * @return
     */
    @Override
    public AttachmentUploadVo uploadFileTo(String pathName, ArchiveTaskSend taskSendArchive, String empNo){
        File file = new File(pathName);
        String fileName=file.getName();
        try (FileInputStream ins=new FileInputStream(file);){
            //上传pdf文档到文档云
            String fileKey = cloudDiskHelper.fileUpload(pathName, empNo, CloudDiskHelper.MAX_RETRY_TIMES);

            if(StringUtils.isEmpty(fileKey)){
                throw new RouteException(ArchiveErrorEnum.ATTACH_GET_FAIL.getCode());
            }
            AttachmentUploadVo attachment = new AttachmentUploadVo();
            attachment.setDiskKey(fileKey);
            attachment.setFileName(fileName);
            attachment.setFileSize(file.length());
            //保存pdf到附件表
            ArchiveBillFileRelation archiveBillFileRelation =new ArchiveBillFileRelation();
            archiveBillFileRelation.setTaskId(taskSendArchive.getTaskId());
            archiveBillFileRelation.setBillId(StringUtils.defaultString(taskSendArchive.getBillId()));
            archiveBillFileRelation.setBillNo(taskSendArchive.getBillNo());
            archiveBillFileRelation.setClouddiskPdfId(fileKey);
            archiveBillFileRelation.setCreatedBy(empNo);
            archiveBillFileRelation.setCreationDate(new Date());
            archiveBillFileRelation.setLastUpdatedBy(empNo);
            archiveBillFileRelation.setLastUpdateDate(new Date());
            archiveBillFileRelationRepository.insert(archiveBillFileRelation);
            return attachment;
        } catch (Exception e) {
            log.error("ArchiveCommonServiceImpl--method【uploadFileTo】   exception:{}{}", e.getMessage(), e);
            throw new RouteException(ArchiveErrorEnum.UPLOAD_ATTACH_FAIL.getCode());
        }
    }



    @Override
    public AttachmentUploadVo createExcelAndUpload(ArchiveTaskSend taskSendArchive, String excelFileName, LinkedHashMap<String, String> sheetHeadMap, List dataList) throws Exception {
        String excelFilePath = new StringBuffer(ArchiveConstant.TEMP_FILE_PATH).append("/").append(excelFileName).append(".xls").toString();

        try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook();) {
            ExportUtils.fillSheetWithOneHead(hssfWorkbook, excelFileName, sheetHeadMap, dataList, new HashMap<>(8));
            writeExcelFlash(excelFilePath, hssfWorkbook);
        } catch (IOException e) {
            log.error("createExcelAndUpload error fileName:{}{}", excelFileName, e);
            throw new RouteException(ArchiveErrorEnum.ARCHIVE_FAIL.getCode());
        }

        AttachmentUploadVo attachmentUploadVo = uploadFileTo(excelFilePath, taskSendArchive, archiveFileProperties.getEmpNo());
        return attachmentUploadVo;
    }

    private void writeExcelFlash(String excelFilePath, HSSFWorkbook hssfWorkbook) throws IOException {
        if (!new File(ArchiveConstant.TEMP_FILE_PATH).exists()) {
            log.info("mkdirs {}", ArchiveConstant.TEMP_FILE_PATH);
            new File(ArchiveConstant.TEMP_FILE_PATH).mkdirs();
        }
        try(OutputStream os = new FileOutputStream(excelFilePath);){
            hssfWorkbook.write(os);
        }
    }

    @Override
    public AttachmentUploadVo createExcelAndUpload(ArchiveTaskSend taskSendArchive, String excelFileName, LinkedHashMap<String, LinkedHashMap<String, String>> sheetHeadMap, LinkedHashMap<String, List> dataListMap) throws Exception {
        if (sheetHeadMap == null || dataListMap == null) {
            throw new RouteException("createExcelAndUpload in parameter is null");
        }
        String excelFilePath = new StringBuffer(ArchiveConstant.TEMP_FILE_PATH).append("/").append(excelFileName).append(".xls").toString();

        try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook();) {
            for (Map.Entry<String, LinkedHashMap<String, String>> entrySet : sheetHeadMap.entrySet()) {
                String key = entrySet.getKey();
                LinkedHashMap<String, String> value =  entrySet.getValue();
                if (key == null || value == null) {
                    continue;
                }
                List list = dataListMap.get(key);
                if (list == null) {
                    list = new LinkedList();
                }
                ExportUtils.fillSheetWithOneHead(hssfWorkbook, key, value , list, new HashMap<>(8));
            }
            writeExcelFlash(excelFilePath, hssfWorkbook);
        } catch (IOException e) {
            log.error("createExcelAndUpload2 error fileName:{}{}", excelFileName, e);
            throw new RouteException(ArchiveErrorEnum.ARCHIVE_FAIL.getCode());
        }

        AttachmentUploadVo attachmentUploadVo = uploadFileTo(excelFilePath, taskSendArchive, archiveFileProperties.getEmpNo());
        return attachmentUploadVo;
    }


    /**
     * 发送数据到归档系统
     * @param archiveItem
     * @param taskSendArchive
     * @return
     * @throws Exception
     */
    @Override
    public ServiceData sendDataToArchive(ArchiveReq.ArchiveItem archiveItem, ArchiveTaskSend taskSendArchive) {
        String params="",result="";
        try {
            ArchiveReq archive = new ArchiveReq();
            archive.setCreatedBy(archiveFileProperties.getEmpNo());
            archive.setCreatedByName(archiveFileProperties.getEmpNo());
            archive.setFromSystem(archiveFileProperties.getCloudServiceName());
            archive.setMappingId(archiveFileProperties.getMapping());
            List<ArchiveReq.ArchiveItem> archiveItems = new ArrayList<>();
            archiveItems.add(archiveItem);
            archive.setItems(archiveItems);
            params=JSONObject.toJSONString(archive);
            log.info("发送归档数据：{}",params );
            ServiceData serviceData = archiveClient.archiveFile(archive);
            result=JSONObject.toJSONString(serviceData);
            log.info("发送归档请求返回的结果：{}", result);
            return serviceData;
        }catch (Exception e){
            log.error("归档接口访问失败，网络故障或无权限！", e);
            throw new RouteException(ArchiveErrorEnum.ARCHIVE_ACCESS_FAIL.getMessage());
        }finally{
            insertSendArchiveLog(taskSendArchive,archiveItem,params,result);
        }

    }


    private void insertSendArchiveLog(ArchiveTaskSend taskSendArchive,ArchiveReq.ArchiveItem archiveItem,String params,String result){
        try {
            //发送记录写入日志表
            ArchiveSendLog sendArchiveLog = new ArchiveSendLog();
            sendArchiveLog.setTaskId(taskSendArchive.getTaskId());
            sendArchiveLog.setBusinessId(archiveItem.getBusinessId());
            sendArchiveLog.setBillNo(taskSendArchive.getBillNo());
            sendArchiveLog.setParams(params);
            sendArchiveLog.setResults(result);
            sendArchiveLog.setUrl(archiveFileProperties.getUrl() + ArchiveConstant.ARCHIVE_API_URL);
            sendArchiveLog.setCreationDate(new Date());
            sendArchiveLog.setCreatedBy(archiveFileProperties.getEmpNo());
            sendArchiveLog.setLastUpdateDate(new Date());
            sendArchiveLog.setLastUpdatedBy(archiveFileProperties.getEmpNo());
            sendArchiveLog.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            sendArchiveLogRepository.insert(sendArchiveLog);
        }catch (Exception e){
            log.error("发送记录保存日志失败,{},{},{},{}", archiveItem.getBusinessId(),taskSendArchive.getBillNo(),params,result,e);
        }
    }

    public AttachmentUploadVo downloadFromFtpAndUpload(ArchiveTaskSend taskSendArchive, String fileName, String ftpFilePath) {
        checkDirExist();
        String localFilePath = ArchiveConstant.TEMP_FILE_PATH + "/" + fileName;
        File file = new File(localFilePath);
        Sftp sftp = sftpConfig.createSftp();
        try {
            sftp.download(ftpFilePath, file);
            log.info("{}文件下载成功", fileName);
        } finally {
            if (sftp != null) {
                sftp.close();
            }
        }
        AttachmentUploadVo attachmentUploadVo = uploadFileTo(localFilePath, taskSendArchive, archiveFileProperties.getEmpNo());
        return attachmentUploadVo;
    }

    private void checkDirExist() {
        if (!new File(ArchiveConstant.TEMP_FILE_PATH).exists()) {
            log.info("mkdirs {}", ArchiveConstant.TEMP_FILE_PATH);
            new File(ArchiveConstant.TEMP_FILE_PATH).mkdirs();
        }
    }

    @Override
    public int lock(String lookUpCode) {
        log.info("try lock{0}",lookUpCode);
        Map<String, String> params = new HashMap<>();
        params.put("lookUpCode", lookUpCode);
        params.put("preVal", "0");
        params.put("curVal", "1");
        return mesGetDictInforService.updateArchiveFlag(params);
    }

    @Override
    public int unlock(String lookUpCode) {
        log.info("try unlock{0}",lookUpCode);
        Map<String, String> params = new HashMap<>();
        params.put("lookUpCode", lookUpCode);
        params.put("preVal", "1");
        params.put("curVal", "0");
        return mesGetDictInforService.updateArchiveFlag(params);
    }

    @Override
    public Integer getDicDescriptionByCode(String lookUpCode) {
        String description = mesGetDictInforService.getDicDescription(lookUpCode);
        return description == null ? null : Integer.parseInt(description);
    }



}
