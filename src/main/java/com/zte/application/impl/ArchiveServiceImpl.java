package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ArchiveConstant;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ArchiveErrorEnum;
import com.zte.common.enums.ArchiveStatusEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.interfaces.dto.CallbackArchiveFileReq;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ArchiveSendLog;
import com.zte.domain.model.ArchiveSendLogRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.ArchiveTaskSendRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FileSystemUtils;

import java.io.File;
import java.util.*;

/**
 * <p>
 *  归档实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Slf4j
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveServiceImpl implements ArchiveService {

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveTaskSendRepository taskSendArchiveRepository;

    @Autowired
    private ArchiveSendLogRepository sendArchiveLogRepository;



    @Override
    public void sendArchiveTask(String taskType) {
        if (archiveCommonService.lock(Constant.LOOKUP_CODE_824005000002) != ArchiveConstant.INTEGER_1) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_TASK_EXECUTING);
        }
        try {
            archiveClearFile(ArchiveConstant.TEMP_FILE_PATH);
            Integer maxRetryCount = archiveCommonService.getDicDescriptionByCode(Constant.LOOKUP_CODE_824005000001);
            Date scheduleDate = new Date();
            //分页查询
            Page<ArchiveTaskSend> pageInfo = getArchiveTaskSendPage(scheduleDate, maxRetryCount, taskType, ArchiveConstant.INTEGER_1);
            //总页数
            int totalPage = pageInfo.getTotalPage();
            log.info("总的页数:{},记录数:{}", totalPage, pageInfo.getTotal());
            int curPage = 1;
            while (curPage <= totalPage) {
                List<ArchiveTaskSend> taskSendArchiveList = pageInfo.getRows();
                if (CollectionUtils.isEmpty(taskSendArchiveList)) {
                    return;
                }
                taskSendArchiveList.stream().forEach(taskSendArchive -> sendSignArchiveTask(taskSendArchive));
                log.info("当前页:{}", curPage);
                if (!ArchiveConstant.INTEGER_1.equals(archiveCommonService.getDicDescriptionByCode(Constant.LOOKUP_CODE_824005000003))) {
                    log.info("是否允许执行归档任务标识为否...");
                    break;
                }
                curPage++;
                pageInfo = getArchiveTaskSendPage(scheduleDate, maxRetryCount, taskType, ArchiveConstant.INTEGER_1);
            }
        } catch (Exception ex) {
            log.error("sendArchiveTask error {}", ex);
        } finally {
            archiveClearFile(ArchiveConstant.TEMP_FILE_PATH);
            archiveCommonService.unlock(Constant.LOOKUP_CODE_824005000002);
        }
    }

    /**
     * 分页查询
     * @param scheduleDate 计划日期
     * @param currPage 当前页
     * @return 分页信息
     */
    private Page<ArchiveTaskSend> getArchiveTaskSendPage(Date scheduleDate,Integer maxRetryCount,String taskType,Integer currPage) {
        Page<ArchiveTaskSend> pageInfo = new Page<ArchiveTaskSend>(currPage,ArchiveConstant.PAGE_SIZE);
        Map<String,Object> params = new HashMap<>();
        params.put("scheduleDate",scheduleDate);
        params.put("maxRetryCount",maxRetryCount);
        params.put("taskType",taskType);
        pageInfo.setParams(params);
        List<ArchiveTaskSend> list = taskSendArchiveRepository.selectTaskSendArchive(pageInfo);
        pageInfo.setRows(list);
        return pageInfo;
    }



    @Override
    public void sendSignArchiveTask(ArchiveTaskSend taskSendArchive) {
        try {
            //首先将任务根据task_id,version更新为执行中，version+1，如果返回1，则继续执行，否则返回
            if (taskSendArchiveRepository.updateTaskSendArchiveVersion(taskSendArchive) != 1) {
                return;
            }
            //根据类型获取归档数据
            ArchiveBusinessService archiveBusinessService = (ArchiveBusinessService) SpringContextUtil.getBean(ArchiveBusinessTypeEnum.getServiceNameByCode(taskSendArchive.getTaskType()));
            if (archiveBusinessService == null) {
                return;
            }
            ArchiveReq.ArchiveItem archiveItem = archiveBusinessService.archive(taskSendArchive);
            if (archiveItem == null) {
                //更新为执行异常，任务完成时间，重试次数,异常信息
                updateTaskStatus(taskSendArchive, ExecuteStatusEnum.EXECUTE_FAIL.getCode(), new Date(), 1, ArchiveErrorEnum.ARCHIVE_FAIL.getCode());
                return;
            }
            ServiceData result = archiveCommonService.sendDataToArchive(archiveItem, taskSendArchive);
            if (!RetCode.SUCCESS_CODE.equals(result.getCode().getCode())) {
                //更新为执行异常，任务完成时间，重试次数,异常信息
                updateTaskStatus(taskSendArchive, ExecuteStatusEnum.EXECUTE_FAIL.getCode(), new Date(), 1, ArchiveErrorEnum.ARCHIVE_SEND_FAIL.getCode());
                return;
            }
            //否则更新为执行成功，任务完成时间，重试次数重置为0,异常信息重置为空
            updateTaskStatus(taskSendArchive, ExecuteStatusEnum.EXECUTE_SUCCESS.getCode(), new Date(), 0, "");
        } catch (Exception ex) {
            log.error("sendSignArchiveTask error {},{},{}", taskSendArchive.getTaskId(), taskSendArchive.getBillNo(), ex);
            //任务更新为执行异常，任务完成时间，重试次数,异常信息
            updateTaskStatus(taskSendArchive, ExecuteStatusEnum.EXECUTE_FAIL.getCode(), new Date(), 1, getErrorMsg(ex));
        }
    }

    private String getErrorMsg(Exception ex) {
        if (ex instanceof MesBusinessException) {
            MesBusinessException mesBusinessException = (MesBusinessException) ex;
            return mesBusinessException.getExMsgId();
        }
        return ex.getMessage() == null ? "null" : ex.getMessage();
    }

    private void updateTaskStatus(ArchiveTaskSend taskSendArchive, String status, Date completeDate, int count, String errorMessage) {
        int retryCount = count == 0 ? 0 : taskSendArchive.getRetryCount() == null ? count : taskSendArchive.getRetryCount() + count;
        taskSendArchive.setStatus(status);
        taskSendArchive.setCompleteDate(completeDate);
        taskSendArchive.setLastUpdatedBy(archiveFileProperties.getEmpNo());
        taskSendArchive.setRetryCount(retryCount);
        taskSendArchive.setErrorMessage(errorMessage);
        taskSendArchiveRepository.updateTaskSendArchiveStatus(taskSendArchive);
    }

    private void updateArchivingStatus(ArchiveTaskSend taskSendArchive, String status, Date completeDate, String errorMessage) {
        int count = ArchiveStatusEnum.ARCHIVED.getCode().equals(status) ? 0 : 1;
        Integer archivingRetryCount = count == 0 ? 0 : taskSendArchive.getArchivingRetryCount() == null ? count : taskSendArchive.getArchivingRetryCount() + count;
        taskSendArchive.setArchivingStatus(status);
        taskSendArchive.setArchivingRetryCount(archivingRetryCount);
        taskSendArchive.setCallbackDate(completeDate);
        taskSendArchive.setLastUpdatedBy(archiveFileProperties.getEmpNo());
        taskSendArchive.setErrorMessage(errorMessage);
        taskSendArchiveRepository.updateTaskSendArchiveStatus(taskSendArchive);
    }

    @Override
    public void receiveCallback(CallbackArchiveFileReq req) {
        String[] vals = req.getFileKey().split(ArchiveConstant.UNDER_LINE);
        if(vals.length< ArchiveConstant.FILE_KEY_LENGTH) {
            return;
        }
        ArchiveTaskSend archive = taskSendArchiveRepository.getArchiveByBillId(vals[0], vals[1]);
        archive.setArchivingFailReasonCn(req.getMsg());
        archive.setArchivingFailReasonEn(req.getMsgEn());
        String status = CallbackArchiveFileReq.SUCCESS_STATUS.equalsIgnoreCase(req.getStatus()) ?
                ArchiveStatusEnum.ARCHIVED.getCode() : ArchiveStatusEnum.ARCHIVE_FAIL.getCode();
        updateArchivingStatus(archive, status, new Date(), req.getMsg());

        ArchiveSendLog archiveLog = new ArchiveSendLog();
        archiveLog.setTaskId(archive.getTaskId());
        archiveLog.setBusinessId(req.getFileKey());
        archiveLog.setBillNo(archive.getBillNo());
        archiveLog.setParams(JSONObject.toJSONString(req));
        archiveLog.setResults(req.getStatus());
        archiveLog.setUrl(ArchiveConstant.ARCHIVE_CALLBACK_URL);
        archiveLog.setCreatedBy(archiveFileProperties.getEmpNo());
        archiveLog.setLastUpdatedBy(archiveFileProperties.getEmpNo());
        archiveLog.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
        sendArchiveLogRepository.insert(archiveLog);
    }

    @Override
    public void archiveClearFile(String filePath) {
        try {
            log.info("---------------清理归档文件任务执行开始-------------------");
            File root = new File(filePath);
            FileSystemUtils.deleteRecursively(root);
            log.info("---------------清理归档文件任务执行结束-------------------");
        } catch (Exception ex) {
            log.error("archiveClearFile error {} ", ex);
        }
    }

    @Override
    public ArchiveTaskSend selectById(String taskId) {
        return taskSendArchiveRepository.selectById(taskId);
    }


}
