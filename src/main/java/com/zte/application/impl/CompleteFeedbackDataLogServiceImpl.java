package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.CompleteFeedbackDataLogService;
import com.zte.application.datawb.TaskToBeQueriedService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.DateUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.TaskToBeQueriedRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@Service("CompleteFeedbackDataLogService")
@DataSource(DatabaseType.WMES)
public class CompleteFeedbackDataLogServiceImpl  implements CompleteFeedbackDataLogService{

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Autowired
    private TaskToBeQueriedService taskToBeQueriedService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private TaskToBeQueriedRepository taskToBeQueriedRepository;

    @Autowired
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Autowired
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadServiceImpl;
    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;

    public CompleteFeedbackDataLogServiceImpl() {
    }

    @Override
    public void uploadCompleteFeedbackData(UploadCompleteMachineDataDTO dto) throws Exception {
        //完工生产订单任务信息
        boolean execFlag = true;
        Integer current = NumConstant.NUM_ONE;
        /* Started by AICoder, pid:n8e3a7abee341841420d091f00885e03ebc825dc */
        List<String> userAddressList = new ArrayList<>();
        List<EntityWeightDTO> dictCompanys = mesGetDictInforRepository.getDict(Constant.LOOKUP_TYPE_3020035);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dictCompanys)) {
            userAddressList = dictCompanys.stream().map(f -> f.getDescription()).collect(Collectors.toList());
        }
        /* Ended by AICoder, pid:n8e3a7abee341841420d091f00885e03ebc825dc */
        List<String> organizationIdList =  this.getLookupMeanList(Constant.LOOKUP_TYPE_8240040);
        List<String> taskLikeList =  this.getLookupMeanList(Constant.LOOKUP_TYPE_8240045);
        List<String> subStatusList =  this.getLookupMeanList(Constant.LOOKUP_TYPE_8240043);
        //配置上传的客户部件型号
        List<String> modelNumberList =  this.getLookupMeanList(Constant.LOOKUP_TYPE_8240044);
        dto.setUserAddressList(userAddressList);
        dto.setOrganizationIdList(organizationIdList);
        dto.setTaskLike(taskLikeList.get(NumConstant.NUM_ZERO));
        dto.setSubStatusList(subStatusList);
        dto.setModelNumberList(modelNumberList);
        String taskNo= zmsDeviceInventoryUploadServiceImpl.getDataTransferBatchNo();;
        while(execFlag){
            dto.setCurrent(current);
            List<TaskCompleteEntitiesDTO> taskCompleteEntitiesDTOList = queryTaskCompleteInformationPage(dto);
            if (taskCompleteEntitiesDTOList.size()<NumConstant.NUM_500||current>NumConstant.NUM_SEVENTY) {
                execFlag=false;
            }
            current++;
            pushDataToB2B(taskCompleteEntitiesDTOList,dto,taskNo);
        }
        taskToBeQueriedRepository.insertTaskCompleteInformation(dto);
    }


    /**
     * 查询完工任务订单
     * @param dto
     * @return
     */
    private List<TaskCompleteEntitiesDTO> queryTaskCompleteInformationPage(UploadCompleteMachineDataDTO dto) {
        Page<UploadCompleteMachineDataDTO> page = new Page<>(dto.getCurrent(),NumConstant.NUM_500);
        page.setSearchCount(false);
        page.setParams(dto);
        List<TaskCompleteEntitiesDTO> taskCompleteEntitiesDTOList= taskToBeQueriedRepository.queryTaskCompleteInformation(page);
        return taskCompleteEntitiesDTOList;
    }

    /**
     * 组装数据推送B2B
     * @param
     * @throws Exception
     */
    @Override
    public void pushDataToB2B(List<TaskCompleteEntitiesDTO> tempInsertList,UploadCompleteMachineDataDTO dto,String taskNo) throws Exception {
        if (CollectionUtils.isEmpty(tempInsertList)) {
            return;
        }

        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(java.util.UUID.randomUUID().toString());
        customerDataLogDTO.setOrigin(Constant.MES);
        customerDataLogDTO.setCustomerName(BYTE_DANCE);
        customerDataLogDTO.setProjectName(PROJECT_NAME);
        customerDataLogDTO.setMessageType(MESSAGE_TYPE_B2BFEEDBACK);
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setCreateBy(dto.getEmpNo());
        customerDataLogDTO.setLastUpdatedBy(dto.getEmpNo());
        customerDataLogDTO.setProjectPhase(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_FULL));
        customerDataLogDTO.setTaskNo(taskNo);
        for (TaskCompleteEntitiesDTO taskCompleteDto : tempInsertList) {
            taskCompleteDto.setDataTransferBatchNo(taskNo);
        }

        /* Started by AICoder, pid:5574fab3cd1943928396dfa155b20ec9 */
        Map<String, Object> map = new HashMap<>();
        map.put(DATA, tempInsertList);
        customerDataLogDTO.setJsonData(JSON.toJSONString(map));
        dataList.add(customerDataLogDTO);
        /* Ended by AICoder, pid:5574fab3cd1943928396dfa155b20ec9 */

        // 组装日志对象
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
        BeanUtils.copyProperties(customerDataLogDTO, zmsMesInfoUploadDTO);
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        // 写入上传日志
        zmsIndicatorUploadService.insertMesInfoUploadLog(zmsMesInfoUploadDTOList);
        //推送B2B
        centerfactoryRemoteService.pushDataToB2B(dataList);
    }

    /**
     * 获取相关配置
     * @param lookupType
     * @return
     */
    private List<String> getLookupMeanList(String lookupType) {
        List<SysLookupValues> sysLookupValuesList = wsmAssembleLinesService.getSysLookupValues(lookupType);
        if (CollectionUtils.isEmpty(sysLookupValuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG,new Object[]{lookupType});
        }
        return sysLookupValuesList.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning())).map(e -> e.getLookupMeaning()).distinct().collect(Collectors.toList());
    }
}
