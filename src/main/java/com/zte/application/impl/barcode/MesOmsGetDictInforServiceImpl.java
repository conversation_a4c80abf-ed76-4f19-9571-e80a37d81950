package com.zte.application.impl.barcode;

import com.zte.application.barcode.MesOmsGetDictInforService;
import com.zte.domain.model.barcode.MesOmsGetDictInforRepository;
import com.zte.interfaces.dto.api.DictInfoForDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 查询BARCODE库字典表数据
 * <AUTHOR>
 * @Date 15:47 2025/3/6
 * @Version 1.0
 **/
@Service
@DataSource(DatabaseType.BARCODE)
public class MesOmsGetDictInforServiceImpl implements MesOmsGetDictInforService {

    @Autowired
    private MesOmsGetDictInforRepository mesOmsGetDictInforRepository ;

    @Override
    public Map<String, List<DictInfoForDTO>> getDictMap(List<String> lookUpTypeList) {
        List<DictInfoForDTO> dictList = mesOmsGetDictInforRepository.getDictList(lookUpTypeList);
        return dictList
                .stream()
                .collect(Collectors.groupingBy(DictInfoForDTO::getLookupType));
    }

}
