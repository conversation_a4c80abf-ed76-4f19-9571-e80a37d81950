package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;
import java.util.Map;

import com.zte.domain.model.MesTopboxDefineRepository;
import com.zte.interfaces.dto.MesTopboxDefineEntityDTO;
import com.zte.application.MesTopboxDefineService;


@Service("mesTopboxDefineService")
@DataSource(value = DatabaseType.WMSPRODLMS)
public class MesTopboxDefineServiceImpl implements MesTopboxDefineService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MesTopboxDefineRepository mesTopboxDefinerepository;

    @Override
    public List<MesTopboxDefineEntityDTO> getTopBoxByCondition(MesTopboxDefineEntityDTO record) throws Exception {
        if(StringHelper.isEmpty(record.getBoxTypeCode())&&StringHelper.isEmpty(record.getPackageItemCode())&&StringHelper.isEmpty(record.getInBoxTypeCode())){
        	// 如果没传参数，而且没有传分页条件，则不查询
        	if(StringHelper.isEmpty(record.getStartRow()) || StringHelper.isEmpty(record.getEndRow())) {
        		return null;        		
        	}
        }
        return mesTopboxDefinerepository.getTopBoxByCondition(record);
    }

    @Override
    public long getCount(MesTopboxDefineEntityDTO record){
        if(StringHelper.isEmpty(record.getBoxTypeCode())&&StringHelper.isEmpty(record.getPackageItemCode())&&StringHelper.isEmpty(record.getInBoxTypeCode())){
            if(StringHelper.isEmpty(record.getStartRow()) || StringHelper.isEmpty(record.getEndRow())) {
                return 0;
            }
        }
        return mesTopboxDefinerepository.getCount(record);
    }

    }