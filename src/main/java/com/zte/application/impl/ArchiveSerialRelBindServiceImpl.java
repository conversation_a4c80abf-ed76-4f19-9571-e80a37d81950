package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveSerialRelBindClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  串号预关联绑定归档
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveSerialRelBindServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveSerialRelBindServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveSerialRelBindClient archiveSerialRelBindClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("串号预关联绑定查询归档start......");
        String fileName = ArchiveBusinessTypeEnum.SERIALREL_BIND.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        //获取串号预关联绑定单据信息
        List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS = selectSerialRelBindByPlanNumber(taskSendArchive);
        //获取串号预关联绑定单据列表excel
        AttachmentUploadVo serialRelBindExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.SERIALRELBIND_SHEET_HEAD_MAP, archiveSerialRelBindDTOS);

        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(serialRelBindExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.SERIALREL_BIND.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.SERIALREL_BIND.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveSerialRelBindDTOS);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("串号预关联绑定查询归档end......");
        return item;
    }

    private List<ArchiveSerialRelBindDTO> selectSerialRelBindByPlanNumber(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveSerialRelBindDTO>> serviceData = archiveSerialRelBindClient.selectSerialRelBindByPlanNumber(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档串号预关联绑定单据信息返回报错：{}",msg);
            throw new RouteException("调用服务获取待归档串号预关联绑定单据信息返回报错");
        }

        List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveSerialRelBindDTO.class);
        if(CollectionUtils.isEmpty(archiveSerialRelBindDTOS)){
            logger.error("调用服务获取待归档串号预关联绑定单据信息返回空");
            throw new RouteException("调用服务获取待归档串号预关联绑定单据信息返回空");
        }
        return archiveSerialRelBindDTOS;
    }



    private ArchiveBusinessVo buildBusiness(List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO = archiveSerialRelBindDTOS.get(0);
        Date creationDate = DateUtil.convertStringToDate(archiveSerialRelBindDTO.getCreationDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archiveSerialRelBindDTO.getCreatedBy()));
        if(pubHrvOrg == null){
            pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserId(CommonUtil.regexUserId(archiveSerialRelBindDTO.getCreatedBy())));
        }
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(creationDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(creationDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveSerialRelBindDTO.getPlanNumber());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.SERIALREL_BIND.getName()+"-"+archiveSerialRelBindDTO.getPlanNumber());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.SERIALREL_BIND.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.SERIALREL_BIND.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("serialRelBind archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveSerialRelBindDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.SERIALREL_BIND.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("serialRelBind archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }
    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        long start=System.currentTimeMillis();
        ServiceData<Page<ArchiveSerialRelBindDTO>> listServiceData = archiveSerialRelBindClient.selectSerialRelBindList(archiveQueryParamDTO);
        long end=System.currentTimeMillis();
        logger.info("time:{}",(end-start)/1000);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取串号预关联绑定单据返回报错：{}", msg);
            throw new RouteException("调用服务获取串号预关联绑定单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveSerialRelBindDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)){
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem.getPlanNumber());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
