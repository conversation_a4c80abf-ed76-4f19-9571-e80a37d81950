package com.zte.application.impl;

import com.zte.application.ArchiveBoqBoxMakeDataService;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.BoxMakeTypeEnum;
import com.zte.common.enums.MergeBillFlagEnum;
import com.zte.common.enums.YesNoEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.common.utils.BoxMakeUtil;
import com.zte.common.utils.CommonUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.interfaces.assembler.ArchiveBoqBoxMakeAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/7
 * @description :装箱单拟制归档服务
 */
@Service("archiveBoxMakeService")
public class ArchiveBoxMakeServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchiveBoqBoxMakeDataService archiveBoqBoxMakeDataService;

    @Autowired
    PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        // 事务管理器。使支持多数据源事务
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        try {
            if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
                return null;
            }
            ArchiveBoqBoxMakeDTO dto = archiveBoqBoxMakeDataService.getByEntityName(taskSendArchive.getBillId());
            if(null == dto){
                return null;
            }
            return initArchiveItem(taskSendArchive,dto);
        }catch (Exception e){
            throw e;
        }finally {
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        }
    }

    /**
     * BOQ-BOM装箱单拟制归档
     * @param taskSendArchive 归档任务
     * @param dto BOQ-BOM装箱单拟制
     * @return 归档项
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchiveBoqBoxMakeDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();

        String businessId = ArchiveBusinessTypeEnum.BOX_MAKE.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.BOX_MAKE.getName());

        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNameId(dto.getCreatedBy());
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        ArchiveBoqBoxMakeAssembler.initItemContent(item, dto, pubHrvOrg);
        //BOQ-BOM装箱单拟制归档附件归档
        archiveBoqBoxMake(item,dto,taskSendArchive);
        return item;
    }

    /**
     * BOQ-BOM装箱单拟制归档附件归档
     * @param item 归档项
     * @param dto BOQ-BOM装箱单拟制
     * @param taskSendArchive 归档任务
     */
    private void archiveBoqBoxMake(ArchiveReq.ArchiveItem item, ArchiveBoqBoxMakeDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        //文件题名
        String fileTitle = ArchiveConstant.MODULE_BOQ_BOX_MAKE_TITLE + dto.getBillNumber();
        //归档箱物料列表
        AttachmentUploadVo boqUnitBoxMakeItemUpload = archiveUnitBoxMake(dto,taskSendArchive,fileTitle);
        //归档单元箱列表
        AttachmentUploadVo boqBoxMakeItemUpload = archiveBoxMakeItem(dto,taskSendArchive,fileTitle);
        List<ArchiveReq.ArchiveItemDoc> docs = ArchiveBusiness.buildArchiveItemDocVo(Arrays.asList(boqBoxMakeItemUpload,boqUnitBoxMakeItemUpload));
        item.setItemDocs(docs);
    }

    /**
     * 箱物料列表归档
     * @param dto boq装箱单
     * @param taskSendArchive 归档任务
     * @param fileTitle 文件题名
     * @return 附件上传信息
     */
    private AttachmentUploadVo archiveBoxMakeItem(ArchiveBoqBoxMakeDTO dto, ArchiveTaskSend taskSendArchive, String fileTitle) throws Exception {
        //查询箱物料列表
        List<ArchiveBoxMakeItemDTO> dtos = archiveBoqBoxMakeDataService.getArchiveBoxMakeItemByOrganizationIdAndBillId(dto.getOrganizationId(),dto.getBillId());
        dtos.forEach(o->{o.setIsDangergoods(YesNoEnum.getNameByCode(o.getIsDangergoods()));});
        String fileName = CommonUtil.join(ArchiveConstant.JOIN_STR,fileTitle,ArchiveConstant.BOX_ITEM);
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        AttachmentUploadVo uploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive,fileName,ArchiveConstant.BOX_MAKE_ITEM_MAP,dtos);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return uploadVo;
    }

    /**
     * 归档单元箱列表
     * @param dto BOQ装箱单拟制
     * @param taskSendArchive 归档任务
     * @param fileTitle 文件题名
     * @return 附件上传信息
     */
    private AttachmentUploadVo archiveUnitBoxMake(ArchiveBoqBoxMakeDTO dto, ArchiveTaskSend taskSendArchive, String fileTitle) throws Exception {
        //查询单元箱列表
        List<ArchiveBoqUnitBoxMakeDTO> boqUnitBoxMakeDTOS = archiveBoqBoxMakeDataService.getArchiveUnitBoxMakeByBillId(taskSendArchive.getBillId());
        boqUnitBoxMakeDTOS.forEach(o->o.setMergeBillFlag(MergeBillFlagEnum.getNameByCode(o.getMergeBillFlag())));
        String fileName = CommonUtil.join(ArchiveConstant.JOIN_STR,fileTitle,ArchiveConstant.BOX_UNIT);
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        AttachmentUploadVo uploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive,fileName,ArchiveConstant.BOQ_UNIT_BOX_MAKE_MAP,boqUnitBoxMakeDTOS);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return uploadVo;
    }

    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        return BoxMakeUtil.getArchiveDataList(archiveQueryParamDTO,archiveBoqBoxMakeDataService,BoxMakeTypeEnum.BOX_MAKE.getCode(),archiveFileProperties.getEmpNo());
    }
}
