package com.zte.application.impl;


import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchivePsEntityPlanDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchivePsEntityPlanAssembler;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/16
 * @description : 生产指令查询归档
 */
@Slf4j
@Service("psEntityPlan")
@DataSource(value = DatabaseType.SFC)
public class ArchivePsEntityPlanServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchivePsEntityPlanDataService psEntityPlanService;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
            return null;
        }
        ArchivePsEntityPlanDTO dto = psEntityPlanService.getByPlanNumber(taskSendArchive.getBillNo());
        if(null == dto){
            return null;
        }
        return initArchiveItem(taskSendArchive,dto);
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {

        Page<ArchiveTaskSend> page = new Page<>();
        page.setPageSize(archiveQueryParamDTO.getRows());
        page.setCurrent(archiveQueryParamDTO.getPage());

        Page<ArchivePsEntityPlanDTO> pageInfo = psEntityPlanService.getByDateRange(archiveQueryParamDTO);
        page.setTotal(pageInfo.getTotal());

        List<ArchivePsEntityPlanDTO> cces = pageInfo.getRows();
        List<ArchiveTaskSend> list = cces.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(ArchiveBusinessTypeEnum.PS_ENTITY_PLAN.getCode());
            archiveItem.setBillId(String.valueOf(dataItem.getPlanId()));
            archiveItem.setBillNo(dataItem.getPlanNumber());
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            archiveItem.setBillReserves(ArchiveConstant.BLANK);
            return archiveItem;
        }).collect(Collectors.toList());
        page.setRows(list);
        return page;
    }

    /**
     * 初始化归档项
     *
     * @param taskSendArchive 归档任务
     * @param dto 生产任务查询归档数据
     * @return 归档项
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchivePsEntityPlanDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        String businessId = ArchiveBusinessTypeEnum.PS_ENTITY_PLAN.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.PS_ENTITY_PLAN.getName());
        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNamePlus(dto.getFinishPerson());
        ArchivePsEntityPlanAssembler.initItemContent(item, dto, pubHrvOrg);
        //归档生产指令数据
        archivePsEntityPlan(item,dto,taskSendArchive);
        return item;
    }

    /**
     * 归档生产指令数据
     * @param item 归档项
     * @param dto 生产指令
     * @param taskSendArchive
     */
    private void archivePsEntityPlan(ArchiveReq.ArchiveItem item, ArchivePsEntityPlanDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        //查询生产指令详情
        List<ArchivePsEntityPlanDetailDTO> details = psEntityPlanService.getDetailByEntityPlan(dto);
        //文件题名* 生产指令-指令号
        String excelFileName = ArchiveConstant.MODULE_ENTITY_PLAN_FILE_TITLE + dto.getPlanNumber();
        //查询数据，导出excel
        AttachmentUploadVo uploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive,excelFileName,ArchiveConstant.PS_ENTITY_PLAN_MAP,details);
        //封装并赋值附件信息
        ArchiveReq.ArchiveItemDoc archiveItemDoc = ArchiveBusiness.buildArchiveItemDoc(uploadVo);
        item.setItemDocs(Collections.singletonList(archiveItemDoc));
    }

}
