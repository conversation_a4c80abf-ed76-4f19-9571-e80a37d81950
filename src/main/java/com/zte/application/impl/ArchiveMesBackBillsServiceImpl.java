package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveMesBackBillsDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchiveMesBackBillsAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/15
 * @description :
 */
@Service("archiveMesBackBillsDService")
public class ArchiveMesBackBillsServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchiveMesBackBillsDataService archiveMesBackBillsDataService;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        // 事务管理器。使支持多数据源事务
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        try {
            if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
                return null;
            }
            ArchiveMesBackBillsDTO dto = archiveMesBackBillsDataService.getByBackNumber(taskSendArchive.getBillNo());
            if(null == dto){
                return null;
            }
            return initArchiveItem(taskSendArchive,dto);
        }catch (Exception e){
            throw e;
        }finally {
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        }
    }

    /**
     * 初始化归档项
     * @param taskSendArchive 归档任务
     * @param dto 装箱单退库申请
     * @return 归档项
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchiveMesBackBillsDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();

        String businessId = ArchiveBusinessTypeEnum.MES_BACK_BILLS.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.MES_BACK_BILLS.getName());

        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNameId(dto.getCreatedBy());
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        ArchiveMesBackBillsAssembler.initItemContent(item, dto, pubHrvOrg);
        //装箱单退库申请归档附件归档
        archiveBoqBoxMake(item,dto,taskSendArchive);
        return item;
    }

    private void archiveBoqBoxMake(ArchiveReq.ArchiveItem item, ArchiveMesBackBillsDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        //查询单元箱列表
        String fileTitle = ArchiveConstant.FILE_TITLE_MES_BACK_BILLS+dto.getBackNumber();
        List<ArchiveMesBackBillsDTO> dtos = archiveMesBackBillsDataService.getListByBackBillId(dto.getBackBillId());
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        AttachmentUploadVo uploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.MES_BACK_BILLS_MAP,dtos);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        item.setItemDocs(Collections.singletonList(ArchiveBusiness.buildArchiveItemDoc(uploadVo)));
    }

    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        Page<ArchiveTaskSend> page = new Page<>();
        page.setPageSize(archiveQueryParamDTO.getRows());
        page.setCurrent(archiveQueryParamDTO.getPage());

        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        try {
            Page<ArchiveMesBackBillsDTO> pageInfo = archiveMesBackBillsDataService.getPageByDateRange(archiveQueryParamDTO);
            page.setTotal(pageInfo.getTotal());

            List<ArchiveMesBackBillsDTO> cces = pageInfo.getRows();
            List<ArchiveTaskSend> list = cces.stream().map(dataItem -> {
                ArchiveTaskSend archiveItem = new ArchiveTaskSend();
                archiveItem.setTaskType(ArchiveBusinessTypeEnum.MES_BACK_BILLS.getCode());
                archiveItem.setBillId(dataItem.getBackBillId());
                archiveItem.setBillNo(dataItem.getBackNumber());
                archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
                archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
                archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
                archiveItem.setBillReserves(ArchiveConstant.BLANK);
                return archiveItem;
            }).collect(Collectors.toList());
            page.setRows(list);
        }catch (Exception e){
            throw  e;
        }finally {
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        }
        return page;
    }
}
