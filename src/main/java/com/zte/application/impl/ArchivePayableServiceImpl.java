package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  应付款结算查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchivePayableServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchivePayableServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchivePayableRepository archivePayableRepository;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("应付款结算查询归档start......");
        String fileName = ArchiveBusinessTypeEnum.PAYABLE_QUERY.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        List<ArchivePayableDTO> archivePayableDTOs = archivePayableRepository.getPayableByBillNo(taskSendArchive.getBillNo());
        if(CollectionUtils.isEmpty(archivePayableDTOs)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_PAYABLE_NULL);
        }
        //应付款结算查询
        AttachmentUploadVo payableExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.PAYABLE_SHEET_HEAD_MAP, archivePayableDTOs);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(payableExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.PAYABLE_QUERY.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.PAYABLE_QUERY.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archivePayableDTOs);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("应付款结算查询归档end......");
        return item;
    }



    private ArchiveBusinessVo buildBusiness(List<ArchivePayableDTO> archivePayableDTOs) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        ArchivePayableDTO archivePayableDTO=archivePayableDTOs.get(0);
        Date dealingDate = DateUtil.convertStringToDate(archivePayableDTO.getDealingDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archivePayableDTO.getDealingPerson())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(dealingDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(dealingDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archivePayableDTO.getBillNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(archivePayableDTO.getDealingPerson());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.PAYABLE_QUERY.getName()+"-"+archivePayableDTO.getBillNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.PAYABLE_QUERY.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.PAYABLE_QUERY.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        // 外包供应商
        businessVo.setOther(archivePayableDTO.getSupplyName());
        // 结算月份
        businessVo.setOtherTwo(archivePayableDTO.getSettleMonth());
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("payable archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchivePayableDTO> pageInfo = getPageInfo(archiveQueryParamDTO);
        pageInfo.setRows(archivePayableRepository.selectPayableListByPage(pageInfo));
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.PAYABLE_QUERY.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("payable archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getPageInfo(ArchiveQueryParamDTO archiveQueryParamDTO){
        Page<ArchivePayableDTO> pageInfo = new Page<>(archiveQueryParamDTO.getPage(), archiveQueryParamDTO.getRows());
        pageInfo.setParams(archiveQueryParamDTO);
        return  pageInfo;
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchivePayableDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)){
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem.getBillNo());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
