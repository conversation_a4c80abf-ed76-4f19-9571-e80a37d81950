package com.zte.application.impl;

import com.zte.application.ArchiveMesBackBillsDataService;
import com.zte.domain.model.ArchiveMesBackBillsDataRepository;
import com.zte.interfaces.dto.ArchiveMesBackBillsDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/16
 * @description :
 */
@Service
public class ArchiveMesBackBillsDataServiceImpl implements ArchiveMesBackBillsDataService {

    @Autowired
    ArchiveMesBackBillsDataRepository repository;

    @Override
    public Page<ArchiveMesBackBillsDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO) {
        if(null == archiveQueryParamDTO || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }
        Page<ArchiveMesBackBillsDTO> page = new Page<>(archiveQueryParamDTO.getPage(),archiveQueryParamDTO.getRows());
        page.setParams(archiveQueryParamDTO);
        page.setRows(repository.getPageByDateRange(page));
        return page;
    }

    @Override
    public ArchiveMesBackBillsDTO getByBackNumber(String billNo) {
        if(StringUtils.isBlank(billNo)){
            return null;
        }
        return repository.getByBackNumber(billNo);
    }

    @Override
    public List<ArchiveMesBackBillsDTO> getListByBackBillId(String backBillId) {
        if(StringUtils.isBlank(backBillId)){
            return Collections.emptyList();
        }
        return repository.getListByBackBillid(backBillId);
    }
}
