package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.MesGetDictInforService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveInvSundriesClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveInvSundriesServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveInvSundriesServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private MesGetDictInforService mesGetDictInforService;

    @Autowired
    private ArchiveInvSundriesClient archiveInvSundriesClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("杂项交易明细归档start......");
        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = selectInvSundriesByCondition(taskSendArchive);
        //单据明细信息
        ArchiveInvSundriesDTO archiveInvSundriesDTO=archiveInvSundriesDTOS.get(0);
        if (StringUtils.isEmpty(archiveInvSundriesDTO.getPostDateStr())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ISSUE_DATE_NOT_NULL);
        }
        Date postDate = DateUtil.convertStringToDate(archiveInvSundriesDTO.getPostDateStr(),DateUtils.DATE_FORMAT_FULL);
        String businessName = String.format(ArchiveBusinessTypeEnum.INV_SUNDRIES.getName(),DateUtil.getYear(postDate),DateUtil.getMonth(postDate));
        String fileName = businessName + ArchiveConstant.JOIN_STR + taskSendArchive.getBillNo();

        AttachmentUploadVo invSundriesExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.INV_SUNDRIES_SHEET_HEAD_MAP, archiveInvSundriesDTOS);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(invSundriesExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.INV_SUNDRIES.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, businessName, archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveInvSundriesDTO,businessName);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("杂项交易明细归档end......");
        return item;
    }

    private List<ArchiveInvSundriesDTO> selectInvSundriesByCondition(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveInvSundriesDTO>> serviceData = archiveInvSundriesClient.selectInvSundriesByCondition(getInvSundriesQueryParams(taskSendArchive));
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务根据物料代码和月份查询杂项交易明细单据返回报错：{}",msg);
            throw new RouteException("调用服务根据物料代码和月份查询杂项交易明细单据返回报错");
        }
        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = serviceData.getBo();
        if(CollectionUtils.isEmpty(archiveInvSundriesDTOS)){
            logger.error("调用服务根据物料代码和月份查询杂项交易明细单据返回空：{}");
            throw new RouteException("调用服务根据物料代码和月份查询杂项交易明细单据返回空");
        }
        return archiveInvSundriesDTOS;
    }

    private Map<String, Object> getInvSundriesQueryParams(ArchiveTaskSend taskSendArchive) {
        Map<String, Object> params = getQueryParams();
        params.put("itemCode", taskSendArchive.getBillNo());
        params.put("creationMonth", taskSendArchive.getBillReserves());
        return params;
    }

    private ArchiveBusinessVo buildBusiness(ArchiveInvSundriesDTO archiveInvSundriesDTO,String businessName) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        Date postDate = DateUtil.convertStringToDate(archiveInvSundriesDTO.getPostDateStr(),DateUtils.DATE_FORMAT_FULL);
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(postDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.TEN_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(DateUtil.getLastDayOfMonth(archiveInvSundriesDTO.getPostDateStr()), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveInvSundriesDTO.getItemCode());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(ArchiveConstant.INV_SUNDRIES_SUB_INVENTORY_XA.equals(archiveInvSundriesDTO.getSubInventory())?ArchiveConstant.INV_SUNDRIES_ZRZ_XA:ArchiveConstant.INV_SUNDRIES_ZRZ_SZ);
        // 文件题名
        businessVo.setFileTitle(businessName);
        // 关键词
        businessVo.setKeyWord(ArchiveConstant.INV_SUNDRIES_KEY_WORD);
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.INV_SUNDRIES.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(ArchiveConstant.INV_SUNDRIES_SUB_INVENTORY_XA.equals(archiveInvSundriesDTO.getSubInventory())?ArchiveConstant.INV_SUNDRIES_DEPT_XA:ArchiveConstant.INV_SUNDRIES_DEPT_SZ);
        // 归档部门编号
        businessVo.setArchiveDeptCode(ArchiveConstant.INV_SUNDRIES_SUB_INVENTORY_XA.equals(archiveInvSundriesDTO.getSubInventory())?ArchiveConstant.INV_SUNDRIES_DEPT_CODE_XA:ArchiveConstant.INV_SUNDRIES_DEPT_CODE_SZ);
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("inv sundries archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        archiveQueryParamDTO.setConditions(getQueryParams());
        Page<ArchiveInvSundriesDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.INV_SUNDRIES.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("inv sundries archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Map<String,Object> getQueryParams(){
        List<String> subInventorys=getDicListValue(Constant.LOOKUP_CODE_824005000004);
        List<String> itemCodes=getDicListValue(Constant.LOOKUP_CODE_824005000005);
        Map<String,Object> params=new HashMap();
        params.put("subInventorys",subInventorys);
        params.put("itemCodes",itemCodes);
        return params;
    }

    private List<String> getDicListValue(String lookUpCode){
        String description = mesGetDictInforService.getDicDescription(lookUpCode);
        if(StringUtils.isEmpty(description)){
            logger.error("没有配置子库或者物料代码");
            throw new RouteException("没有配置子库或者物料代码");
        }
        return Arrays.asList(description.split(Constant.COMMA));
    }

    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        ServiceData<Page<ArchiveInvSundriesDTO>> listServiceData = archiveInvSundriesClient.selectInvSundriesListByPage(archiveQueryParamDTO);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取查询待归档的杂项交易明细单据返回报错：{}", msg);
            throw new RouteException("调用服务获取查询待归档的杂项交易明细单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveInvSundriesDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem.getItemCode());
            archiveItem.setBillReserves(dataItem.getCreationMonth());
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
