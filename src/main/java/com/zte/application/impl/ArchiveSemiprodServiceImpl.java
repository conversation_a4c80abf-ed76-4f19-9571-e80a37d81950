package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveSemiprodRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  半成品检验管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveSemiprodServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveSemiprodServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveSemiprodRepository archiveSemiprodRepository;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("半成品检验管理归档start......");
        String fileName = ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        ArchiveSemiprodDTO archiveSemiprodDTO = archiveSemiprodRepository.getSemiprodById(taskSendArchive.getBillId());
        if(archiveSemiprodDTO == null){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_SEMIPROD_NULL);
        }
        List<ArchiveSemiprodDetailDTO> archiveSemiprodDetailDTOList = archiveSemiprodRepository.getSemiprodDetailById(taskSendArchive.getBillId());
        if(CollectionUtils.isEmpty(archiveSemiprodDetailDTOList)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_SEMIPROD_DETAIL_NULL);
        }
        //半成品检验
        AttachmentUploadVo semiprodExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.SEMIPROD_SHEET_HEAD_MAP, Collections.singletonList(archiveSemiprodDTO));
        //半成品检验单据明细
        AttachmentUploadVo semiprodDetailsExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", ArchiveConstant.SEMIPROD_DETAIL_SHEET_HEAD_MAP, archiveSemiprodDetailDTOList);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(semiprodExcelUploadVo);
        attachmentDataList.add(semiprodDetailsExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveSemiprodDTO,archiveSemiprodDetailDTOList);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("半成品检验管理归档end......");
        return item;
    }



    private ArchiveBusinessVo buildBusiness(ArchiveSemiprodDTO archiveSemiprodDTO,List<ArchiveSemiprodDetailDTO> archiveSemiprodDetailDTOList) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        ArchiveSemiprodDetailDTO archiveSemiprodDetailDTO=archiveSemiprodDetailDTOList.get(archiveSemiprodDetailDTOList.size()-1);
        Date checkDate=archiveSemiprodDetailDTO.getCheckDate();
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archiveSemiprodDetailDTO.getCreatedBy())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(checkDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(checkDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveSemiprodDTO.getCheckNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(archiveSemiprodDetailDTO.getCheckBy());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getName()+"-"+archiveSemiprodDTO.getCheckNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(Constant.SZPROD_FULL_NAME);
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("semiprod archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveSemiprodDTO> pageInfo = getPageInfo(archiveQueryParamDTO);
        pageInfo.setRows(archiveSemiprodRepository.selectSemiprodListByPage(pageInfo));
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.SEMIPROD_MANAGE.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("semiprod archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getPageInfo(ArchiveQueryParamDTO archiveQueryParamDTO){
        Page<ArchiveSemiprodDTO> pageInfo = new Page<>(archiveQueryParamDTO.getPage(), archiveQueryParamDTO.getRows());
        pageInfo.setParams(archiveQueryParamDTO);
        return  pageInfo;
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveSemiprodDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)){
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId(String.valueOf(dataItem.getId()));
            archiveItem.setBillNo(dataItem.getCheckNo());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
