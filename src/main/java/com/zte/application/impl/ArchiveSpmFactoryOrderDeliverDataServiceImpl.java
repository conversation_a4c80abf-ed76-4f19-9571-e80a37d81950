package com.zte.application.impl;

import com.zte.application.ArchiveSpmFactoryOrderDeliverDataService;
import com.zte.domain.model.ArchiveSpmFactoryOrderDeliverRepository;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryReturnDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/22
 * @description :
 */
@DataSource(DatabaseType.SFC)
@Service
public class ArchiveSpmFactoryOrderDeliverDataServiceImpl implements ArchiveSpmFactoryOrderDeliverDataService {
    @Autowired
    ArchiveSpmFactoryOrderDeliverRepository repository;

    @Override
    public ArchiveSpmFactoryOrderDeliverDTO getByRecordId(String billNo) {
        if(StringUtils.isBlank(billNo)){
            return null;
        }
        return repository.getByRecordId(billNo);
    }

    @Override
    public Page<ArchiveSpmFactoryOrderDeliverDTO> getByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO) {
        if(null == archiveQueryParamDTO || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }
        Page<ArchiveSpmFactoryOrderDeliverDTO> page = new Page<>(archiveQueryParamDTO.getPage(),archiveQueryParamDTO.getRows());
        page.setParams(archiveQueryParamDTO);
        page.setRows(repository.getPageByDateRange(page));
        return page;
    }

    @Override
    public List<ArchiveSpmFactoryReturnDTO> getSpmFactoryReturnByRecordId(String recordId) {
        if(StringUtils.isBlank(recordId)){
            return Collections.emptyList();
        }
        return repository.getSpmFactoryReturnByRecordId(recordId);
    }

    @Override
    public List<ArchiveSpmFactoryOrderDeliverDTO> getSpmProdPlanByRecordId(String recordId,String organizationId) {
        if(StringUtils.isBlank(recordId)){
            return Collections.emptyList();
        }
        return repository.getSpmProdPlanByRecordId(recordId,organizationId);
    }

    @Override
    public List<ArchiveSpmFactoryReturnDTO> getSpmFactoryProcessDefineByOrderId(String recordId) {
        if(StringUtils.isBlank(recordId)){
            return Collections.emptyList();
        }
        return repository.getSpmFactoryProcessDefineByOrderId(recordId);
    }
}
