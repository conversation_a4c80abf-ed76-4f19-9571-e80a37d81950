package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveBoxupBillClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  装箱清单归档
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveBoxupBillServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveBoxupBillServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveBoxupBillClient archiveBoxupBillClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("装箱清单查询归档start......");
        String fileName = ArchiveBusinessTypeEnum.BOXUP_BILL.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        //装箱清单查询单据信息
        List<ArchiveBoxupBillDTO> archiveBoxupBillDTOS = selectBoxupBillByBoxupBillId(taskSendArchive);
        //装箱清单明细查询
        List<ArchiveBoxupBillDetailDTO> archiveBoxupBillDetailDTOS = selectBoxupBillDetailByBoxupBillId(taskSendArchive);
        //装箱清单查询单据信息列表excel
        AttachmentUploadVo archiveBoxupBillExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.BOXUP_BILL_SHEET_HEAD_MAP, archiveBoxupBillDTOS);
       //装箱清单明细查询单据信息列表excel
        AttachmentUploadVo archiveBoxupBillDetailExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", ArchiveConstant.BOXUP_BILL_DETAIL_SHEET_HEAD_MAP, archiveBoxupBillDetailDTOS);

        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(archiveBoxupBillExcelUploadVo);
        attachmentDataList.add(archiveBoxupBillDetailExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.BOXUP_BILL.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.BOXUP_BILL.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveBoxupBillDTOS);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("装箱清单查询归档end......");
        return item;
    }

    private List<ArchiveBoxupBillDTO> selectBoxupBillByBoxupBillId(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveBoxupBillDTO>> serviceData = archiveBoxupBillClient.selectBoxupBillByBoxupBillId(taskSendArchive.getBillId());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取装箱清单单据信息返回报错：{}",msg);
            throw new RouteException("调用服务获取装箱清单单据信息返回报错");
        }

        List<ArchiveBoxupBillDTO> archiveBoxupBillDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveBoxupBillDTO.class);
        if(CollectionUtils.isEmpty(archiveBoxupBillDTOS)){
            logger.error("调用服务获取装箱清单单据信息返回空");
            throw new RouteException("调用服务获取装箱清单单据信息返回空");
        }
        return archiveBoxupBillDTOS;
    }

    private List<ArchiveBoxupBillDetailDTO> selectBoxupBillDetailByBoxupBillId(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveBoxupBillDetailDTO>> serviceData = archiveBoxupBillClient.selectBoxupBillDetailByBoxupBillId(taskSendArchive.getBillId());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取装箱清单单据明细信息返回报错：{}",msg);
            return new ArrayList<>();
        }

        List<ArchiveBoxupBillDetailDTO> archiveBoxupBillDetailDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveBoxupBillDetailDTO.class);
        if(CollectionUtils.isEmpty(archiveBoxupBillDetailDTOS)){
            return new ArrayList<>();
        }
        return archiveBoxupBillDetailDTOS;
    }



    private ArchiveBusinessVo buildBusiness(List<ArchiveBoxupBillDTO> archiveBoxupBillDTOS) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        ArchiveBoxupBillDTO archiveBoxupBillDTO = archiveBoxupBillDTOS.get(0);
        Date lastUpdateDate = DateUtil.convertStringToDate(archiveBoxupBillDTO.getLastUpdateDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archiveBoxupBillDTO.getLastUpdateBy())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(lastUpdateDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(lastUpdateDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveBoxupBillDTO.getBoxupBillNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.BOXUP_BILL.getName()+"-"+archiveBoxupBillDTO.getBoxupBillNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.BOXUP_BILL.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.BOXUP_BILL.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        // 任务号
        businessVo.setOther(archiveBoxupBillDTO.getEntityName());
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("boxup bill archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveBoxupBillDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.BOXUP_BILL.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("boxup bill archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }
    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        long start=System.currentTimeMillis();
        ServiceData<Page<ArchiveBoxupBillDTO>> listServiceData = archiveBoxupBillClient.selectBoxupBillList(archiveQueryParamDTO);
        long end=System.currentTimeMillis();
        logger.info("time:{}",(end-start)/1000);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档装箱清单单据返回报错：{}", msg);
            throw new RouteException("调用服务获取待归档装箱清单单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveBoxupBillDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)){
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId(String.valueOf(dataItem.getBoxupBillId()));
            archiveItem.setBillNo(dataItem.getBoxupBillNo());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
