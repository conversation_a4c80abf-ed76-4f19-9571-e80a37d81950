package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveTestProcessClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveTestProcessServiceImpl implements ArchiveBusinessService {

    @Autowired
    private ArchiveTestProcessClient archiveTestProcessClient;

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Value("${archive.ftp.host}")
    private String ftpHost;


    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        log.info("测试工艺单归档start......");
        ArchiveTestProcessDTO testProcessDTO = selectTestProcessById(taskSendArchive);
        String pubSubClass = Objects.toString(testProcessDTO.getProductSubClass(),"");
        String singleCode = Objects.toString(testProcessDTO.getSingleCode(),"");
        String billVersion = Objects.toString(testProcessDTO.getBillVersion(),"");
        String fileName = Objects.toString(testProcessDTO.getBillNumber(),"");
        StringBuffer ftpFilePath = new StringBuffer(ArchiveConstant.FTP_PATH_PREFIX).append(ArchiveConstant.FTP_PATH_CSGY).append(pubSubClass).append("/")
                .append(singleCode).append("/").append(billVersion).append("/").append(fileName).append(".xlsx");
        AttachmentUploadVo attachmentUploadVo = archiveCommonService.downloadFromFtpAndUpload(taskSendArchive, fileName+".xlsx", ftpFilePath.toString());
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(attachmentUploadVo);
        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.TEST_PROCESS.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.TEST_PROCESS.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(testProcessDTO,fileName);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        log.info("测试工艺单归档end......");
        return item;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        log.info("test process order archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveTestProcessDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.TEST_PROCESS.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        log.info("test process order archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        ServiceData<Page<ArchiveTestProcessDTO>> listServiceData = archiveTestProcessClient.selectTestProcessList(archiveQueryParamDTO);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            log.error("调用服务获取待归档测试工艺单据返回报错：{}", msg);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UNARCHIVE_TESTPROCESS_SERVERERROR);
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveTestProcessDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId(String.valueOf(dataItem.getHeaderId()));
            archiveItem.setBillNo(String.valueOf(dataItem.getBillNumber()));
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }

    private ArchiveBusinessVo buildBusiness(ArchiveTestProcessDTO testProcessDTO,String fileName) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        String billNumber = (String)testProcessDTO.getBillNumber();
        String createBy = (String)testProcessDTO.getCreateBy();
        String gjc=getGjc(testProcessDTO);
        Date creationDate = DateUtil.convertStringToDate((String)testProcessDTO.getCreationDateStr(),DateUtils.DATE_FORMAT_YEAR_MONTH_DAY);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDeptGz(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(createBy)));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(creationDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(creationDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(billNumber);
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(fileName);
        // 关键词
        businessVo.setKeyWord(gjc);
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.TEST_PROCESS.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }

    private String getGjc(ArchiveTestProcessDTO testProcessDTO){
        //文件编号，手机型号，国家
        String billNumber = Objects.toString(testProcessDTO.getBillNumber(),"");
        String pubSubClass =Objects.toString(testProcessDTO.getProductSubClass(),"");
        String countryName = Objects.toString(testProcessDTO.getCountryName(),"");
        StringBuffer gjc = new StringBuffer("");
        gjc.append(billNumber).append(",").append(pubSubClass).append(",").append(countryName);
        return gjc.toString();
    }

    private ArchiveTestProcessDTO selectTestProcessById(ArchiveTaskSend taskSendArchive){
        ServiceData<ArchiveTestProcessDTO> serviceData = archiveTestProcessClient.selectTestProcessById(taskSendArchive.getBillId());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            log.error("调用服务获取测试工艺单返回报错：{}",msg);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_TESTPROCESS_SERVERERROR);
        }
        return serviceData.getBo();
    }



}
