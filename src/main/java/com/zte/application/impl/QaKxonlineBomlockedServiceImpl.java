package com.zte.application.impl;

import com.zte.application.QaKxonlineBomlockedService;
import com.zte.domain.model.QaKxonlineBomlockedRepository;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("qaKxonlineBomlockedService")
@DataSource(value = DatabaseType.DB2)
public class QaKxonlineBomlockedServiceImpl implements QaKxonlineBomlockedService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QaKxonlineBomlockedRepository qaKxonlineBomlockedrepository;

    @Override
    public Page<QaKxonlineBomlockedEntityDTO> pageList(QaKxonlineBomlockedEntityDTO record) throws Exception{
        Page<QaKxonlineBomlockedEntityDTO> pageInfo = new Page<>(record.getPage(), record.getRows());
        pageInfo.setParams(record);
        List<QaKxonlineBomlockedEntityDTO> qaKxonlineBomlockedlist = qaKxonlineBomlockedrepository.pageList(pageInfo);
        pageInfo.setRows(qaKxonlineBomlockedlist);
        return pageInfo;
    }
}