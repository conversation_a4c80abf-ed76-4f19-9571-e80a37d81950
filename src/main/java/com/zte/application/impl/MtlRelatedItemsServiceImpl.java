package com.zte.application.impl;

import com.zte.application.MtlRelatedItemsService;
import com.zte.domain.model.MtlRelatedItemsRepository;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service("mtlRelatedItemsService")
@DataSource(value = DatabaseType.SFC)
public class MtlRelatedItemsServiceImpl implements MtlRelatedItemsService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MtlRelatedItemsRepository mtlRelatedItemsrepository;

    @Override
    public List<MtlRelatedItemsEntityDTO> getList(MtlRelatedItemsEntityDTO record) throws Exception{
        return mtlRelatedItemsrepository.getList(record);
    }

    @Override
    public List<MtlRelatedItemsEntityDTO> getItemInfoList(MtlRelatedItemsEntityDTO record) throws Exception{
        return mtlRelatedItemsrepository.getItemInfoList(record);
    }

}