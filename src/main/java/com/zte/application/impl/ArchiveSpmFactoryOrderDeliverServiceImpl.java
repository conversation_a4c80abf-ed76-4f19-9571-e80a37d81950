package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveSpmFactoryOrderDeliverDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchiveSpmFactoryOrderDeliverAssembler;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/22
 * @description : 外协订单下达归档
 */
@Service("archiveSpmFactoryOrderDeliverService")
public class ArchiveSpmFactoryOrderDeliverServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchiveSpmFactoryOrderDeliverDataService archiveSpmFactoryOrderDeliverDataService;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
            return null;
        }
        ArchiveSpmFactoryOrderDeliverDTO dto = archiveSpmFactoryOrderDeliverDataService.getByRecordId(taskSendArchive.getBillId());
        if(null == dto){
            return null;
        }
        return initArchiveItem(taskSendArchive,dto);
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {

        Page<ArchiveTaskSend> page = new Page<>();
        page.setPageSize(archiveQueryParamDTO.getRows());
        page.setCurrent(archiveQueryParamDTO.getPage());

        Page<ArchiveSpmFactoryOrderDeliverDTO> pageInfo = archiveSpmFactoryOrderDeliverDataService.getByDateRange(archiveQueryParamDTO);
        page.setTotal(pageInfo.getTotal());

        List<ArchiveSpmFactoryOrderDeliverDTO> cces = pageInfo.getRows();
        List<ArchiveTaskSend> list = cces.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(ArchiveBusinessTypeEnum.SPM_PROD_PLAN.getCode());
            archiveItem.setBillId(dataItem.getRecordId());
            archiveItem.setBillNo(dataItem.getExpenseitem());
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            archiveItem.setBillReserves(ArchiveConstant.BLANK);
            return archiveItem;
        }).collect(Collectors.toList());
        page.setRows(list);
        return page;
    }

    /**
     * 初始化归档项
     *
     * @param taskSendArchive 归档任务
     * @param dto 生产任务查询归档数据
     * @return 归档项
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchiveSpmFactoryOrderDeliverDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        String businessId = ArchiveBusinessTypeEnum.SPM_PROD_PLAN.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.SPM_PROD_PLAN.getName());
        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNameId(dto.getLastUpdatedBy());
        ArchiveSpmFactoryOrderDeliverAssembler.initItemContent(item, dto, pubHrvOrg);
        //归档生产指令数据
        archiveDoc(item,dto,taskSendArchive);
        return item;
    }

    /**
     * 归档附件
     * @param item 归档项
     * @param dto 外协订单下达
     * @param taskSendArchive 归档推送任务
     */
    private void archiveDoc(ArchiveReq.ArchiveItem item, ArchiveSpmFactoryOrderDeliverDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        String fileTitle = ArchiveConstant.MODULE_TITLE_SPM_FACTORY_ORDERDELIVER + dto.getExpenseitem();
        //查询结果
        AttachmentUploadVo planUpload = archiveSpmProdPlan(taskSendArchive,dto,fileTitle+ArchiveConstant.SPM_PROD_PLAN);
        //需求回货
        AttachmentUploadVo returnUpload = archiveSpmFactoryReturn(taskSendArchive,dto,fileTitle+ArchiveConstant.SPM_FACTORY_RETURN);
        //外协订单
        AttachmentUploadVo processUpload = archivespmFactoryProcessDefine(taskSendArchive,dto,fileTitle + ArchiveConstant.SPM_FACTORY_PROCESS_DEFINE);

        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(planUpload);
        attachmentDataList.add(returnUpload);
        attachmentDataList.add(processUpload);

        List<ArchiveReq.ArchiveItemDoc> docs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        item.setItemDocs(docs);
    }

    /**
     * 外包工序导出
     * @param taskSendArchive 推送任务
     * @param dto 外协订单
     * @param fileTitle 文件标题
     * @return 文件上传信息
     */
    private AttachmentUploadVo archivespmFactoryProcessDefine(ArchiveTaskSend taskSendArchive, ArchiveSpmFactoryOrderDeliverDTO dto, String fileTitle) throws Exception {
        List<ArchiveSpmFactoryReturnDTO> dtos = archiveSpmFactoryOrderDeliverDataService.getSpmFactoryProcessDefineByOrderId(dto.getRecordId());
        return archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.SPM_FACTORY_PROCESS_DEFINE_MAP,dtos);
    }

    private AttachmentUploadVo archiveSpmProdPlan(ArchiveTaskSend taskSendArchive, ArchiveSpmFactoryOrderDeliverDTO dto, String fileTitle) throws Exception {
        List<ArchiveSpmFactoryOrderDeliverDTO> dtos = archiveSpmFactoryOrderDeliverDataService.getSpmProdPlanByRecordId(dto.getRecordId(),dto.getOrganizationId());
        return archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.SPM_PROD_PLAN_MAP,dtos);
    }

    private AttachmentUploadVo archiveSpmFactoryReturn(ArchiveTaskSend taskSendArchive, ArchiveSpmFactoryOrderDeliverDTO dto, String fileTitle) throws Exception {
        List<ArchiveSpmFactoryReturnDTO> dtos = archiveSpmFactoryOrderDeliverDataService.getSpmFactoryReturnByRecordId(dto.getRecordId());
        return archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.SPM_FACTORY_RETURN_MAP,dtos);
    }

}
