package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveGetMaterialClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveGetMaterialServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveGetMaterialServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveGetMaterialClient archiveGetMaterialClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("成品返工领料归档start......");
        String fileName = ArchiveBusinessTypeEnum.GET_MATERIAL.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        ArchiveGetMaterialDTO archiveGetMaterialDTO = selectGetMaterialByGetNo(taskSendArchive);
        List<ArchiveGetMaterialBoxDetailDTO> archiveGetMaterialBoxDetailDTOS = selectGetMaterialBoxDetailByGetNo(taskSendArchive);
        //单据头信息
        AttachmentUploadVo getMaterialExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.GET_MATERIAL_HEADER_MAP, Collections.singletonList(archiveGetMaterialDTO));
        //单据明细信息
        AttachmentUploadVo getMaterialBoxDetailsExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", ArchiveConstant.GET_MATERIAL_BOX_DETAIL_MAP, archiveGetMaterialBoxDetailDTOS);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(getMaterialExcelUploadVo);
        attachmentDataList.add(getMaterialBoxDetailsExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.GET_MATERIAL.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.GET_MATERIAL.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveGetMaterialDTO);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("成品返工领料归档end......");
        return item;
    }

    private ArchiveGetMaterialDTO selectGetMaterialByGetNo(ArchiveTaskSend taskSendArchive){
        ServiceData<ArchiveGetMaterialDTO> serviceData = archiveGetMaterialClient.selectGetMaterialByGetNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务根据送检单号查询检验单信息返回报错：{}",msg);
            throw new RouteException("调用服务根据送检单号查询检验单信息返回报错");
        }
        return serviceData.getBo();
    }

    private List<ArchiveGetMaterialBoxDetailDTO> selectGetMaterialBoxDetailByGetNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveGetMaterialBoxDetailDTO>> serviceData = archiveGetMaterialClient.selectGetMaterialBoxDetailByGetNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务根据单号查询明细返回报错：{}",msg);
            return new ArrayList<>();
        }
        List<ArchiveGetMaterialBoxDetailDTO> archiveGetMaterialBoxDetailDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveGetMaterialBoxDetailDTO.class);
        if(CollectionUtils.isEmpty(archiveGetMaterialBoxDetailDTOS)){
            return new ArrayList<>();
        }
        return archiveGetMaterialBoxDetailDTOS;
    }

    private ArchiveBusinessVo buildBusiness(ArchiveGetMaterialDTO archiveGetMaterialDTO) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        Date applyDate = DateUtil.convertStringToDate(archiveGetMaterialDTO.getApplyDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserId(CommonUtil.regexUserId(archiveGetMaterialDTO.getCreatedBy())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(applyDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(applyDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveGetMaterialDTO.getGetNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.GET_MATERIAL.getName()+"-"+archiveGetMaterialDTO.getGetNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.GET_MATERIAL.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.GET_MATERIAL.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        // 扩展字段1
        businessVo.setOther(archiveGetMaterialDTO.getWipEntityName());
        return businessVo;
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("get material archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveGetMaterialDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.GET_MATERIAL.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("get material archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        ServiceData<Page<ArchiveGetMaterialDTO>> listServiceData = archiveGetMaterialClient.selectGetMaterialList(archiveQueryParamDTO);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档的成品返工领料单据返回报错：{}", msg);
            throw new RouteException("调用服务获取待归档的成品返工领料单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveGetMaterialDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem.getGetNo());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
