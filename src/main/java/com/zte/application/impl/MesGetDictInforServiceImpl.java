package com.zte.application.impl;


import com.zte.application.MesGetDictInforService;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.interfaces.dto.EntityWeightDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@DataSource(DatabaseType.SFC)
public class MesGetDictInforServiceImpl implements MesGetDictInforService {


    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;

    /**
     * <AUTHOR>
     * 获取数据字典参数
     * @param lookUpType=例如 8000320
     *  返回：map<"lookup_code", "value"> 例如：lookup_code=800032000001，value="url"
     */
    public Map<String, Object> getDict(String lookUpType) {
        List<EntityWeightDTO> dictList = mesGetDictInforRepository.getDict(lookUpType);
        if (!CollectionUtils.isEmpty(dictList)) {
            Map<String, Object> outMap = new HashMap<>(dictList.size());
            for (EntityWeightDTO entityWeightDTO : dictList) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            return outMap;
        }

        return null;
    }

    @Override
    public String getDicDescription(String lookUpCode) {
        return mesGetDictInforRepository.getDicDescription(lookUpCode);
    }

    @Override
    public int updateArchiveFlag(Map<String, String> params) {
        return mesGetDictInforRepository.updateArchiveFlag(params);
    }

}
