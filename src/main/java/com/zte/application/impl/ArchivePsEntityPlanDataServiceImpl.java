package com.zte.application.impl;

import com.zte.application.ArchivePsEntityPlanDataService;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchivePsEntityPlanRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/20
 * @description : 生产指令查询归档服务实现类
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchivePsEntityPlanDataServiceImpl implements ArchivePsEntityPlanDataService {

    @Autowired
    ArchivePsEntityPlanRepository psEntityPlanRepository;

    @Override
    public ArchivePsEntityPlanDTO getByPlanNumber(String billNo) {
        if(StringUtils.isBlank(billNo)){
            return null;
        }
        return psEntityPlanRepository.getByPlanNumber(billNo);
    }

    @Override
    public List<ArchivePsEntityPlanDetailDTO> getDetailByEntityPlan(ArchivePsEntityPlanDTO dto) {
        if(null == dto){
            return Collections.emptyList();
        }
        Map<String,Object> map = new HashMap<>();
        map.put("dto",dto);
        psEntityPlanRepository.callDwProductReportPkg(map);
        return (List<ArchivePsEntityPlanDetailDTO>)map.get(ArchiveConstant.RESULT);
    }

    @Override
    public Page<ArchivePsEntityPlanDTO> getByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO) {
        if(null == archiveQueryParamDTO || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }

        Page<ArchivePsEntityPlanDTO> page = new Page<>(archiveQueryParamDTO.getPage(), archiveQueryParamDTO.getRows());
        page.setParams(archiveQueryParamDTO);
        List<ArchivePsEntityPlanDTO> rows = psEntityPlanRepository.getByDateRange(page);
        page.setRows(rows);
        return page;
    }
}
