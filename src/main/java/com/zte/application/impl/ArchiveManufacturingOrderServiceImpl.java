package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.*;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveManufacturingOrderClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveManufacturingOrderServiceImpl implements ArchiveBusinessService {

    @Autowired
    private ArchiveManufacturingOrderClient archiveManufacturingOrderClient;

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Value("${archive.ftp.host}")
    private String ftpHost;


    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        log.info("制造通知单归档start......");
        Map<String, Object> manOrderMap = selectManufacturingOrderById(taskSendArchive);
        String filePath = this.getFilePath(manOrderMap);
        String ftpFilePath = filePath.replaceAll(ArchiveConstant.FTP + Objects.toString(ftpHost,""), ArchiveConstant.FTP_PATH_PREFIX);
        String fileName = ftpFilePath.substring(ftpFilePath.lastIndexOf("/") + 1);
        AttachmentUploadVo attachmentUploadVo = archiveCommonService.downloadFromFtpAndUpload(taskSendArchive, fileName, ftpFilePath);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(attachmentUploadVo);
        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.MANUFACTURING_ORDER.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.MANUFACTURING_ORDER.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(manOrderMap,fileName);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        log.info("制造通知单归档end......");
        return item;
    }

    public  String getFilePath(Map<String, Object> manOrderMap) {
        Object filePathObj = manOrderMap.get("FILE_PATH");
        String filePath = filePathObj == null?"":filePathObj.toString();
        return filePath;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        log.info("manufacturing order archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<Map<String,Object>> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.MANUFACTURING_ORDER.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        log.info("manufacturing order archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        ServiceData<Page<Map<String,Object>>> listServiceData = archiveManufacturingOrderClient.selectManufacturingOrderList(archiveQueryParamDTO);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            log.error("调用服务获取待归档制造通知单据返回报错：{}", msg);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.UNARCHIVE_MANUFACTURINGORDER_SERVERERROR);
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<Map<String,Object>> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId(String.valueOf(dataItem.get("MO_HEADER_ID")));
            archiveItem.setBillNo(String.valueOf(dataItem.get("MO_NO")));
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }

    private ArchiveBusinessVo buildBusiness(Map<String,Object> manOrderMap,String fileName) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        String moNo = (String)manOrderMap.get("MO_NO");
        String createBy = (String)manOrderMap.get("CREATE_BY");
        String gjc=getGjc(manOrderMap);
        Date creationDate = DateUtil.convertStringToDate((String)manOrderMap.get("CREATION_DATE_STR"),DateUtils.DATE_FORMAT_YEAR_MONTH_DAY);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDeptGz(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(createBy)));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(com.zte.common.utils.DateUtil.getYear(creationDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(com.zte.common.utils.DateUtil.convertDateToString(creationDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(moNo);
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(fileName.replace(".xlsx",""));
        // 关键词
        businessVo.setKeyWord(gjc);
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.MANUFACTURING_ORDER.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }

    private String getGjc(Map<String,Object> manOrderMap){
        String pubSubClass = (String)manOrderMap.get("PRODUCT_SUB_CLASS");
        String countryName = Objects.toString(manOrderMap.get("COUNTRY_NAME"),"");
        String itemNo = Objects.toString(manOrderMap.get("ITEM_NO"),"");
        StringBuffer gjc = new StringBuffer("");
        gjc.append(pubSubClass).append(",").append(countryName).append(",").append(itemNo);
        return gjc.toString();
    }

    private Map<String,Object> selectManufacturingOrderById(ArchiveTaskSend taskSendArchive){
        ServiceData<Map<String,Object>> serviceData = archiveManufacturingOrderClient.selectManufacturingOrderById(taskSendArchive.getBillId());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            log.error("调用服务获取制造通知单返回报错：{}",msg);
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ARCHIVE_MANUFACTURINGORDER_SERVERERROR);
        }
        return serviceData.getBo();
    }



}
