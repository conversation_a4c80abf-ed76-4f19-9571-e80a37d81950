package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.CompleteMachineDataLogService;
import com.zte.application.datawb.EntryBoxAppliedOthersService;
import com.zte.application.datawb.MaterialConfigBindService;
import com.zte.application.datawb.TaskToBeQueriedService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.ZmsCompleteMachineHeadRepository;
import com.zte.domain.model.ZmsCompleteMachineLineRepository;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.EmailUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;


@Service("completeMachineDataLogService")
@DataSource(DatabaseType.SFC)
public class CompleteMachineDataLogServiceImpl implements CompleteMachineDataLogService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ZmsCompleteMachineHeadRepository zmsCompleteMachineHeadRepository;

    @Autowired
    private ZmsCompleteMachineLineRepository zmsCompleteMachineLineRepository;

    @Autowired
    private TaskToBeQueriedService taskToBeQueriedService;

    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private MaterialConfigBindService materialConfigBindService;

    @Autowired
    private EmailUtils emailUtils;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Autowired
    private EntryBoxAppliedOthersService entryBoxAppliedOthersService;
    @Autowired
    private MdsRemoteService mdsRemoteService;
    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Autowired
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;
    @Autowired
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Autowired
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;


    public ZmsStationSSPDTO querySspTaskInfoByte(String sn) throws Exception {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(SN, sn);
            String zSUrl = zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200026);
            String strToken = zmsStationLogUploadService.getToken();

            String bo = zmsStationLogUploadService.getZsStationLog(paramMap, zSUrl, strToken);
            ObjectMapper objectMapper = new ObjectMapper();
            ZmsStationSSPDTO zmsStationSSPDTO = objectMapper.readValue(bo, ZmsStationSSPDTO.class);
            return zmsStationSSPDTO;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void uploadCompleteMachineData(UploadCompleteMachineDataDTO dto) throws Exception {

        //查询任务合同信息
        boolean execFlag = true;
        Integer current = NumConstant.NUM_ONE;
        List<String> organizationIdList = this.getLookupMeanList(Constant.LOOKUP_TYPE_8240040);
        List<String> taskLikeList = this.getLookupMeanList(Constant.LOOKUP_TYPE_8240045);
        List<String> subStatusList = this.getLookupMeanList(Constant.LOOKUP_TYPE_8240043);
        //配置上传的客户部件型号
        List<String> modelNumberList = this.getLookupMeanList(Constant.LOOKUP_TYPE_8240044);
        dto.setUserAddressList(mesGetDictInforRepository.getDict(Constant.LOOKUP_TYPE_3020035).stream().map(f -> f.getDescription()).collect(Collectors.toList()));
        dto.setOrganizationIdList(organizationIdList);
        dto.setTaskLike(taskLikeList.get(NumConstant.NUM_ZERO));
        dto.setSubStatusList(subStatusList);
        dto.setModelNumberList(modelNumberList);
        StringBuilder mailer = getMailer();

        while (execFlag) {
            dto.setCurrent(current);
            List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList = taskToBeQueriedService.queryTaskContractInformationPage(dto);
            if (CollectionUtils.isEmpty(cpmContractEntitiesDTOList)) {
                return;
            }
            current++;
            this.uploadCompleteMachineData(mailer, cpmContractEntitiesDTOList, dto);
        }

    }

    /**
     * 获取错误邮件发送人
     *
     * @return
     */
    private StringBuilder getMailer() {
        List<String> recipientList = this.getLookupMeanList(Constant.LOOKUP_TYPE_8240041);
        //错误邮件人
        StringBuilder mailer = new StringBuilder();
        for (String recipenter : recipientList) {
            if (StringUtils.contains(recipenter, Constant.MAILBOX_SUFFIX)) {
                mailer.append(recipenter + Constant.SEMICOLON);
            } else {
                mailer.append(recipenter + Constant.MAILBOX_SUFFIX + Constant.SEMICOLON);
            }
        }
        return mailer;
    }

    /**
     * 获取相关配置
     *
     * @param lookupType
     * @return
     */
    private List<String> getLookupMeanList(String lookupType) {
        List<SysLookupValues> sysLookupValuesList = wsmAssembleLinesService.getSysLookupValues(lookupType);
        if (CollectionUtils.isEmpty(sysLookupValuesList)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{lookupType});
        }
        return sysLookupValuesList.stream().filter(e -> StringUtils.isNotEmpty(e.getLookupMeaning())).map(e -> e.getLookupMeaning()).distinct().collect(Collectors.toList());
    }

    /* Started by AICoder, pid:cb57d880c5b248669907be2504eaf888 */
    /*
     * 灰度批次号：HD0000000505，M89M1-B1DD2M；082630100438指定立讯
     * grayplanno 改为截取 灰度批次号：与第一个英文逗号之间的内容，如果没有灰度批次号字样，则传空值
     * */
    public String getGrayPlanNo(String grayPlanNo) {
        if (StringUtils.isEmpty(grayPlanNo)) {
            return STRING_EMPTY;
        }
        int indexMaohao = grayPlanNo.indexOf(STRING_MAOHAO);
        if (indexMaohao < INT_0) {
            return STRING_EMPTY;
        }
        int indexDouhao = grayPlanNo.indexOf(COMMA);
        if (indexDouhao < INT_0) {
            return STRING_EMPTY;
        }
        if (indexMaohao > indexDouhao) {
            return STRING_EMPTY;
        }
        return grayPlanNo.substring(indexMaohao + INT_1, indexDouhao);
    }/* Ended by AICoder, pid:cb57d880c5b248669907be2504eaf888 */

    public void uploadCompleteMachineData(StringBuilder mailer, List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList, UploadCompleteMachineDataDTO dto) throws Exception {
        String empNo = dto.getEmpNo();
        List<String> modelNumberList = dto.getModelNumberList();
        //按任务循环处理
        List<CompleteMachineDataLogEntityDTO> insertList = new ArrayList<>();

        List<String> listEntityName = cpmContractEntitiesDTOList.stream().map(CpmContractEntitiesDTO::getEntityName).collect(Collectors.toList());
        String descLike = mesGetDictInforRepository.getDicDescription(Constant.LOOKUP_TYPE_824005900003);
        String intercept = mesGetDictInforRepository.getDicDescription(Constant.LOOKUP_TYPE_824005900008);
        List<ZmsCbomInfoDTO> configDescList = zmsDeviceInventoryUploadRepository.getConfigDesc(listEntityName, descLike, intercept);

        for (CpmContractEntitiesDTO cpmContractEntitiesDTO : cpmContractEntitiesDTOList) {
            //获取任务对应装配物料信息以及服务器sn,主板sn,客户定义的机型名称:服务器sn对应的
            CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = this.setServerMotherboardSn(cpmContractEntitiesDTO);

            ZmsStationSSPDTO zmsStationSSPDTO = querySspTaskInfoByte(completeMachineDataLogEntityDTO.getServerSn());
            if (zmsStationSSPDTO != null) {
                completeMachineDataLogEntityDTO.setAssetNo(zmsStationSSPDTO.getAssetNum());
                completeMachineDataLogEntityDTO.setVAssetNo(zmsStationSSPDTO.getNodeAssetNum());
                if (zmsStationSSPDTO.getMainboard() != null) {
                    completeMachineDataLogEntityDTO.setBiosVersion(zmsStationSSPDTO.getMainboard().getBiosVer());
                    completeMachineDataLogEntityDTO.setBmcVersion(zmsStationSSPDTO.getMainboard().getBmcFirmwareVer());
                }
            }

            ZmsCbomInfoDTO zmsCbomInfoDTO = configDescList.stream().filter(f -> f.getEntityName().equals(cpmContractEntitiesDTO.getEntityName())).findFirst().orElse(null);
            if (zmsCbomInfoDTO != null) {
                completeMachineDataLogEntityDTO.setCombo(zmsCbomInfoDTO.getCbomNameCn());
            }
            //获取合同信息
            List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList = entryBoxAppliedOthersService.getTaskContractInformation(cpmContractEntitiesDTO.getEntityId());
            if (!CollectionUtils.isEmpty(completeMachineDataLogEntityDTOList)) {
                CompleteMachineDataLogEntityDTO tempDTO = completeMachineDataLogEntityDTOList.get(NumConstant.NUM_ZERO);
                completeMachineDataLogEntityDTO.setGrayPlanNo(getGrayPlanNo(tempDTO.getCombo()));
                completeMachineDataLogEntityDTO.setOrderTime(tempDTO.getOrderTime());
                completeMachineDataLogEntityDTO.setManufacturerTime(tempDTO.getManufacturerTime());
                completeMachineDataLogEntityDTO.setFactoryLocation(Constant.ZTE + tempDTO.getFactoryLocation());
                completeMachineDataLogEntityDTO.setFactoryOrderId(tempDTO.getFactoryOrderId());
                completeMachineDataLogEntityDTO.setOrderState(tempDTO.getOrderState());
                completeMachineDataLogEntityDTO.setCreateBy(empNo);
            }
            //装配包装时间
            List<TaskHistorySubStateDTO> taskHistorySubStateDTOList = entryBoxAppliedOthersService.getEarliestTimeForAssemblyPackaging(cpmContractEntitiesDTO.getEntityId());
            this.setAssemblyPackagingTime(completeMachineDataLogEntityDTO, completeMachineDataLogEntityDTOList, taskHistorySubStateDTOList);
            this.generateCompleteMachineDataLogLineEntityDTOList(cpmContractEntitiesDTO, completeMachineDataLogEntityDTO, modelNumberList, zmsStationSSPDTO);
            insertList.add(completeMachineDataLogEntityDTO);
        }
        //错误数据发送邮件
        List<CompleteMachineDataLogEntityDTO> errorList = insertList.stream().filter(e -> !CollectionUtils.isEmpty(e.getErrorList())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorList)) {
            emailUtils.sendMail(mailer.toString(), Constant.MES_MACHINE_INFORMATION_FEEDBACK, Constant.STRING_EMPTY,
                    this.getEmailContent(errorList), Constant.STRING_EMPTY);
        }
        //正常数据写表并调中心工厂接口推送
        List<CompleteMachineDataLogEntityDTO> correctDataList = insertList.stream().filter(e -> CollectionUtils.isEmpty(e.getErrorList())).collect(Collectors.toList());
        //批量新增
        batchInsert(correctDataList, empNo);
    }

    /**
     * 批量新增
     *
     * @param correctDataList
     */
    public void batchInsert(List<CompleteMachineDataLogEntityDTO> correctDataList, String empNo) throws Exception {
        if (StringUtils.isEmpty(empNo)) {
            empNo = Constant.SYSTEM;
        }
        if (CollectionUtils.isEmpty(correctDataList)) {
            return;
        }
        //推送B2B
        pushDataToB2B(empNo, correctDataList);
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        //写日志数据
        correctDataList.forEach(p -> {
            p.setId(UUID.randomUUID().toString());
        });
        for (List<CompleteMachineDataLogEntityDTO> tempInsertList : CommonUtils.splitList(correctDataList, Constant.INT_100)) {
            List<String> serviceSnList = tempInsertList.stream().filter(e -> StringUtils.isNotEmpty(e.getServerSn())).map(e -> e.getServerSn()).distinct().collect(Collectors.toList());
            zmsCompleteMachineHeadRepository.batchDelete(serviceSnList);
            zmsCompleteMachineLineRepository.batchDelete(serviceSnList);
            zmsCompleteMachineHeadRepository.batchInsert(tempInsertList);
        }
        for (CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO : correctDataList) {
            List<CompleteMachineDataLogLineEntityDTO> components = completeMachineDataLogEntityDTO.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                continue;
            }
            components.forEach(p -> p.setId(UUID.randomUUID().toString()));
            for (List<CompleteMachineDataLogLineEntityDTO> completeMachineDataLogLineEntityDTOList : CommonUtils.splitList(components, Constant.INT_100)) {
                zmsCompleteMachineLineRepository.batchInsert(completeMachineDataLogLineEntityDTOList);
            }
        }
    }

    /**
     * 组装数据推送B2B
     *
     * @param empNo
     * @param tempInsertList
     * @throws Exception
     */
    private void pushDataToB2B(String empNo, List<CompleteMachineDataLogEntityDTO> tempInsertList) throws Exception {
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO : tempInsertList) {
            CompleteMachineDataForB2BEntityDTO completeMachineDataForB2BEntityDTO = new CompleteMachineDataForB2BEntityDTO();
            BeanUtils.copyProperties(completeMachineDataLogEntityDTO, completeMachineDataForB2BEntityDTO);

            completeMachineDataForB2BEntityDTO.setOrderId(completeMachineDataLogEntityDTO.getOrderId());

            Date orderTime = completeMachineDataLogEntityDTO.getOrderTime();
            completeMachineDataForB2BEntityDTO.setOrderTime(orderTime == null ? 0L : orderTime.getTime() / NumConstant.NUM_ONE_THOUSAND);

            Date manufacturerTime = completeMachineDataLogEntityDTO.getManufacturerTime();
            completeMachineDataForB2BEntityDTO.setManufacturerTime(manufacturerTime == null ? 0L : manufacturerTime.getTime() / NumConstant.NUM_ONE_THOUSAND);

            Date factoryOrderTime = completeMachineDataLogEntityDTO.getFactoryOrderTime();
            completeMachineDataForB2BEntityDTO.setFactoryOrderTime(factoryOrderTime == null ? 0L : factoryOrderTime.getTime() / NumConstant.NUM_ONE_THOUSAND);
            Date acceptanceTime = completeMachineDataLogEntityDTO.getAcceptanceTime();
            completeMachineDataForB2BEntityDTO.setAcceptanceTime(acceptanceTime == null ? 0L : acceptanceTime.getTime() / NumConstant.NUM_ONE_THOUSAND);
            Date packageTime = completeMachineDataLogEntityDTO.getPackageTime();
            completeMachineDataForB2BEntityDTO.setPackageTime(packageTime == null ? 0L : packageTime.getTime() / NumConstant.NUM_ONE_THOUSAND);
            completeMachineDataForB2BEntityDTO.setAssetNo(completeMachineDataLogEntityDTO.getAssetNo());
            completeMachineDataForB2BEntityDTO.setVAssetNo(completeMachineDataLogEntityDTO.getVAssetNo());
            completeMachineDataForB2BEntityDTO.setGrayPlanNo(completeMachineDataLogEntityDTO.getGrayPlanNo());
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString());
            customerDataLogDTO.setOrigin(Constant.MES);
            CustomerItemsDTO customerItemsDTO = completeMachineDataLogEntityDTO.getCustomerItemsDTO();
            if (customerItemsDTO != null) {
                customerDataLogDTO.setCustomerName(customerItemsDTO.getCustomerName());
                customerDataLogDTO.setProjectPhase(customerItemsDTO.getProjectPhase());
                customerDataLogDTO.setProjectName(customerItemsDTO.getProjectName());
                customerDataLogDTO.setCooperationMode(customerItemsDTO.getCooperationMode());
                customerDataLogDTO.setItemNo(customerItemsDTO.getZteCode());
            }
            customerDataLogDTO.setMessageType(MESSAGE_TYPE_B2B);
            customerDataLogDTO.setContractNo(completeMachineDataLogEntityDTO.getOrderId());
            customerDataLogDTO.setTaskNo(completeMachineDataLogEntityDTO.getFactoryOrderId());
            customerDataLogDTO.setSn(completeMachineDataLogEntityDTO.getServerSn());
            customerDataLogDTO.setJsonData(JSON.toJSONString(completeMachineDataForB2BEntityDTO));
            customerDataLogDTO.setFactoryId(NumConstant.NUM_ZERO);
            customerDataLogDTO.setCreateBy(empNo);
            customerDataLogDTO.setLastUpdatedBy(empNo);
            dataList.add(customerDataLogDTO);
        }
        for (List<CustomerDataLogDTO> tempList : CommonUtils.splitList(dataList, Constant.BATCH_QUERY_SIZE)) {
            centerfactoryRemoteService.pushDataToB2B(tempList);
        }
    }

    /**
     * 获取itemCode与SN等字段的map
     */
    public List<ZmsStationSSPNICListDTO> getSubItemCodeSNMap(ZmsStationSSPDTO zmsStationSSPDTO) {
        List<ZmsStationSSPNICListDTO> itemCodeSNMap = new ArrayList<>();
        ZmsStationSSPNICListDTO dto = null;
        if (zmsStationSSPDTO.getCpuList() != null) {
            for (ZmsStationSSPCpuListDTO item : zmsStationSSPDTO.getCpuList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getGpuList() != null) {
            for (ZmsStationSSPGPUListDTO item : zmsStationSSPDTO.getGpuList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getPsuList() != null) {
            for (ZmsStationSSPPSUListDTO item : zmsStationSSPDTO.getPsuList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getRaidList() != null) {
            for (ZmsStationSSPRaidListDTO item : zmsStationSSPDTO.getRaidList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        return itemCodeSNMap;
    }

    /**
     * 获取itemCode与SN等字段的map
     */
    public List<ZmsStationSSPNICListDTO> getItemCodeSNMap(ZmsStationSSPDTO zmsStationSSPDTO) {
        List<ZmsStationSSPNICListDTO> itemCodeSNMap = new ArrayList<>();
        if (zmsStationSSPDTO == null) {
            return itemCodeSNMap;
        }
        ZmsStationSSPNICListDTO dto = null;
        if (zmsStationSSPDTO.getNicList() != null) {
            for (ZmsStationSSPNICListDTO item : zmsStationSSPDTO.getNicList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                if (item.getExtraParams() != null) {
                    dto.setPort1Mac(item.getExtraParams().getMacAddr());
                }
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getMemoryList() != null) {
            for (ZmsStationSSPMemoryListDTO item : zmsStationSSPDTO.getMemoryList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getDiskList() != null) {
            for (ZmsStationSSPDiskListDTO item : zmsStationSSPDTO.getDiskList()) {
                dto = new ZmsStationSSPNICListDTO();
                dto.setItemcode(item.getItemcode());
                dto.setSn(item.getSn());
                dto.setSlot(item.getSlot());
                itemCodeSNMap.add(dto);
            }
        }
        if (zmsStationSSPDTO.getMainboard() != null) {
            dto = new ZmsStationSSPNICListDTO();
            dto.setItemcode(zmsStationSSPDTO.getMainboard().getItemcode());
            dto.setSn(zmsStationSSPDTO.getMainboard().getSn());
            dto.setSlot(zmsStationSSPDTO.getMainboard().getSlot());
            itemCodeSNMap.add(dto);
        }
        itemCodeSNMap.addAll(getSubItemCodeSNMap(zmsStationSSPDTO));
        return itemCodeSNMap;
    }

    /**
     * 获取itemCode与SN等字段的map
     */
    public void setCompleteMachineComponentsDTO(CompleteMachineDataLogLineEntityDTO completeMachineComponentsDTO, List<ZmsStationSSPNICListDTO> itemCodeSNMap, String itemCode, ZmsStationSSPDTO zmsStationSSPDTO) {
        ZmsStationSSPNICListDTO itemCodeSN = itemCodeSNMap.stream().filter(f -> f.getItemcode().equals(itemCode)).findFirst().orElse(null);
        if (itemCodeSN != null) {
            completeMachineComponentsDTO.setOriginComponentSn(itemCodeSN.getSn());
            completeMachineComponentsDTO.setMacVersion(itemCodeSN.getPort1Mac());
            if (StringUtils.isEmpty(itemCodeSN.getSlot())) {
                completeMachineComponentsDTO.setSlot(SOLT_IS_EMPTY_ELSE_IT);
            } else {
                completeMachineComponentsDTO.setSlot(itemCodeSN.getSlot());
            }
            itemCodeSNMap.remove(itemCodeSN);
        }
        if (zmsStationSSPDTO != null && zmsStationSSPDTO.getMainboard() != null) {
            completeMachineComponentsDTO.setBiosVersion(zmsStationSSPDTO.getMainboard().getBiosVer());
            completeMachineComponentsDTO.setBmcVersion(zmsStationSSPDTO.getMainboard().getBmcFirmwareVer());
            completeMachineComponentsDTO.setCpldVersion(zmsStationSSPDTO.getMainboard().getCpldVer());
        }
    }

    /**
     * 组装物料数据
     *
     * @param cpmContractEntitiesDTO
     * @param completeMachineDataLogEntityDTO
     * @throws Exception
     */
    private void generateCompleteMachineDataLogLineEntityDTOList(CpmContractEntitiesDTO cpmContractEntitiesDTO, CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, List<String> modelNumberList, ZmsStationSSPDTO zmsStationSSPDTO) throws Exception {
        //服务.器sn对应装配物料
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = completeMachineDataLogEntityDTO.getWsmAssembleLinesList();
        //任务对应装配物料
        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOS = completeMachineDataLogEntityDTO.getConfigDetailDTOList();
        List<String> itemBarcodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            itemBarcodeList.addAll(wsmAssembleLinesList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemBarcode())).map(e -> e.getItemBarcode()).distinct().collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(cpmConfigItemAssembleDTOS)) {
            itemBarcodeList.addAll(cpmConfigItemAssembleDTOS.stream().filter(e -> StringUtils.isNotEmpty(e.getItemBarcode())).map(e -> e.getItemBarcode()).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(itemBarcodeList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_ASSEMBLE_MATERIAL_FOR_TASK);
            return;
        }
        List<QueryCustomerBasicInformationDTO> queryCustomerBasicInformationDTOList = new ArrayList<>();
        this.generateCustomerBasicInformationDTOList(cpmContractEntitiesDTO, wsmAssembleLinesList, queryCustomerBasicInformationDTOList);
        this.generateForTaskItem(cpmContractEntitiesDTO, cpmConfigItemAssembleDTOS, queryCustomerBasicInformationDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = completeMachineDataLogEntityDTO.getCustomerItemsDTOList();
        List<ZmsStationSSPNICListDTO> itemCodeSNMap = this.getItemCodeSNMap(zmsStationSSPDTO);
        //设置配置物料
        List<CompleteMachineDataLogLineEntityDTO> completeMachineDataLogLineEntityDTOList = new ArrayList<>();
        for (QueryCustomerBasicInformationDTO queryCustomerBasicInformationDTO : queryCustomerBasicInformationDTOList) {
            CompleteMachineDataLogLineEntityDTO completeMachineComponentsDTO = new CompleteMachineDataLogLineEntityDTO();
            completeMachineComponentsDTO.setServerSn(completeMachineDataLogEntityDTO.getServerSn());
            completeMachineComponentsDTO.setCreateBy(completeMachineDataLogEntityDTO.getCreateBy());
            completeMachineComponentsDTO.setSpecifications("");
            completeMachineComponentsDTO.setVersion("");
            completeMachineComponentsDTO.setDateCode(NOT_INVOLVED_NOT_GET);
            completeMachineComponentsDTO.setOriginComponentSn("");
            completeMachineComponentsDTO.setMacVersion("");
            completeMachineComponentsDTO.setSlot(SOLT_IS_EMPTY_ELSE_IT);
            completeMachineComponentsDTO.setBiosVersion("");
            completeMachineComponentsDTO.setBmcVersion("");
            completeMachineComponentsDTO.setCpldVersion("");

            setCompleteMachineComponentsDTO(completeMachineComponentsDTO, itemCodeSNMap, queryCustomerBasicInformationDTO.getItemCode(), zmsStationSSPDTO);

            completeMachineComponentsDTO.setManufacturer("");
            completeMachineComponentsDTO.setItemCode(queryCustomerBasicInformationDTO.getItemCode());
            //服务器sn装配物料对应imes的客户物料信息 通过物料代码、供应商、规格，从imes基础信息表找到一条数据
            CustomerItemsDTO customerItemsDTO = customerItemsDTOList.stream().filter(f -> f.getZteCode().equals(queryCustomerBasicInformationDTO.getItemCode())).findFirst().orElse(null);
            if (customerItemsDTO == null) {
                continue;
            }
            //客户部件类型 材料类型
            completeMachineComponentsDTO.setComponentType(customerItemsDTO.getCustomerComponentType());
            completeMachineComponentsDTO.setModelNumber(customerItemsDTO.getCustomerMaterialType());
            completeMachineComponentsDTO.setSpecifications(customerItemsDTO.getCustomerSpecification());
            completeMachineComponentsDTO.setManufacturer(customerItemsDTO.getCustomerSupplier());
            completeMachineComponentsDTO.setMpn(customerItemsDTO.getPnCode());
            if (!modelNumberList.contains(completeMachineComponentsDTO.getComponentType())) {
                continue;
            }
            completeMachineDataLogLineEntityDTOList.add(completeMachineComponentsDTO);
        }
        //根据料单代码分类
        Map<String, List<CompleteMachineDataLogLineEntityDTO>> comMap = completeMachineDataLogLineEntityDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getComponentType())).collect(Collectors.groupingBy(
                CompleteMachineDataLogLineEntityDTO::getComponentType));
        //设置部件序号
        for (Map.Entry<String, List<CompleteMachineDataLogLineEntityDTO>> k : comMap.entrySet()) {
            for (int i = NumConstant.NUM_ZERO; i < k.getValue().size(); i++) {
                k.getValue().get(i).setComponentId(k.getKey() + (i + NumConstant.NUM_ONE));
            }
        }
        completeMachineDataLogEntityDTO.setComponents(completeMachineDataLogLineEntityDTOList);
    }

    /**
     * 任务物料组装数据
     *
     * @param cpmContractEntitiesDTO
     * @param cpmConfigItemAssembleDTOS
     * @param queryCustomerBasicInformationDTOList
     * @param barcodeExpandDTOMap
     */
    private void generateForTaskItem(CpmContractEntitiesDTO cpmContractEntitiesDTO, List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOS, List<QueryCustomerBasicInformationDTO> queryCustomerBasicInformationDTOList) {
        if (!CollectionUtils.isEmpty(cpmConfigItemAssembleDTOS)) {
            for (CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO : cpmConfigItemAssembleDTOS) {
                QueryCustomerBasicInformationDTO queryCustomerBasicInformationDTO = new QueryCustomerBasicInformationDTO();
                queryCustomerBasicInformationDTO.setItemBarcode(cpmConfigItemAssembleDTO.getItemBarcode());
                queryCustomerBasicInformationDTO.setItemCode(cpmConfigItemAssembleDTO.getItemCode());
                queryCustomerBasicInformationDTO.setUserAddress(cpmContractEntitiesDTO.getUserAddress());

                queryCustomerBasicInformationDTOList.add(queryCustomerBasicInformationDTO);
            }
        }
    }

    /**
     * 组装过滤物料基础信息数据
     *
     * @param cpmContractEntitiesDTO
     * @param wsmAssembleLinesList
     * @param queryCustomerBasicInformationDTOList
     * @param barcodeExpandDTOMap
     */
    public void generateCustomerBasicInformationDTOList(CpmContractEntitiesDTO cpmContractEntitiesDTO, List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList,
                                                        List<QueryCustomerBasicInformationDTO> queryCustomerBasicInformationDTOList) {
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            for (WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO : wsmAssembleLinesList) {
                QueryCustomerBasicInformationDTO queryCustomerBasicInformationDTO = new QueryCustomerBasicInformationDTO();
                queryCustomerBasicInformationDTO.setItemBarcode(wsmAssembleLinesEntityDTO.getItemBarcode());
                queryCustomerBasicInformationDTO.setItemCode(wsmAssembleLinesEntityDTO.getItemCode());
                queryCustomerBasicInformationDTO.setUserAddress(cpmContractEntitiesDTO.getUserAddress());

                queryCustomerBasicInformationDTOList.add(queryCustomerBasicInformationDTO);
            }
        }
    }

    /**
     * @Description 获取表格html
     * @Date 2019/5/14 11:01
     * @Param
     **/
    private String getEmailContent(List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList) throws Exception {
        StringBuffer html = new StringBuffer();
        //有产出数据
        String div = "<br/><br/><div>";
        String htmlTitle = "<p style='margin:0;font-size:13pt'>" + Constant.MES_MACHINE_INFORMATION_FEEDBACK + "</p>";
        html.append(div + htmlTitle + "<table style='border:1px rgb(204,204,204);width:100%;border-collapse:collapse;background-color:white;font-size:9pt;'cellspacing=0 cellpadding=3>");
        html.append("<tr style ='color:white;font-weight:bold;background-color:rgb(0,102,153);font-weight:bold'>");
        html.append("<td>" + Constant.INDEX_STR + "</td>");
        html.append("<td>" + Constant.TASK_NO + "</td>");
        html.append("<td>" + Constant.ERROR_INFO + "</td>");
        html.append("</tr>");
        for (int index = NumConstant.NUM_ZERO; index < completeMachineDataLogEntityDTOList.size(); index++) {
            int num = index + 1;
            html.append("<tr>");
            html.append("<td>" + num + "</td>");
            html.append("<td>" + CommonUtils.getStrTransNull(completeMachineDataLogEntityDTOList.get(index).getFactoryOrderId()) + "</td>");
            List<String> errorList = completeMachineDataLogEntityDTOList.get(index).getErrorList();
            html.append("<td>" + CommonUtils.getStrTransNull(StringUtils.join(errorList, Constant.COMMA)) + "</td>");
            html.append("</tr>");
        }
        html.append("</table>");
        html.append("<hr style='height:5px;border:none;background-color:rgb(0,102,153);'/></div>");
        return html.toString();
    }

    /**
     * 设置装配包装时间
     *
     * @param completeMachineDataLogEntityDTO
     * @param completeMachineDataLogEntityDTOList
     * @param taskHistorySubStateDTOList
     */
    public void setAssemblyPackagingTime(CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList,
                                         List<TaskHistorySubStateDTO> taskHistorySubStateDTOList) {
        if (CollectionUtils.isEmpty(completeMachineDataLogEntityDTOList)) {
            return;
        }
        List<TaskHistorySubStateDTO> assemblingList = taskHistorySubStateDTOList.stream().filter(e -> StringUtils.equals(e.getSubStatus(), Constant.ASSEMBLING)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(assemblingList)) {
            Date date = assemblingList.get(NumConstant.NUM_ZERO).getCreationDate();
            completeMachineDataLogEntityDTO.setFactoryOrderTime(date);
        }
        List<TaskHistorySubStateDTO> packingList = taskHistorySubStateDTOList.stream().filter(e -> StringUtils.equals(e.getSubStatus(), Constant.PACKING)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packingList)) {
            Date date = packingList.get(NumConstant.NUM_ZERO).getCreationDate();
            completeMachineDataLogEntityDTO.setPackageTime(date);
        }
    }

    /**
     * 设置任务对应服务器sn 主板sn
     *
     * @param cpmContractEntitiesDTO
     * @throws Exception
     */
    private CompleteMachineDataLogEntityDTO setServerMotherboardSn(CpmContractEntitiesDTO cpmContractEntitiesDTO) throws Exception {
        CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = new CompleteMachineDataLogEntityDTO();
        //入库记账完成时间
        setAcceptanceTime(cpmContractEntitiesDTO, completeMachineDataLogEntityDTO);
        //设置orderId
        this.setOrderId(completeMachineDataLogEntityDTO, cpmContractEntitiesDTO.getEntityId());
        //任务装配物料
        List<CpmConfigItemAssembleDTO> configDetailDTOList = wsmAssembleLinesService.getAssemblyMaterialsByEntityId(cpmContractEntitiesDTO.getEntityId());
        if (CollectionUtils.isEmpty(configDetailDTOList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_FOR_TASK);
            return completeMachineDataLogEntityDTO;
        }
        completeMachineDataLogEntityDTO.setConfigDetailDTOList(configDetailDTOList);

        List<String> itemCodeList = configDetailDTOList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemCode())).map(e -> e.getItemCode()).distinct().collect(Collectors.toList());
        //调imes获取物料代码客户基础信息
        List<CustomerItemsDTO> customerItemsDTOList = centerfactoryRemoteService.getCustomerItemsInfo(Constant.BYTE_DANCE_UNIT_NAME, itemCodeList);
        //没物料基础信息的需要发送邮件
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_CUSTOMER_INFO);
            return completeMachineDataLogEntityDTO;
        }
        completeMachineDataLogEntityDTO.getCustomerItemsDTOList().addAll(customerItemsDTOList);
        //过滤出服务器sn
        List<CustomerItemsDTO> completeMachineList = customerItemsDTOList.stream().filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.NUM_THREE + "")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(completeMachineList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_THE_SERVER_CUSTOMER_INFO);
            return completeMachineDataLogEntityDTO;
        }
        //多个服务器sn也报错
        if (completeMachineList.size() > NumConstant.NUM_ONE) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_MORE_THAN_ONE_THE_SERVER_CUSTOMER_INFO);
            return completeMachineDataLogEntityDTO;
        }

        //服务器sn对应客户物料信息
        CustomerItemsDTO serviceSnCustomerItemsDTO = completeMachineList.get(NumConstant.NUM_ZERO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = configDetailDTOList.stream().filter(e -> StringUtils.equals(e.getItemCode(), serviceSnCustomerItemsDTO.getZteCode())).findFirst().orElse(null);
        if (cpmConfigItemAssembleDTO == null) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_THE_SERVER_CUSTOMER_INFO);
            return completeMachineDataLogEntityDTO;
        }
        completeMachineDataLogEntityDTO.setCustomerItemsDTO(serviceSnCustomerItemsDTO);
        //服务器sn
        String serviceSn = cpmConfigItemAssembleDTO.getItemBarcode();
        setServiceSn(completeMachineDataLogEntityDTO, serviceSnCustomerItemsDTO, serviceSn);

        //主板sn
        List<CustomerItemsDTO> motherboardList = customerItemsDTOList.stream().filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.NUM_FIVE + "")).collect(Collectors.toList());
        //多个主板sn也报错
        if (!CollectionUtils.isEmpty(motherboardList) && motherboardList.size() > NumConstant.NUM_ONE) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_MORE_THAN_ONE_ZB_CUSTOMER_INFO);
            return completeMachineDataLogEntityDTO;
        }
        //判断任务装配物料中是否存在主板条码
        setMainBoardSn(completeMachineDataLogEntityDTO, configDetailDTOList, motherboardList);
        //服务器sn对应装配物料
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = wsmAssembleLinesService.getAssemblyMaterials(serviceSn);
        if (CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_GET_MATERIAL_FOR_SERVICE_SN);
            return completeMachineDataLogEntityDTO;
        }

        completeMachineDataLogEntityDTO.setWsmAssembleLinesList(wsmAssembleLinesList);
        List<String> zbItemCodeList = wsmAssembleLinesList.stream().filter(e -> StringUtils.isNotEmpty(e.getItemCode())).map(e -> e.getItemCode()).distinct().collect(Collectors.toList());
        //调imes获取物料代码客户基础信息
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = centerfactoryRemoteService.getCustomerItemsInfo(Constant.BYTE_DANCE_UNIT_NAME, zbItemCodeList);
        completeMachineDataLogEntityDTO.getCustomerItemsDTOList().addAll(serviceSnCustomerItemsDTOList);
        return getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, serviceSnCustomerItemsDTOList, cpmConfigItemAssembleDTO, wsmAssembleLinesList, serviceSnCustomerItemsDTOList);
    }

    /**
     * 判断任务装配物料中是否存在主板条码
     *
     * @param completeMachineDataLogEntityDTO
     * @param configDetailDTOList
     * @param motherboardList
     */
    private void setMainBoardSn(CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, List<CpmConfigItemAssembleDTO> configDetailDTOList, List<CustomerItemsDTO> motherboardList) {
        if (!CollectionUtils.isEmpty(motherboardList)) {
            //主板sn对应客户物料信息
            CustomerItemsDTO zbSnCustomerItemsDTO = motherboardList.get(NumConstant.NUM_ZERO);
            CpmConfigItemAssembleDTO zbCpmConfigItemAssembleDTO = configDetailDTOList.stream().filter(e -> StringUtils.equals(e.getItemCode(), zbSnCustomerItemsDTO.getZteCode())).findFirst().orElse(null);
            if (zbCpmConfigItemAssembleDTO != null) {
                completeMachineDataLogEntityDTO.setMainBoardSn(zbCpmConfigItemAssembleDTO.getItemBarcode());
            }
        }
    }

    /**
     * 设置服务器sn 客户名称 机型
     *
     * @param completeMachineDataLogEntityDTO
     * @param serviceSnCustomerItemsDTO
     * @param serviceSn
     */
    private void setServiceSn(CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, CustomerItemsDTO serviceSnCustomerItemsDTO, String serviceSn) {
        completeMachineDataLogEntityDTO.setServerSn(serviceSn);
        //客户定义的机型名称
        if (StringUtils.isEmpty(serviceSnCustomerItemsDTO.getCustomerModel())) {
            completeMachineDataLogEntityDTO.getErrorList().add(Constant.FAILED_TO_OBTAIN_SERVER_SN_MODEL);
        }
        completeMachineDataLogEntityDTO.setCustomerName(serviceSnCustomerItemsDTO.getCustomerName());
        completeMachineDataLogEntityDTO.setServerType(serviceSnCustomerItemsDTO.getCustomerModel());
    }

    /**
     * 获取服务器sn对应条码
     *
     * @param completeMachineDataLogEntityDTO
     * @param customerItemsDTOList
     * @param cpmConfigItemAssembleDTO
     * @param wsmAssembleLinesList
     * @param serviceSnCustomerItemsDTOList
     * @return
     */
    public CompleteMachineDataLogEntityDTO getCompleteMachineDataLogEntityDTO(CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, List<CustomerItemsDTO> customerItemsDTOList, CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO,
                                                                              List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList, List<CustomerItemsDTO> serviceSnCustomerItemsDTOList) {
        if (CollectionUtils.isEmpty(serviceSnCustomerItemsDTOList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(String.format(Constant.FAILED_GET_MATERIAL_FOR_SERVICE_SN_CUSTOMER_INFO));
            return completeMachineDataLogEntityDTO;
        }
        //没获取到主板sn才再次根据服务器sn获取
        if (StringUtils.isNotEmpty(completeMachineDataLogEntityDTO.getMainBoardSn())) {
            return completeMachineDataLogEntityDTO;
        }

        //主板sn
        List<CustomerItemsDTO> zbSnCustomerItemsDTOList = customerItemsDTOList.stream().filter(e -> StringUtils.equals(e.getProjectType(), NumConstant.NUM_FIVE + "")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zbSnCustomerItemsDTOList)) {
            completeMachineDataLogEntityDTO.getErrorList().add(String.format(Constant.FAILED_GET_MATERIAL_ZB_CUSTOMER_INFO));
            return completeMachineDataLogEntityDTO;
        }
        //多个主板sn也报错
        if (zbSnCustomerItemsDTOList.size() > NumConstant.NUM_ONE) {
            completeMachineDataLogEntityDTO.getErrorList().add(String.format(Constant.FAILED_GET_MATERIAL_MORE_THAN_ONE_ZB_CUSTOMER_INFO));
            return completeMachineDataLogEntityDTO;
        }
        //主板sn对应客户物料信息
        CustomerItemsDTO zbSnCustomerItemsDTO = zbSnCustomerItemsDTOList.get(NumConstant.NUM_ZERO);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = wsmAssembleLinesList.stream().filter(e -> StringUtils.equals(e.getItemCode(), zbSnCustomerItemsDTO.getZteCode())).findFirst().orElse(null);
        if (wsmAssembleLinesEntityDTO == null) {
            completeMachineDataLogEntityDTO.getErrorList().add(String.format(Constant.FAILED_GET_MATERIAL_ZB_CUSTOMER_INFO));
            return completeMachineDataLogEntityDTO;
        }
        //主板SN
        completeMachineDataLogEntityDTO.setMainBoardSn(wsmAssembleLinesEntityDTO.getItemBarcode());
        return completeMachineDataLogEntityDTO;
    }

    /**
     * 入库记账完成时间
     *
     * @param cpmContractEntitiesDTO
     * @param completeMachineDataLogEntityDTO
     */
    public void setAcceptanceTime(CpmContractEntitiesDTO cpmContractEntitiesDTO, CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO) {
        List<CpmConfigItemAssembleDTO> warehousingList = materialConfigBindService.getTheCompletionTimeOfInboundAccounting(cpmContractEntitiesDTO.getEntityId() + "", cpmContractEntitiesDTO.getOrganizationId());
        if (!CollectionUtils.isEmpty(warehousingList)) {
            Date acceptanceTime = warehousingList.get(NumConstant.NUM_ZERO).getAcceptanceTime();
            completeMachineDataLogEntityDTO.setAcceptanceTime(acceptanceTime);
        }
    }

    /**
     * 设置orderId
     *
     * @param entityId
     */
    public void setOrderId(CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO, Integer entityId) {
        String orderId = wsmAssembleLinesService.getCustomerPONumber(entityId);
        if (StringUtils.isEmpty(orderId)) {
            orderId = Constant.FAILED_TO_NO_PO_PRODUCT;
        }
        completeMachineDataLogEntityDTO.setOrderId(orderId);
        if (StringUtils.isEmpty(orderId)) {
            completeMachineDataLogEntityDTO.getErrorList().add(String.format(Constant.FAILED_TO_OBTAIN_TASK_PO_NUMBER));
        }

    }

    /**
     * 上传资源信息
     *
     * @param list
     * @throws Exception
     */
    @Override
    public void uploadResourceInfoToMes(List<SpSpecialityNalDTO> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (List<SpSpecialityNalDTO> spSpecialityNalDTOS : Lists.partition(list, Constant.INT_100)) {
            zmsCompleteMachineHeadRepository.batchInsertResourceInfo(spSpecialityNalDTOS);
        }
    }

    @Override
    public List<SpSpecialityNalDTO> getMacByResourceNumber(List<String> list) {
        List<SpSpecialityNalDTO> spSpecialityNalDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return spSpecialityNalDTOS;
        }
        for (List<String> tempResourceNumberList : Lists.partition(list, Constant.INT_1000)) {
            List<SpSpecialityNalDTO> tempList = zmsCompleteMachineHeadRepository.getMacByResourceNumber(tempResourceNumberList);
            if (!CollectionUtils.isEmpty(tempList)) {
                spSpecialityNalDTOS.addAll(tempList);
            }
        }
        return spSpecialityNalDTOS;
    }

    @Override
    public List<BarcodeNetSignDTO> selectBarAccSignForSchTask(BarcodeNetSignDTO barcodeNetSignDTO) {
        if (barcodeNetSignDTO == null || barcodeNetSignDTO.getRow() == null) {
            return new ArrayList<>();
        }
        if (barcodeNetSignDTO.getRow() > Constant.INT_1000) {
            return new ArrayList<>();
        }
        return zmsCompleteMachineHeadRepository.selectBarAccSignForSchTask(barcodeNetSignDTO);
    }
}