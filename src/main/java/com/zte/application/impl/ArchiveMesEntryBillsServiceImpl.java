package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveMesEntryBillsDataService;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.common.enums.YesNoEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchiveMesEntryBillsAssembler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/9
 * @description : 装箱单入库申请归档
 */
@Slf4j
@Service("archiveMesEntryBillsService")
public class  ArchiveMesEntryBillsServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchiveMesEntryBillsDataService archiveMesEntryBillsDataService;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        // 事务管理器。使支持多数据源事务
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        try {
            if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
                return null;
            }
            ArchiveMesEntryBillsDTO dto = archiveMesEntryBillsDataService.getByEntryNumber(taskSendArchive.getBillNo());
            if(null == dto){
                return null;
            }
            return initArchiveItem(taskSendArchive,dto);
        }catch (Exception e){
            log.error("ArchiveMesEntryBillsServiceImpl archive error",e);
            throw e;
        }finally {
            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        }
    }

    /**
     * 归档方法
     * @param taskSendArchive
     * @param dto
     * @return
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchiveMesEntryBillsDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();

        String businessId = ArchiveBusinessTypeEnum.MES_ENTRY_BILLS.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.MES_ENTRY_BILLS.getName());

        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNameId(dto.getCreatedBy());
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        ArchiveMesEntryBillsAssembler.initItemContent(item, dto, pubHrvOrg);
        //装邢丹入库申请归档附件归档
        archiveBoqBoxMake(item,dto,taskSendArchive);
        return item;
    }

    /**
     * 附件归档
     * @param item 归档项
     * @param dto 装箱单入库申请
     * @param taskSendArchive 归档任务
     */
    private void archiveBoqBoxMake(ArchiveReq.ArchiveItem item, ArchiveMesEntryBillsDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        //查询单元箱列表
        String fileTitle = ArchiveConstant.FILE_TITLE_MES_ENTRY_BILLS+dto.getEntryNumber();
        List<ArchiveMesEntryBillsDetailDTO> dtos = archiveMesEntryBillsDataService.getDetailListByEntryBillId(dto.getEntryBillId());
        dtos.forEach(o->{
            o.setBoxType(StringUtils.isBlank(o.getBoxType()) ? YesNoEnum.NO.getName() : o.getBoxType());
        });
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        AttachmentUploadVo uploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.MES_ENTRY_BILLS_DETAIL_MAP,dtos);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        item.setItemDocs(Collections.singletonList(ArchiveBusiness.buildArchiveItemDoc(uploadVo)));
    }

    @Transactional(propagation= Propagation.NOT_SUPPORTED)
    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        Page<ArchiveTaskSend> page = new Page<>();
        page.setPageSize(archiveQueryParamDTO.getRows());
        page.setCurrent(archiveQueryParamDTO.getPage());

        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        Page<ArchiveMesEntryBillsDTO> pageInfo = archiveMesEntryBillsDataService.getEntryBillsByDateRange(archiveQueryParamDTO);
        page.setTotal(pageInfo.getTotal());

        List<ArchiveMesEntryBillsDTO> cces = pageInfo.getRows();
        List<ArchiveTaskSend> list = cces.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(ArchiveBusinessTypeEnum.MES_ENTRY_BILLS.getCode());
            archiveItem.setBillId(String.valueOf(dataItem.getEntryBillId()));
            archiveItem.setBillNo(dataItem.getEntryNumber());
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setBillReserves(ArchiveConstant.BLANK);
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        page.setRows(list);
        return page;
    }

}
