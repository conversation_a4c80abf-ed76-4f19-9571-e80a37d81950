package com.zte.application.impl;

import com.zte.application.CompleteMachineDataLogLineService;
import com.zte.domain.model.ZmsCompleteMachineLineRepository;
import com.zte.interfaces.dto.CompleteMachineDataLogLineEntityDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Service("completeMachineDataLogLineService")
public class CompleteMachineDataLogLineServiceImpl implements CompleteMachineDataLogLineService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ZmsCompleteMachineLineRepository completeMachineDataLogLinerepository;


}