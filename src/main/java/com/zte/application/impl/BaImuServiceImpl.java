package com.zte.application.impl;

import com.zte.application.BaImuService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BaImuRepository;
import com.zte.interfaces.dto.BaImuDTO;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service("baImuService")
@DataSource(DatabaseType.DB2)
public class BaImuServiceImpl implements BaImuService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BaImuRepository baImurepository;


    /**
     * 更新或新增
     *
     * @param baImuDTOList
     * @throws Exception
     */
    @Override
    public void save(List<BaImuDTO> baImuDTOList) throws Exception {
        if (CollectionUtils.isEmpty(baImuDTOList)) {
            return;
        }
        //先判断imu是否存在，存在即更新，不然就新增
        List<Integer> imuList = baImuDTOList.stream().map(e -> e.getImuId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(imuList)) {
            return;
        }
        List<BaImuDTO> existBaImuDTOList = baImurepository.getListByImuIdList(imuList);
        List<Integer> existImuList = CollectionUtils.isEmpty(existBaImuDTOList) ? new ArrayList<>() : existBaImuDTOList.stream().map(e -> e.getImuId()).distinct().collect(Collectors.toList());
        //需更新的
        List<BaImuDTO> updateBaImuDTOList = baImuDTOList.stream().filter(e -> existImuList.contains(e.getImuId().intValue())).collect(Collectors.toList());
        //需更新的
        List<BaImuDTO> insertBaImuDTOList = baImuDTOList.stream().filter(e -> !existImuList.contains(e.getImuId().intValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateBaImuDTOList)) {
            for (List<BaImuDTO> tempBaImuDTOList : CommonUtils.splitList(updateBaImuDTOList, Constant.INT_100)) {
                baImurepository.batchUpdate(tempBaImuDTOList);
            }
        }
        if (CollectionUtils.isNotEmpty(insertBaImuDTOList)) {
            for (List<BaImuDTO> tempBaImuDTOList : CommonUtils.splitList(insertBaImuDTOList, Constant.INT_100)) {
                baImurepository.batchInsert(tempBaImuDTOList);
            }
        }
    }
}