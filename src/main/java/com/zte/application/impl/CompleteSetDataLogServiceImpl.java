package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.CompleteSetDataLogService;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsOverallUnitServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtils;
import com.zte.domain.model.datawb.TaskToSetRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@Service("CompleteSetDataLogService")
@DataSource(DatabaseType.SFC)
public class CompleteSetDataLogServiceImpl implements CompleteSetDataLogService {

    @Autowired
    private TaskToSetRepository taskToSetRepository;

    @Autowired
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Autowired
    private ZmsOverallUnitServiceImpl zmsOverallUnitServiceImpl;
    @Autowired
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Autowired
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Autowired
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Autowired
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadServiceImpl;
    public CompleteSetDataLogServiceImpl() {
    }
    @Override
    public void uploadCompleteSetData(CompleteSetDataDTO dto) throws Exception {
        List<TaskHeadInfo> taskHeadInfoList = taskToSetRepository.selectTaskToSetHeadInfo(dto);
        if (taskHeadInfoList == null) {
            return;
        }
        List<TaskDetailInfo> taskLineInfoList = taskToSetRepository.selectTaskToSetInfo(dto);
        if (taskLineInfoList == null) {
            return;
        }
        for (TaskHeadInfo taskHeadInfo : taskHeadInfoList) {
            if (taskHeadInfo.getAssetNo() == null) {
                taskHeadInfo.setAssetNo(STRING_EMPTY);
            }
            if (taskHeadInfo.getCombo() == null) {
                taskHeadInfo.setCombo(STRING_EMPTY);
            }

            /* Started by AICoder, pid:ff38fb17b0ab47229693198bdc98c73d */
            List<TaskDetailInfo> taskSetInfoList = new ArrayList<>();
            for (TaskDetailInfo taskDetailInfo : taskLineInfoList) {
                if (StringUtils.equals(taskDetailInfo.getServerSn(), taskHeadInfo.getServerSn())) {
                    TaskDetailInfo temp = JSONObject.parseObject(JSONObject.toJSONString(taskDetailInfo), new TypeReference<TaskDetailInfo>() {});
                    taskSetInfoList.add(temp);
                }
            }
            /* Ended by AICoder, pid:ff38fb17b0ab47229693198bdc98c73d */

            if (taskSetInfoList.isEmpty()) {
                continue;
            }
            List<SspTaskDetailInfoDTO> sspTaskDetailList = querySspTaskInfoByte(taskHeadInfo.getServerSn());
            List<TaskDetailInfo> taskSetInfoS= getTaskDetailList(taskSetInfoList,sspTaskDetailList);
            taskHeadInfo.setComponents(taskSetInfoS);
            taskHeadInfo.setFactoryOrderId(null);
            pushDataToB2B(taskHeadInfo, dto.getEmpNo(), taskHeadInfo.getServerSn());
        }
    }

    private  List<TaskDetailInfo>  getTaskDetailList(List<TaskDetailInfo> taskSetInfoList,List<SspTaskDetailInfoDTO> sspTaskDetailList)
    {
        for (TaskDetailInfo taskInfo : taskSetInfoList) {
            if (taskInfo.getSlot() == null) {
                taskInfo.setSlot(SLOT_NAME);
            }
            List<SspTaskDetailInfoDTO> sspTaskDetails = sspTaskDetailList.stream()
                    .filter(obj -> obj.getItemCode().equals(taskInfo.getItemNo())
                            && obj.getSn().equals(taskInfo.getOriginComponentSn()))
                    .collect(Collectors.toList());
            if (!sspTaskDetails.isEmpty()) {
                ExtraParamsDTO extraParams = sspTaskDetails.get(0).getExtraParams();
                taskInfo.setExtraParams(extraParams);
                if(sspTaskDetails.get(0).getBrandName()!=null) {
                    taskInfo.setManufacturer(sspTaskDetails.get(0).getBrandName());
                }
                if(sspTaskDetails.get(0).getManufacturerProductName()!=null) {
                    taskInfo.setManufacturerProductName(sspTaskDetails.get(0).getManufacturerProductName());
                }
            } else {
                taskInfo.setExtraParams(new ExtraParamsDTO());
            }

            taskInfo.setServerSn(null);
            taskInfo.setFactoryOrderId(null);
            taskInfo.setItemNo(null);
        }
        return taskSetInfoList;
    }

    public List<SspTaskDetailInfoDTO> querySspTaskInfoByte(String sn) throws Exception {
        String strToken = zmsStationLogUploadService.getToken();
        String zSUrl = zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200026);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(SN, sn);
        String bo = zmsStationLogUploadService.getZsStationLog(paramMap, zSUrl, strToken);
        List<SspTaskDetailInfoDTO> totalList = new ArrayList<>();
        if(bo==null) {
            return  totalList;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        SspTaskInfoDTO sspTaskInfoDTO = objectMapper.readValue(bo, SspTaskInfoDTO.class);
        totalList.addAll(sspTaskInfoDTO.nicList);
        totalList.addAll(sspTaskInfoDTO.memoryList);
        totalList.addAll(sspTaskInfoDTO.diskList);
        totalList.addAll(sspTaskInfoDTO.psuList);
        totalList.addAll(sspTaskInfoDTO.cpuList);
        return totalList;
    }

    /* Started by AICoder, pid:ddd6d61f7ce54a47a83e23e566ebb626 */
    public void pushDataToB2B(TaskHeadInfo tempInsert, String empNo, String sn) throws Exception {
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(java.util.UUID.randomUUID().toString());
        customerDataLogDTO.setOrigin(Constant.MES);
        customerDataLogDTO.setCustomerName(BYTE_DANCE);
        customerDataLogDTO.setProjectName(PROJECTSET_NAME);
        customerDataLogDTO.setMessageType(MESSAGE_TYPE_B2BSET);
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setCreateBy(empNo);
        customerDataLogDTO.setLastUpdatedBy(empNo);
        customerDataLogDTO.setProjectPhase(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_FULL));
        customerDataLogDTO.setSn(sn);
        customerDataLogDTO.setJsonData(JSON.toJSONString(tempInsert, SerializerFeature.DisableCircularReferenceDetect));
        dataList.add(customerDataLogDTO);
        // 组装日志对象
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadDTO = new ZmsMesInfoUploadLogDTO();
        BeanUtils.copyProperties(customerDataLogDTO, zmsMesInfoUploadDTO);
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadDTO);
        // 写入上传日志
        zmsIndicatorUploadService.insertMesInfoUploadLog(zmsMesInfoUploadDTOList);
        // 推送B2B
        centerfactoryRemoteService.pushDataToB2B(dataList);
    }
    /* Ended by AICoder, pid:ddd6d61f7ce54a47a83e23e566ebb626 */
}
