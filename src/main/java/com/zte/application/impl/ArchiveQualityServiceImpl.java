package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveQualityClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  质检查询归档
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveQualityServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveQualityServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveQualityClient archiveQualityClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("质检查询归档start......");
        String fileName = ArchiveBusinessTypeEnum.QUALITY_QUERY.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        //获取检验单信息
        List<ArchiveQualityCheckDTO> archiveQualityCheckDTOS = selectQualityBySendChkNo(taskSendArchive);
        //获取送检头
        ArchiveQualityCheckDTO archiveQualityCheckDTO = selectQualitySendCheckHeaderBySendChkNo(taskSendArchive);
        //获取送检行
        List<ArchiveQualitySendCheckLineDTO> archiveQualitySendCheckLineDTOS= selectQualitySendCheckLineBySendChkNo(taskSendArchive);
        //获取质检头
        List<ArchiveQualityCheckDTO> archiveQualityCheckHeaderDTOS =  selectQualityCheckHeaderBySendChkNo(taskSendArchive);
        //获取质检行行
        List<ArchiveQualityCheckLineDTO> archiveQualityCheckLineDTOS = selectQualityCheckLineBySendChkNo(taskSendArchive);
        //质检查询检验单列表excel
        AttachmentUploadVo qualityExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.QUALITY_SHEET_HEAD_MAP, archiveQualityCheckDTOS);
        //半成品检验单据明细
        LinkedHashMap<String,List> dataListMap = new LinkedHashMap<>();
        dataListMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[0], Collections.singletonList(archiveQualityCheckDTO));
        dataListMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[1],archiveQualitySendCheckLineDTOS);
        dataListMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[2],archiveQualityCheckHeaderDTOS);
        dataListMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[3],archiveQualityCheckLineDTOS);

        LinkedHashMap<String,LinkedHashMap<String,String>> headerMap = new LinkedHashMap<>();
        headerMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[0],ArchiveConstant.QUALITY_SEND_HEADER_SHEET_HEAD_MAP);
        headerMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[1],ArchiveConstant.QUALITY_SEND_LINE_SHEET_HEAD_MAP);
        headerMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[2],ArchiveConstant.QUALITY_CHECK_HEADER_SHEET_HEAD_MAP);
        headerMap.put(ArchiveConstant.QUALITY_SHEET_NAMES[3],ArchiveConstant.QUALITY_CHECK_LINE_SHEET_HEAD_MAP);

        AttachmentUploadVo qualityDetailsExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", headerMap, dataListMap);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(qualityExcelUploadVo);
        attachmentDataList.add(qualityDetailsExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.QUALITY_QUERY.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.QUALITY_QUERY.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveQualityCheckDTO);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("质检查询归档end......");
        return item;
    }

    private List<ArchiveQualityCheckDTO> selectQualityBySendChkNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData = archiveQualityClient.selectQualityBySendChkNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取送检头返回报错：{}",msg);
            return new ArrayList<ArchiveQualityCheckDTO>();
        }

        List<ArchiveQualityCheckDTO> archiveQualityCheckDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveQualityCheckDTO.class);
        if(CollectionUtils.isEmpty(archiveQualityCheckDTOS)){
            return new ArrayList<>();
        }
        return archiveQualityCheckDTOS;
    }

    private ArchiveQualityCheckDTO selectQualitySendCheckHeaderBySendChkNo(ArchiveTaskSend taskSendArchive){
        ServiceData<ArchiveQualityCheckDTO> serviceData = archiveQualityClient.selectQualitySendCheckHeaderBySendChkNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取送检头返回报错：{}",msg);
            throw new RouteException("调用服务获取送检头返回报错");
        }
        ArchiveQualityCheckDTO archiveQualityCheckDTO = serviceData.getBo();
        return archiveQualityCheckDTO;
    }

    private List<ArchiveQualitySendCheckLineDTO> selectQualitySendCheckLineBySendChkNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveQualitySendCheckLineDTO>> serviceData = archiveQualityClient.selectQualitySendCheckLineBySendChkNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取送检行返回报错：{}",msg);
            return new ArrayList<ArchiveQualitySendCheckLineDTO>();
        }

        List<ArchiveQualitySendCheckLineDTO> archiveQualitySendCheckLineDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveQualitySendCheckLineDTO.class);
        if(CollectionUtils.isEmpty(archiveQualitySendCheckLineDTOS)){
            return new ArrayList<>();
        }
        return archiveQualitySendCheckLineDTOS;
    }

    private List<ArchiveQualityCheckDTO> selectQualityCheckHeaderBySendChkNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData = archiveQualityClient.selectQualityCheckHeaderBySendChkNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取质检头返回报错：{}",msg);
            return new ArrayList<ArchiveQualityCheckDTO>();
        }
        List<ArchiveQualityCheckDTO> archiveQualityCheckDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveQualityCheckDTO.class);
        if(CollectionUtils.isEmpty(archiveQualityCheckDTOS)){
            return new ArrayList<>();
        }
        return archiveQualityCheckDTOS;
    }

    private List<ArchiveQualityCheckLineDTO> selectQualityCheckLineBySendChkNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveQualityCheckLineDTO>> serviceData = archiveQualityClient.selectQualityCheckLineBySendChkNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取质检行返回报错：{}",msg);
            return new ArrayList<ArchiveQualityCheckLineDTO>();
        }

        List<ArchiveQualityCheckLineDTO> archiveQualityCheckLineDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveQualityCheckLineDTO.class);
        if(CollectionUtils.isEmpty(archiveQualityCheckLineDTOS)){
            return new ArrayList<>();
        }
        return archiveQualityCheckLineDTOS;
    }


    private ArchiveBusinessVo buildBusiness(ArchiveQualityCheckDTO archiveQualityCheckDTO) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        Date checkDate = DateUtil.convertStringToDate(archiveQualityCheckDTO.getCheckDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserId(archiveQualityCheckDTO.getCreatedBy()));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(checkDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(checkDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveQualityCheckDTO.getSendChkNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(archiveQualityCheckDTO.getSender());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.QUALITY_QUERY.getName()+"-"+archiveQualityCheckDTO.getSendChkNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.QUALITY_QUERY.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.QUALITY_QUERY.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(Constant.SZPROD_FULL_NAME);
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("quality archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<String> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.QUALITY_QUERY.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("quality archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }
    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        long start=System.currentTimeMillis();
        ServiceData<Page<String>> listServiceData = archiveQualityClient.selectQualityList(archiveQueryParamDTO);
        long end=System.currentTimeMillis();
        logger.info("time:{}",(end-start)/1000);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档质检单据返回报错：{}", msg);
            throw new RouteException("调用服务获取待归档质检单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<String> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)){
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem);
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
