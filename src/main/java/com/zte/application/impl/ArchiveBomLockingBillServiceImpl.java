package com.zte.application.impl;

import com.zte.application.ArchiveBomLockingBillDataService;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.ArchiveBusinessTypeEnum;
import com.zte.common.enums.ExecuteStatusEnum;
import com.zte.common.utils.ArchiveBusiness;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchiveBomLockingBillAssembler;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/17
 * @description :
 */
@Service("archiveBomLockingBillService")
public class ArchiveBomLockingBillServiceImpl implements ArchiveBusinessService {

    @Autowired
    ArchiveBomLockingBillDataService archiveBomLockingBillDataService;

    @Autowired
    ArchiveFileProperties archiveFileProperties;

    @Autowired
    PubHrvOrgService pubHrvOrgService;

    @Autowired
    ArchiveCommonService archiveCommonService;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        if(null == taskSendArchive || StringUtils.isBlank(taskSendArchive.getBillNo())){
            return null;
        }
        ArchiveBomLockingBillDTO dto = archiveBomLockingBillDataService.getByBillNo(taskSendArchive.getBillNo());
        if(null == dto){
            return null;
        }
        return initArchiveItem(taskSendArchive,dto);
    }

    /**
     * 初始化归档记录
     * @param taskSendArchive 归档任务
     * @param dto 单板锁定
     * @return 归档记录
     */
    private ArchiveReq.ArchiveItem initArchiveItem(ArchiveTaskSend taskSendArchive, ArchiveBomLockingBillDTO dto) throws Exception {
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();

        String businessId = ArchiveBusinessTypeEnum.BOM_LOCKING_BILL.getCode()
                + ArchiveConstant.UNDER_LINE
                + taskSendArchive.getTaskId()
                + ArchiveConstant.UNDER_LINE
                + System.currentTimeMillis();
        item.setBusinessId(businessId);
        item.setBusinessName(ArchiveBusinessTypeEnum.BOM_LOCKING_BILL.getName());

        //组装itemContent
        PubHrvOrg pubHrvOrg = pubHrvOrgService.getPubHrvOrgByUserNameId(dto.getCreatedBy());

        ArchiveBomLockingBillAssembler.initItemContent(item, dto, pubHrvOrg);
        //归档附件
        archiveDoc(item,dto,taskSendArchive);
        return item;
    }

    private void archiveDoc(ArchiveReq.ArchiveItem item, ArchiveBomLockingBillDTO dto, ArchiveTaskSend taskSendArchive) throws Exception {
        //文件题名
        String fileTitle = ArchiveConstant.MODULE_TITLE_BOM_LOCKING_BILL + dto.getBillNo();
        //归档单板锁定批次信息表
        AttachmentUploadVo planUpload = archiveBomlockingPlan(dto,taskSendArchive,fileTitle+ArchiveConstant.BOM_LOCKING_PLAN);
        //归档条码信息
        AttachmentUploadVo barCodeUpload = archiveBomLockingBarCode(dto,taskSendArchive,fileTitle+ArchiveConstant.BOM_LOCKING_BARCODE);
        List<ArchiveReq.ArchiveItemDoc> docs = ArchiveBusiness.buildArchiveItemDocVo(Arrays.asList(planUpload,barCodeUpload));
        item.setItemDocs(docs);
    }

    /**
     * 归档条码附件
     * @param dto 单板锁定
     * @param taskSendArchive 归档任务
     * @param fileTitle 文件名
     * @return 附件信息
     */
    private AttachmentUploadVo archiveBomLockingBarCode(ArchiveBomLockingBillDTO dto, ArchiveTaskSend taskSendArchive, String fileTitle) throws Exception {
        List<ArchiveBomLockingBarcodeDTO> dtos = archiveBomLockingBillDataService.getBarcodeByBillId(dto.getBillId());
        return archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.BOM_LOCKING_BARCODE_MAP,dtos);
    }
    /**
     * 归档批次附件
     * @param dto 单板锁定
     * @param taskSendArchive 归档任务
     * @param fileTitle 文件名
     * @return 附件信息
     */
    private AttachmentUploadVo archiveBomlockingPlan(ArchiveBomLockingBillDTO dto, ArchiveTaskSend taskSendArchive, String fileTitle) throws Exception {
        List<ArchiveBomLockingPlanDTO> dtos = archiveBomLockingBillDataService.getPlanByBillId(dto.getBillId());
        return archiveCommonService.createExcelAndUpload(taskSendArchive,fileTitle,ArchiveConstant.BOM_LOCKING_PLAN_MAP,dtos);
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        Page<ArchiveTaskSend> page = new Page<>();
        page.setPageSize(archiveQueryParamDTO.getRows());
        page.setCurrent(archiveQueryParamDTO.getPage());

        Page<ArchiveBomLockingBillDTO> pageInfo = archiveBomLockingBillDataService.getPageByDateRange(archiveQueryParamDTO);
        page.setTotal(pageInfo.getTotal());

        List<ArchiveBomLockingBillDTO> cces = pageInfo.getRows();
        List<ArchiveTaskSend> list = cces.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(ArchiveBusinessTypeEnum.BOM_LOCKING_BILL.getCode());
            archiveItem.setBillId(dataItem.getBillId());
            archiveItem.setBillNo(dataItem.getBillNo());
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            archiveItem.setBillReserves(ArchiveConstant.BLANK);
            return archiveItem;
        }).collect(Collectors.toList());
        page.setRows(list);
        return page;
    }
}
