package com.zte.application.impl.api;

import com.zte.application.api.ExternalInterfacesService;
import com.zte.application.datawb.WmesWerpConEntityTraceService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.api.ExternalInterfacesRepository;
import com.zte.domain.model.datawb.WmesWerpConEntityTrace;
import com.zte.interfaces.dto.api.ContractAndTaskDataDTO;
import com.zte.interfaces.dto.api.PackingDataDTO;
import com.zte.interfaces.dto.api.PackingDetailsDTO;
import com.zte.interfaces.dto.api.PalletDataDTO;
import com.zte.interfaces.dto.api.PickListQueryDTO;
import com.zte.interfaces.dto.api.PickListResultDTO;
import com.zte.interfaces.dto.api.ProdPickListMainDTO;
import com.zte.interfaces.dto.api.QueryParamBO;
import com.zte.interfaces.dto.api.SiteInfoDTO;
import com.zte.interfaces.dto.wmes.ProcPicklistDetail;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 对外提供服务接口
 *
 * <AUTHOR>
 * @date 2024-03-11 15:41
 */
@Service
@DataSource(value = DatabaseType.WMSPRODLMS)
public class ExternalInterfacesServiceImpl implements ExternalInterfacesService {
    @Autowired
    private ExternalInterfacesRepository externalInterfacesRepository;
    @Autowired
    private WmesWerpConEntityTraceService wmesWerpConEntityTraceService;
    @Autowired
    private MesGetDictInforRepository mesGetDictInforRepository;

    /** Started by AICoder, pid:16c36b758a40472fb273a4556b3db757
     * @param queryParamBO 查询参数
     * @return 托盘信息接口
     */
    @Override
    public PageRows<PalletDataDTO> queryPalletData(QueryParamBO queryParamBO) {

        PageRows<PalletDataDTO> pageRows = new PageRows<>();

        if(queryParamBO.getCurrentPage() <= Constant.INT_0){
            queryParamBO.setCurrentPage(Constant.INT_1);
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        String pageSize = mesGetDictInforRepository.getDicDescription(Constant.LOOK_UP_CODE_302003700003);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        if(queryParamBO.getPageSize() <= Constant.INT_0  ||  queryParamBO.getPageSize() > Integer.valueOf(pageSize)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, String.format(MessageId.PAGESIZE_BETWEEN_0_AND_500,Integer.valueOf(pageSize)));
        }

        if (CollectionUtils.isEmpty(queryParamBO.getPalletNoList()) && CollectionUtils.isEmpty(queryParamBO.getEntityNameList())) {
            return pageRows;
        }

        queryParamBO.setStartRow((queryParamBO.getCurrentPage() -Constant.INT_1) * queryParamBO.getPageSize() + Constant.INT_1);
        queryParamBO.setEndRow(queryParamBO.getPageSize() * queryParamBO.getCurrentPage());
        // 过滤出站点
        List<PalletDataDTO> palletDataList = externalInterfacesRepository.queryPalletData(queryParamBO);
        pageRows.setTotal(externalInterfacesRepository.queryPalletDataTotal(queryParamBO));
        pageRows.setRows(palletDataList);
        pageRows.setCurrent(queryParamBO.getCurrentPage());

        // 获取库房信息
        List<String> palletNoList = palletDataList.stream().filter(item -> StringUtils.isNotBlank(item.getPalletNo()))
                .map(PalletDataDTO::getPalletNo).distinct().collect(Collectors.toList());
        Map<String, PalletDataDTO> palletMap = this.queryStockNoAndName(palletNoList);
        // 设置库房属性
        for (PalletDataDTO palletDataDTO : palletDataList) {
            if(StringUtils.isNotEmpty(palletDataDTO.getStrBills())) {
                List<String> billList = Arrays.asList(palletDataDTO.getStrBills().split(","));
                palletDataDTO.setBillList(billList);
            }else{
                palletDataDTO.setBillList(new ArrayList<>());
            }
            palletDataDTO.setStrBills(null);

            PalletDataDTO dataDTO = palletMap.get(palletDataDTO.getPalletNo());
            if (Objects.isNull(dataDTO)) {
                continue;
            }
            palletDataDTO.setStockNo(dataDTO.getStockNo());
            palletDataDTO.setStockName(dataDTO.getStockName());
        }
        return pageRows;
    }/* Ended by AICoder, pid:16c36b758a40472fb273a4556b3db757 */

    private Map<String, PalletDataDTO> queryStockNoAndName(List<String> palletNoList) {
        if (CollectionUtils.isNotEmpty(palletNoList)) {
            List<List<String>> splitList = CommonUtils.splitList(palletNoList, Constant.INT_100);
            List<PalletDataDTO> resultList = new LinkedList<>();
            for (List<String> list : splitList) {
                List<PalletDataDTO> stockList = externalInterfacesRepository.batchQueryStockName(list);
                if (CollectionUtils.isNotEmpty(stockList)) {
                    resultList.addAll(stockList);
                }
            }
            return resultList.stream()
                    .collect(Collectors.toMap(PalletDataDTO::getPalletNo, value -> value, (k1, k2) -> k1));
        }
        return new HashMap<>();
    }

    /**Started by AICoder, pid:76459031844b471baa80fd5af8f5ea0b
     * 查询MES箱单明细数据
     *
     * @param queryParamBO 查询MES箱单明细数据
     * @return MES箱单明细数据
     */
    @Override
    public PageRows<PackingDetailsDTO> queryPackingListDetails(QueryParamBO queryParamBO) {
        PageRows<PackingDetailsDTO> pageRows = new PageRows<>();

        if(queryParamBO.getCurrentPage() <= Constant.INT_0){
            queryParamBO.setCurrentPage(Constant.INT_1);
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        String pageSize = mesGetDictInforRepository.getDicDescription(Constant.LOOK_UP_CODE_302003700002);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        if(queryParamBO.getPageSize() <= Constant.INT_0  ||  queryParamBO.getPageSize() > Integer.valueOf(pageSize)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, String.format(MessageId.PAGESIZE_BETWEEN_0_AND_500,Integer.valueOf(pageSize)));
        }
        if (CollectionUtils.isEmpty(queryParamBO.getBillNumberList())) {
            return pageRows;
        }

        queryParamBO.setStartRow((queryParamBO.getCurrentPage() -Constant.INT_1) * queryParamBO.getPageSize() + Constant.INT_1);
        queryParamBO.setEndRow(queryParamBO.getPageSize() * queryParamBO.getCurrentPage());
        // 过滤出站点
        List<PackingDetailsDTO> list = externalInterfacesRepository.queryPackingListDetails(queryParamBO);
        pageRows.setTotal(externalInterfacesRepository.queryPackingListDetailsTotal(queryParamBO));
        pageRows.setRows(list);
        pageRows.setCurrent(queryParamBO.getCurrentPage());

        return pageRows;
    }/* Ended by AICoder, pid:76459031844b471baa80fd5af8f5ea0b */

    /**Started by AICoder, pid:1415726146e64e01bec95d6ab30bf89f
     * MES箱单数据
     *
     * @param queryParamBO 查询参数
     * @return
     */
    @Override
    public PageRows<PackingDataDTO> queryPackingData(QueryParamBO queryParamBO) {
        PageRows<PackingDataDTO> pageRows = new PageRows<>();

        if(queryParamBO.getCurrentPage() <= Constant.INT_0){
            queryParamBO.setCurrentPage(Constant.INT_1);
        }

        DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
        String pageSize = mesGetDictInforRepository.getDicDescription(Constant.LOOK_UP_CODE_302003700001);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

        if(queryParamBO.getPageSize() <= Constant.INT_0  ||  queryParamBO.getPageSize() > Integer.valueOf(pageSize)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, String.format(MessageId.PAGESIZE_BETWEEN_0_AND_500,Integer.valueOf(pageSize)));
        }

        if (CollectionUtils.isEmpty(queryParamBO.getEntityNameList())
                && CollectionUtils.isEmpty(queryParamBO.getBillNumberList())
                && CollectionUtils.isEmpty(queryParamBO.getPalletNoList())
                && CollectionUtils.isEmpty(queryParamBO.getEntitySiteList())) {
            // 所有参数为空返回
            return pageRows;
        }
        queryParamBO.setStartRow((queryParamBO.getCurrentPage() -Constant.INT_1) * queryParamBO.getPageSize() + Constant.INT_1);
        queryParamBO.setEndRow(queryParamBO.getPageSize() * queryParamBO.getCurrentPage());
        // 过滤出站点
        List<PackingDataDTO> list = externalInterfacesRepository.queryPackingData(queryParamBO);
        pageRows.setTotal(externalInterfacesRepository.queryPackingDataTotal(queryParamBO));
        pageRows.setRows(list);
        pageRows.setCurrent(queryParamBO.getCurrentPage());
        if (CollectionUtils.isEmpty(list)) {
            return pageRows;
        }
        // 1. 获取子任务状态
        Map<String, String> traceMap = this.queryTaskSubStatusName(list);
        // 2. 高端设备 描述获取
        Map<String, String> productMap = this.queryProductLabel(list);
        // 3. 获取托盘库房信息
        List<String> palletNoList = list.stream().filter(item -> StringUtils.isNotBlank(item.getPalletNo()))
                .map(PackingDataDTO::getPalletNo).distinct().collect(Collectors.toList());
        Map<String, PalletDataDTO> palletNoMap = this.queryStockNoAndName(palletNoList);
        // 3.设置相关属性
        for (PackingDataDTO packingDataDTO : list) {
            // 3.1 设置高端设备字段
            packingDataDTO.setProductLabelName(productMap.get(packingDataDTO.getBillId()));
            // 3.2 设置子任务状态
            packingDataDTO.setTaskSubStatusName(traceMap.get(packingDataDTO.getCceEntityId()));
            // 3.3 设置库房信息
            PalletDataDTO dataDTO = palletNoMap.get(packingDataDTO.getPalletNo());
            if (Objects.nonNull(dataDTO)) {
                packingDataDTO.setStockNo(dataDTO.getStockNo());
                packingDataDTO.setStockName(dataDTO.getStockName());
            }
            // 3.4 辅箱箱号
            String mainBillNumberL = packingDataDTO.getMainBillNumberL();
            if(StringUtils.isNotEmpty(mainBillNumberL)){
                List<String> mainBillNumberList = Arrays.asList(mainBillNumberL.split(Constant.COMMA));
                packingDataDTO.setMainBillNumberList(mainBillNumberList);
            }
            // 3.5 合箱箱号集合
            String mergeBillNumberL = packingDataDTO.getMergeBillNumberL();
            if(StringUtils.isNotEmpty(mergeBillNumberL)){
                List<String> mergeBillNumberList = Arrays.asList(mergeBillNumberL.split(Constant.COMMA));
                packingDataDTO.setMergeBillNumberList(mergeBillNumberList);
            }
        }
        return pageRows;
    }/* Ended by AICoder, pid:1415726146e64e01bec95d6ab30bf89f */

    /**
     * 查询高端设备
     *
     * @param list 箱单明细信息
     * @return 高端设备信息
     */
    private Map<String, String> queryProductLabel(List<PackingDataDTO> list) {
        List<String> entityIdList = list.stream().filter(item -> StringUtils.isNotBlank(item.getCceEntityId()))
                .map(PackingDataDTO::getCceEntityId).distinct().collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(entityIdList, Constant.INT_100);
        List<PackingDataDTO> labelList = new LinkedList<>();
        for (List<String> idList : splitList) {
            List<PackingDataDTO> tempList = externalInterfacesRepository.batchQueryProductLabelName(idList);
            if (CollectionUtils.isNotEmpty(tempList)) {
                labelList.addAll(tempList);
            }
        }
        return labelList.stream().filter(item -> StringUtils.isNotBlank(item.getBillId()))
                .filter(item -> StringUtils.isNotBlank(item.getProductLabelName()))
                .collect(Collectors.toMap(PackingDataDTO::getBillId, PackingDataDTO::getProductLabelName, (k1, k2) -> k1));
    }

    /**
     * 查询子任务状态
     *
     * @param list 数据集
     * @return id，name map
     */
    private Map<String, String> queryTaskSubStatusName(List<PackingDataDTO> list) {
        List<String> entityIdList = list.stream().filter(item -> StringUtils.isNotBlank(item.getCceEntityId()))
                .map(PackingDataDTO::getCceEntityId)
                .distinct().collect(Collectors.toList());
        List<WmesWerpConEntityTrace> traceList = wmesWerpConEntityTraceService.batchQueryStatusName(entityIdList);
        if (Objects.isNull(traceList)) {
            traceList = new LinkedList<>();
        }
        return traceList.stream()
                .filter(item -> Objects.nonNull(item.getEntityId()))
                .filter(item -> StringUtils.isNotBlank(item.getStatusName()))
                .collect(Collectors.toMap(item -> item.getEntityId().toString(),
                        WmesWerpConEntityTrace::getStatusName, (k1, k2) -> k1));
    }

    /**
     * 查询MES合同任务数据
     *
     * @param queryParamBO
     * @return
     * @throws Exception
     */
    @Override
    @DataSource(DatabaseType.WMES)
    public PageRows<ContractAndTaskDataDTO> queryContractAndTaskData(QueryParamBO queryParamBO) throws Exception {

        PageRows<ContractAndTaskDataDTO> pageRows = new PageRows<>();

        if (CollectionUtils.isEmpty(queryParamBO.getContracts()) && CollectionUtils.isEmpty(queryParamBO.getTasks())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CONTRACT_AND_TASK_IS_NULL);
        }

        String pageSize = mesGetDictInforRepository.getDicDescription(Constant.LOOK_UP_CODE_302003700006);

        if(queryParamBO.getPageSize() <= Constant.INT_0  ||  queryParamBO.getPageSize() > Integer.valueOf(pageSize)){
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, String.format(MessageId.PAGESIZE_BETWEEN_0_AND_500,Integer.valueOf(pageSize)));
        }
        if (queryParamBO.getCurrentPage() < Constant.INT_1) {
            queryParamBO.setCurrentPage(Constant.INT_1);
        }
        queryParamBO.setStartRow((queryParamBO.getCurrentPage() - Constant.INT_1) * queryParamBO.getPageSize() + Constant.INT_1);
        queryParamBO.setEndRow(queryParamBO.getPageSize() * queryParamBO.getCurrentPage());
        // 查询合同任务信息
        List<ContractAndTaskDataDTO> contractAndTaskDataDTOList = externalInterfacesRepository.queryContractAndTaskData(queryParamBO);
        pageRows.setTotal(externalInterfacesRepository.queryContractAndTaskDataCount(queryParamBO));
        pageRows.setRows(contractAndTaskDataDTOList);
        pageRows.setCurrent(queryParamBO.getCurrentPage());
        if (CollectionUtils.isEmpty(contractAndTaskDataDTOList)) {
            return pageRows;
        }
        List<String> entityIdList = contractAndTaskDataDTOList.stream().map(ContractAndTaskDataDTO::getEntityId).collect(Collectors.toList());
        for (List<String> tempList : CommonUtils.splitList(entityIdList, NumConstant.NUM_FIVE_HUNDRED)) {
            // 查询布点信息
            List<SiteInfoDTO> siteInfoDTOList = externalInterfacesRepository.querySiteInfoByEntityId(tempList);
            if (CollectionUtils.isEmpty(siteInfoDTOList)) {
                continue;
            }
            for (ContractAndTaskDataDTO dto : contractAndTaskDataDTOList) {
                if (tempList.contains(dto.getEntityId())) {
                    List<SiteInfoDTO> siteInfoDTOS = siteInfoDTOList.stream().filter(i -> i.getEntityId().equals(dto.getEntityId())).collect(Collectors.toList());
                    dto.setSiteInfo(siteInfoDTOS);
				}
            }
        }
        return pageRows;
    }

     @Override
    public List<PickListResultDTO> queryMaterialOrderNoByTaskNo(List<String> taskNos) {
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMES);
        List<PickListResultDTO> picks = externalInterfacesRepository.queryMaterialOrderNoByTaskNo(taskNos);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return picks;
    }


    @Override
    public List<ProdPickListMainDTO> queryPickListByTaskNos(PickListQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(queryDTO.getTaskNos()) && (StringUtils.isEmpty(queryDTO.getStartDate()) || StringUtils.isEmpty(queryDTO.getEndDate()))) {
            return Collections.emptyList();
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMES);
        List<ProdPickListMainDTO> picks = externalInterfacesRepository.queryPickListByTaskNos(queryDTO);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return picks;
    }

    /**
     * 查询领料单信息
     *
     * @param dto queryDTO
     * @return List<ProdPickListMainDTO>
     */
    @Override
    public List<ProdPickListMainDTO> queryPickListCondition(PickListQueryDTO dto) {
        if (StringUtils.isBlank(dto.getBillNumber())
                && (StringUtils.isEmpty(dto.getStartDate()) || StringUtils.isEmpty(dto.getEndDate()))) {
            return Collections.emptyList();
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMES);
        List<ProdPickListMainDTO> resultList = externalInterfacesRepository.queryPickListCondition(dto);
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return resultList;
    }

    /**
     * 获取领料单明细信息
     *
     * @param billNumberList billNumberList
     * @return List<ProcPicklistDetail>
     */
    @Override
    public List<ProcPicklistDetail> queryProcPickDetailBatch(List<String> billNumberList) {
        List<ProcPicklistDetail> resultList = new LinkedList<>();
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMES);
        List<List<String>> splitList = CommonUtils.splitList(billNumberList, Constant.INT_100);
        for (List<String> items : splitList) {
            List<ProcPicklistDetail> procPicklistDetails = externalInterfacesRepository.queryProcPickDetailBatch(items);
            if (CollectionUtils.isNotEmpty(procPicklistDetails)) {
                resultList.addAll(procPicklistDetails);
            }
        }
        DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);
        return resultList;
    }
}
