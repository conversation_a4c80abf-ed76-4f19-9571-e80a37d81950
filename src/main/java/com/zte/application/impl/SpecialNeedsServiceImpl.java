package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.SpecialNeedsService;
import com.zte.domain.model.SpecialNeedsRepository;
import com.zte.interfaces.dto.SpecialNeedsDetailDTO;
import com.zte.interfaces.dto.SpecialNeedsHeadDTO;
import com.zte.interfaces.dto.SpecialNeedsItemDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 17:26
 */
@Service
@DataSource(value = DatabaseType.WMES)
public class SpecialNeedsServiceImpl implements SpecialNeedsService {

    @Autowired
    private SpecialNeedsRepository specialNeedsRepository;
    @Override
    public Page<SpecialNeedsHeadDTO> searchHeadTableData(SpecialNeedsHeadDTO dto) {
        if (StringUtils.isEmpty(dto.getContractNumber())) {
            return new Page<>();
        }
        Page<SpecialNeedsHeadDTO> page = new Page<>(dto.getPage(), dto.getPageSize());
        page.setParams(dto);
        page.setRows(specialNeedsRepository.searchHeadTableData(page));
        return page;
    }

    @Override
    public Page<SpecialNeedsDetailDTO> searchDetailTableData(SpecialNeedsDetailDTO dto) {
        if (StringUtils.isEmpty(dto.getEntityId())) {
            return new Page<>();
        }
        Page<SpecialNeedsDetailDTO> page = new Page<>(dto.getPage(), dto.getPageSize());
        page.setParams(dto);
        page.setRows(specialNeedsRepository.searchDetailTableData(page));
        return page;
    }

    @Override
    public Page<SpecialNeedsItemDTO> searchItemTableData(SpecialNeedsItemDTO dto) {
        if (StringUtils.isEmpty(dto.getMfgSiteId())) {
            return new Page<>();
        }
        Page<SpecialNeedsItemDTO> page = new Page<>(dto.getPage(), dto.getPageSize());
        page.setParams(dto);
        page.setRows(specialNeedsRepository.searchItemTableData(page));
        return page;
    }
}
