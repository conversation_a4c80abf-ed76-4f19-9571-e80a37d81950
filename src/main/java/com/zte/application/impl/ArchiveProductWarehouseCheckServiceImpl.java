package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveProductWarehouseCheckClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  成品入库检验归档
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveProductWarehouseCheckServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveProductWarehouseCheckServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveProductWarehouseCheckClient archiveProductWarehouseCheckClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("成品入库检验归档start......");
        String fileName = ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        //根据入库单号查询成品入库单据
        List<ArchiveProductWarehouseCheckDTO> archiveProductWarehouseCheckDTOS = selectProductWarehouseByFinishNo(taskSendArchive);
        //装箱清单明细查询
        List<ArchiveProductWarehouseBoxDetailDTO> archiveProductWarehouseBoxDetailDTOS = selectProductWarehouseBoxDetailByFinishNo(taskSendArchive);
        //成品入库清单单据信息列表excel
        AttachmentUploadVo archiveProductWarehouseCheckExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.PRODUCT_WAREHOUSE_CHECK_MAP, archiveProductWarehouseCheckDTOS);
       //清单明细查询单据信息列表excel
        LinkedHashMap<String,LinkedHashMap<String,String>> sheetHeadMap = new LinkedHashMap();
        sheetHeadMap.put(ArchiveConstant.PRODUCT_WAREHOUSE_SHEET_NAMES[0],ArchiveConstant.PRODUCT_WAREHOUSE_HEADER_MAP);
        sheetHeadMap.put(ArchiveConstant.PRODUCT_WAREHOUSE_SHEET_NAMES[1],ArchiveConstant.PRODUCT_WAREHOUSE_BOX_DETAIL_MAP);

        LinkedHashMap<String,List> dataListMap = new LinkedHashMap();
        dataListMap.put(ArchiveConstant.PRODUCT_WAREHOUSE_SHEET_NAMES[0],archiveProductWarehouseCheckDTOS);
        dataListMap.put(ArchiveConstant.PRODUCT_WAREHOUSE_SHEET_NAMES[1],archiveProductWarehouseBoxDetailDTOS);
        AttachmentUploadVo archiveProductWarehouseBoxDetailExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", sheetHeadMap, dataListMap);

        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(archiveProductWarehouseCheckExcelUploadVo);
        attachmentDataList.add(archiveProductWarehouseBoxDetailExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveProductWarehouseCheckDTOS);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("成品入库检验归档end......");
        return item;
    }

    private List<ArchiveProductWarehouseCheckDTO> selectProductWarehouseByFinishNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveProductWarehouseCheckDTO>> serviceData = archiveProductWarehouseCheckClient.selectProductWarehouseByFinishNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取成品入库单单据信息返回报错：{}",msg);
            throw new RouteException("调用服务获取成品入库单单据信息返回报错");
        }

        List<ArchiveProductWarehouseCheckDTO> archiveProductWarehouseCheckDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveProductWarehouseCheckDTO.class);
        if(CollectionUtils.isEmpty(archiveProductWarehouseCheckDTOS)){
            return new ArrayList<>();
        }
        return archiveProductWarehouseCheckDTOS;
    }

    private List<ArchiveProductWarehouseBoxDetailDTO> selectProductWarehouseBoxDetailByFinishNo(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveProductWarehouseBoxDetailDTO>> serviceData = archiveProductWarehouseCheckClient.selectProductWarehouseBoxDetailByFinishNo(taskSendArchive.getBillNo());
        String code=serviceData.getCode().getCode();
        String msg=serviceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取入库单号查询箱明细信息返回报错：{}",msg);
            return new ArrayList<>();
        }

        List<ArchiveProductWarehouseBoxDetailDTO> archiveProductWarehouseBoxDetailDTOS =  JSONObject.parseArray(JSONObject.toJSONString(serviceData.getBo()),ArchiveProductWarehouseBoxDetailDTO.class);
        if(CollectionUtils.isEmpty(archiveProductWarehouseBoxDetailDTOS)){
            return new ArrayList<>();
        }
        return archiveProductWarehouseBoxDetailDTOS;
    }



    private ArchiveBusinessVo buildBusiness(List<ArchiveProductWarehouseCheckDTO> archiveProductWarehouseCheckDTOS) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = archiveProductWarehouseCheckDTOS.get(0);
        // 提交日期
        Date confirmDate = DateUtil.convertStringToDate(archiveProductWarehouseCheckDTO.getConfirmDateStr(),DateUtils.DATE_FORMAT_FULL);
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archiveProductWarehouseCheckDTO.getApplierName())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(confirmDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(confirmDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveProductWarehouseCheckDTO.getFinishNo());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(pubHrvOrg.getUserNameId());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getName()+"-"+archiveProductWarehouseCheckDTO.getFinishNo());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        // 任务号
        businessVo.setOther(archiveProductWarehouseCheckDTO.getWipEntityName());
        return businessVo;
    }


    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("product warehouse archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveProductWarehouseCheckDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.PRODUCT_WAREHOUSE.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("product warehouse archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }
    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        long start=System.currentTimeMillis();
        ServiceData<Page<ArchiveProductWarehouseCheckDTO>> listServiceData = archiveProductWarehouseCheckClient.selectProductWarehouseList(archiveQueryParamDTO);
        long end=System.currentTimeMillis();
        logger.info("time:{}",(end-start)/1000);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档成品入库单单据返回报错：{}", msg);
            throw new RouteException("调用服务获取待归档成品入库单单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveProductWarehouseCheckDTO> dataList, String taskType) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId("");
            archiveItem.setBillNo(dataItem.getFinishNo());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
