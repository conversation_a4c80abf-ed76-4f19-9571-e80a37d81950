package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.enums.*;
import com.zte.common.utils.*;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveIpqcClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DataSource(value = DatabaseType.SFC)
public class ArchiveIpqcServiceImpl implements ArchiveBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ArchiveIpqcServiceImpl.class);

    @Autowired
    private ArchiveCommonService archiveCommonService;

    @Autowired
    private ArchiveFileProperties archiveFileProperties;

    @Autowired
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Autowired
    private ArchiveIpqcClient archiveIpqcClient;

    @Override
    public ArchiveReq.ArchiveItem archive(ArchiveTaskSend taskSendArchive) throws Exception {
        logger.info("IPQC确认归档start......");
        String fileName = ArchiveBusinessTypeEnum.IPQC_CONFIRM.getName()+ ArchiveConstant.JOIN_STR+taskSendArchive.getBillNo();
        ArchiveIpqcDTO archiveIpqcDTO = selectIpqcById(taskSendArchive);
        List<ArchiveIpqcDetailDTO> archiveIpqcDetailDTOList = selectIpqcDetailByHeaderId(taskSendArchive);
        //IPQC确认
        AttachmentUploadVo ipqcExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-1", ArchiveConstant.IPQC_SHEET_HEAD_MAP, Collections.singletonList(archiveIpqcDTO));
        //IPQC确认明细
        AttachmentUploadVo ipqcDetailsExcelUploadVo = archiveCommonService.createExcelAndUpload(taskSendArchive, fileName+"-2", ArchiveConstant.IPQC_DETAIL_SHEET_HEAD_MAP, archiveIpqcDetailDTOList);
        List<AttachmentUploadVo> attachmentDataList = new ArrayList<>();
        attachmentDataList.add(ipqcExcelUploadVo);
        attachmentDataList.add(ipqcDetailsExcelUploadVo);

        //组装附件,修改附件名称
        List<ArchiveReq.ArchiveItemDoc> itemDocs = ArchiveBusiness.buildArchiveItemDocVo(attachmentDataList);
        //组装业务数据
        String businessId = ArchiveBusinessTypeEnum.IPQC_CONFIRM.getCode() + ArchiveConstant.UNDER_LINE + taskSendArchive.getTaskId() + ArchiveConstant.UNDER_LINE + System.currentTimeMillis();
        ArchiveReq.ArchiveItem item = ArchiveBusiness.buildArchiveItemVo(businessId, ArchiveBusinessTypeEnum.IPQC_CONFIRM.getName(), archiveFileProperties.getCommunicationCode());
        ArchiveBusinessVo businessVo = buildBusiness(archiveIpqcDTO);
        item.setItemContent(ArchiveBusiness.generateXmlData(businessVo));
        item.setItemDocs(itemDocs);
        logger.info("IPQC确认归档end......");
        return item;
    }

    private ArchiveIpqcDTO selectIpqcById(ArchiveTaskSend taskSendArchive){
        ServiceData<ArchiveIpqcDTO> archiveIpqcDTOServiceData = archiveIpqcClient.selectIpqcById(taskSendArchive.getBillId());
        String code=archiveIpqcDTOServiceData.getCode().getCode();
        String msg=archiveIpqcDTOServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取ipqc确认单据返回报错：{}",msg);
            return new ArchiveIpqcDTO();
        }
        ArchiveIpqcDTO archiveIpqcDTO = archiveIpqcDTOServiceData.getBo();
        if(archiveIpqcDTO.getApplyDate()!=null) {
            archiveIpqcDTO.setApplyDateStr(DateUtil.convertDateToString(archiveIpqcDTO.getApplyDate(), DateUtil.DATE_FORMATE_FULL));
        }
        if(archiveIpqcDTO.getConfirmDate()!=null) {
            archiveIpqcDTO.setConfirmDateStr(DateUtil.convertDateToString(archiveIpqcDTO.getConfirmDate(), DateUtil.DATE_FORMATE_FULL));
        }

        return archiveIpqcDTO;
    }

    private List<ArchiveIpqcDetailDTO> selectIpqcDetailByHeaderId(ArchiveTaskSend taskSendArchive){
        ServiceData<List<ArchiveIpqcDetailDTO>> archiveIpqcDTOServiceData = archiveIpqcClient.selectIpqcDetailByHeaderId(taskSendArchive.getBillId());
        String code=archiveIpqcDTOServiceData.getCode().getCode();
        String msg=archiveIpqcDTOServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取ipqc确认单据详情返回报错：{}",msg);
            return new ArrayList<>();
        }
        List<ArchiveIpqcDetailDTO> detailDTOS =  JSONObject.parseArray(JSONObject.toJSONString(archiveIpqcDTOServiceData.getBo()),ArchiveIpqcDetailDTO.class);
        if(CollectionUtils.isEmpty(detailDTOS)){
            return new ArrayList<>();
        }
        return detailDTOS;
    }

    private ArchiveBusinessVo buildBusiness(ArchiveIpqcDTO archiveIpqcDTO) {
        ArchiveBusinessVo businessVo = new ArchiveBusinessVo();
        Date confirmDate = archiveIpqcDTO.getConfirmDate() == null? new Date():archiveIpqcDTO.getConfirmDate();
        PubHrvOrg pubHrvOrg = ArchiveBusiness.setDefaultDept(pubHrvOrgRepository.getPubHrvOrgByUserNameId(CommonUtil.regexUserId(archiveIpqcDTO.getConfirmedBy())));
        // 分类号
        businessVo.setClassificationCode(ArchiveConstant.CLASSIFICATION_CODE);
        // 年度
        businessVo.setYear(String.valueOf(DateUtil.getYear(confirmDate)));
        // 保管期限
        businessVo.setStoragePeriod(ArchiveYearEnum.THIRTY_YEAR.getName());
        // 发文日期 yyyyMMdd
        businessVo.setIssueDate(DateUtil.convertDateToString(confirmDate, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 文号或图号
        businessVo.setDocCode(archiveIpqcDTO.getTransNum());
        // 业务模块
        businessVo.setBusinessModule(ArchiveConstant.MODULE_ENTITY_PLAN);
        // 责任者
        businessVo.setLiablePerson(archiveIpqcDTO.getConfirmedBy());
        // 文件题名
        businessVo.setFileTitle(ArchiveBusinessTypeEnum.IPQC_CONFIRM.getName()+"-"+archiveIpqcDTO.getTransNum());
        // 关键词
        businessVo.setKeyWord(ArchiveBusinessTypeEnum.IPQC_CONFIRM.getName());
        // 密级
        businessVo.setSecretDegree(SecretDegreeEnum.INTERNAL_DISCLOSURE.getName());
        // 归档类型
        businessVo.setArchiveType(ArchiveModeEnum.ELECTRONICS.getName());
        // 归档日期 yyyyMMdd
        businessVo.setArchiveDate(DateUtil.convertDateToString(new Date(), DateUtils.DATE_FORMAT_YEAR_MONTH_DAY));
        // 归档地址 业务系统名称
        businessVo.setArchiveSource(ArchiveConstant.ARCHIVE_MES);
        // 文件材料名称
        businessVo.setFileName(ArchiveBusinessTypeEnum.IPQC_CONFIRM.getFileMaterialName());
        // 归档部门
        businessVo.setArchiveDept(pubHrvOrg.getOrgFullName());
        // 归档部门编号
        businessVo.setArchiveDeptCode(pubHrvOrg.getOrgNo());
        // 全宗号
        businessVo.setFileNumber(ArchiveConstant.QUANZONG_NUMBER);
        return businessVo;
    }

    @Override
    public Page<ArchiveTaskSend> getArchiveDataList(ArchiveQueryParamDTO archiveQueryParamDTO) {
        logger.info("ipqc archive data {} {}", archiveQueryParamDTO.getStartDate(), archiveQueryParamDTO.getEndDate());
        Page<ArchiveIpqcDTO> pageInfo = getReturnPage(archiveQueryParamDTO);
        List<ArchiveTaskSend> archiveTaskSendList = createTaskSendArchiveList(pageInfo.getRows(), ArchiveBusinessTypeEnum.IPQC_CONFIRM.getCode());
        Page<ArchiveTaskSend> page = new Page<>();
        page.setCurrent(pageInfo.getCurrent());
        page.setTotalPage(pageInfo.getTotalPage());
        page.setRows(archiveTaskSendList);
        logger.info("payable archive data page {} {}", pageInfo.getTotalPage(), pageInfo.getPageSize());
        return page;
    }

    private Page getReturnPage(ArchiveQueryParamDTO archiveQueryParamDTO){
        ServiceData<Page<ArchiveIpqcDTO>> listServiceData = archiveIpqcClient.selectIpqcList(archiveQueryParamDTO);
        String code = listServiceData.getCode().getCode();
        String msg = listServiceData.getCode().getMsg();
        if (!Constant.SUCCESS_CODE.equals(code)) {
            logger.error("调用服务获取待归档ipqc单据返回报错：{}", msg);
            throw new RouteException("调用服务获取待归档ipqc单据返回报错");
        }
        return listServiceData.getBo();
    }

    private List<ArchiveTaskSend> createTaskSendArchiveList(List<ArchiveIpqcDTO> dataList, String taskType) {
        if(CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<ArchiveTaskSend> list = dataList.stream().map(dataItem -> {
            ArchiveTaskSend archiveItem = new ArchiveTaskSend();
            archiveItem.setTaskType(taskType);
            archiveItem.setBillId(String.valueOf(dataItem.getId()));
            archiveItem.setBillNo(dataItem.getTransNum());
            archiveItem.setBillReserves("");
            archiveItem.setStatus(ExecuteStatusEnum.UNEXECUTE.getCode());
            archiveItem.setCreatedBy(archiveFileProperties.getEmpNo());
            archiveItem.setEnabledFlag(ArchiveConstant.ENABLE_FLAG);
            return archiveItem;
        }).collect(Collectors.toList());
        return list;
    }
}
