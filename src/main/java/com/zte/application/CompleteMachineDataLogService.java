package com.zte.application;

import com.zte.interfaces.dto.BarcodeNetSignDTO;
import com.zte.interfaces.dto.SpSpecialityNalDTO;
import com.zte.interfaces.dto.UploadCompleteMachineDataDTO;

import java.util.List;

/**
 * 整机数据上传-整机数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-15 16:55:14
 */
public interface CompleteMachineDataLogService {

    void uploadCompleteMachineData(UploadCompleteMachineDataDTO dto) throws Exception;

    void uploadResourceInfoToMes(List<SpSpecialityNalDTO> list) throws Exception;

    List<SpSpecialityNalDTO> getMacByResourceNumber(List<String> list);

    List<BarcodeNetSignDTO> selectBarAccSignForSchTask(BarcodeNetSignDTO barcodeNetSignDTO);
}

