package com.zte.application;

import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/7
 * @description : BOQ-BOM装箱单拟制归档数据查询服务
 */
public interface ArchiveBoqBoxMakeDataService {

    /**
     * 根据任务号查询BOp装箱拟制
     *
     * @param billNo 任务号
     * @return BOp装箱拟制
     */
    ArchiveBoqBoxMakeDTO getByEntityName(String billNo);

    /**
     * 根据组织id和生产站点id查询单元箱列表信息
     *
     * @param billId 单据id
     * @return
     */
    List<ArchiveBoqUnitBoxMakeDTO> getArchiveUnitBoxMakeByBillId(String billId);

    /**
     * 根据组织id和单据id查询箱物料列表
     *
     * @param organizationId 组织id
     * @param billId         单据id
     * @return 箱物料列表
     */
    List<ArchiveBoqBoxMakeItemDTO> getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId);

    /**
     * 根据组织id和单据id查询箱物料列表(装箱单拟制)
     *
     * @param organizationId 组织id
     * @param billId         单据id
     * @return 箱物料列表
     */
    List<ArchiveBoxMakeItemDTO> getArchiveBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId);

    /**
     * 根据组织id和单据id查询箱物料列表(Eu装箱单拟制)
     *
     * @param organizationId 组织id
     * @param billId         单据id
     * @return 箱物料列表
     */
    List<ArchiveBoqBoxMakeItemDTO> getArchiveEuBoxMakeItemByOrganizationIdAndBillId(String organizationId, String billId);

    /**
     * 根据组织id和生产站点id查询单元箱列表信息
     *
     * @param billId 单据id
     * @return
     */
    List<ArchiveEuUnitBoxMakeDTO> getArchiveEuUnitBoxMakeByBillid(String billId);

    /**
     * 根据时间范围查询归档数据
     *
     * @param queryDTO    归档条件
     * @param boxMakeType
     * @return 归档数据
     */
    Page<ArchiveBoqBoxMakeDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO, String boxMakeType);

    /**
     * 根据配置详情id查询二层物料代码和二层物料名称
     * @param configDetailId 配置详情id
     * @return 二层物料信息
     */
    ArchiveBoqBoxMakeItemDTO getSecondItem(String configDetailId);

    /**
     * 获取四层物料代码
     * @param billItemId 单据项id
     * @param organizationId 组织id
     * @return 四层物料列表
     */
    List<ArchiveBoqBoxMakeItemDTO> getFourMakeItem(String billItemId, String organizationId);

    /**
     * 获取eu和boq箱物料列表
     * @param organizationId 组织id
     * @param billId 单据id
     * @return
     */
    List<ArchiveBoqBoxMakeItemDTO> getEUAdnBoqMakeItem(String organizationId, String billId);

}
