package com.zte.application;


import com.zte.interfaces.dto.TaskCompleteEntitiesDTO;
import com.zte.interfaces.dto.UploadCompleteMachineDataDTO;

import java.util.List;


/**
 * 整机数据上传-生产订单完工反馈
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-02 16:55:14
 */
public interface CompleteFeedbackDataLogService {
    void uploadCompleteFeedbackData(UploadCompleteMachineDataDTO dto) throws Exception;

    /**
     * 整机数据上传B2B-生产订单完工反馈
     *
     * <AUTHOR>
     * @param tempInsertList  完工任务号
     * @param dto 入参
     * @param taskNo 批次号
     * @throws Exception 异常
     */
    void pushDataToB2B(List<TaskCompleteEntitiesDTO> tempInsertList,UploadCompleteMachineDataDTO dto, String taskNo) throws Exception;

}
