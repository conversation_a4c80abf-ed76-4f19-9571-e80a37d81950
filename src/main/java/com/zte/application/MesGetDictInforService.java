package com.zte.application;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 * 获取数据字典参数
 */
public interface MesGetDictInforService {
    /**
     * <AUTHOR>
     * 获取数据字典参数
     */
    Map<String, Object> getDict(String lookUpType);

    /**
     * 获取描述
     * @param lookUpCode
     * @return
     */
    String getDicDescription(String lookUpCode);

    /**
     * 更新归档标识
     * @return
     */
    int updateArchiveFlag(Map<String,String> params);

}
