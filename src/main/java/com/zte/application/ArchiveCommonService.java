package com.zte.application;

import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.itp.msa.core.model.ServiceData;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 *  归档公共类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface ArchiveCommonService {

    /**
     * 根据编码加锁
     * @param lookUpCode
     * @return
     */
    int lock(String lookUpCode);

    /**
     * 根据编码解锁
     * @param lookUpCode
     * @return
     */
    int unlock(String lookUpCode);

    /**
     * 根据编码查询value
     * @param lookUpCode
     * @return
     */
    Integer getDicDescriptionByCode(String lookUpCode);
    /**
     * 保存文件到文档云
     * @param pathName  上传的文件的路径
     * @param taskSendArchive 业务单据归档任务
     * @param empNo empNo
     * @return
     */
    AttachmentUploadVo uploadFileTo(String pathName, ArchiveTaskSend taskSendArchive, String empNo) throws Exception;

    /**
     * 创建excel并上传到文档员
     * @param taskSendArchive 归档任务
     * @param excelFileName excel文件名
     * @param sheetHeadMap excel标题-字段对应map
     * @param dataList 数据集合
     * @return
     * @throws Exception
     */
    AttachmentUploadVo createExcelAndUpload(ArchiveTaskSend taskSendArchive, String excelFileName, LinkedHashMap<String, String> sheetHeadMap, List dataList) throws Exception;

    /**
     * 生产多个sheet的excel
     * @param taskSendArchive
     * @param excelFileName
     * @param sheetHeadMap
     * @param dataListMap
     * @return
     * @throws Exception
     */
    AttachmentUploadVo createExcelAndUpload(ArchiveTaskSend taskSendArchive, String excelFileName, LinkedHashMap<String,LinkedHashMap<String, String>> sheetHeadMap, LinkedHashMap<String,List> dataListMap) throws Exception;

    /**
     * 发送数据到归档系统
     * @param archiveItem 归档数据
     * @param taskSendArchive 归档任务
     * @return
     * @throws Exception
     */
    ServiceData sendDataToArchive(ArchiveReq.ArchiveItem archiveItem, ArchiveTaskSend taskSendArchive) throws Exception;


    /**
     * 从FTP下载附件并上传到文档云
     * @param taskSendArchive
     * @param fileName
     * @param ftpFilePath
     * @return
     */
    AttachmentUploadVo downloadFromFtpAndUpload(ArchiveTaskSend taskSendArchive,String fileName, String ftpFilePath) throws IOException;

    }
