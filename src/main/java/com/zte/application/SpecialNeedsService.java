package com.zte.application;

import com.zte.interfaces.dto.SpecialNeedsDetailDTO;
import com.zte.interfaces.dto.SpecialNeedsHeadDTO;
import com.zte.interfaces.dto.SpecialNeedsItemDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

public interface SpecialNeedsService {
    Page<SpecialNeedsHeadDTO> searchHeadTableData(SpecialNeedsHeadDTO dto);
    Page<SpecialNeedsDetailDTO> searchDetailTableData(SpecialNeedsDetailDTO dto);
    Page<SpecialNeedsItemDTO> searchItemTableData(SpecialNeedsItemDTO dto);
}
