package com.zte.application;

import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/17
 * @description : 单板锁定归档服务接口
 */
public interface ArchiveBomLockingBillDataService {
    /**
     * 根据时间范围查询单板锁定分页信息
     * @param archiveQueryParamDTO 归档数据提取归档参数
     * @return 单板锁定分页参数
     */
    Page<ArchiveBomLockingBillDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO);

    /**
     * 根据锁定单号查询单板锁定DTO
     * @param billNo 锁定单号
     * @return 单板锁定DTO
     */
    ArchiveBomLockingBillDTO getByBillNo(String billNo);

    /**
     * 根据单据id查询条码
     * @param billId 单据id
     * @return 条码
     */
    List<ArchiveBomLockingBarcodeDTO> getBarcodeByBillId(String billId);

    /**
     * 根据单据id查询批次信息
     * @param billId 单据id
     * @return 批次信息
     */
    List<ArchiveBomLockingPlanDTO> getPlanByBillId(String billId);
}
