package com.zte.application.datawb.impl;

import com.zte.application.datawb.BarcodeContractService;
import com.zte.application.datawb.ECCfgMaterialAssemblyRelService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.springbootframe.common.Constants.STR_ONE;

/**
 * 配置物料装配关系查询实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 10:20:00
 */
@Service
@DataSource(value = DatabaseType.SFC)
public class ECCfgMaterialAssemblyRelServiceImpl implements ECCfgMaterialAssemblyRelService {

    @Autowired
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Autowired
    private BarcodeContractService barcodeContractService;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    /**
     * 根据服务器SN列表查询物料装配关系
     *
     * @param serverSnList 服务器SN列表
     * @return 物料装配关系列表
     */
    @Override
    public List<ECMaterialAssemblyDTO> getAssemblyRelationList(List<String> serverSnList) {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(serverSnList)) {
            return list;
        }
        List<ECCpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSnList);
        if (CollectionUtils.isEmpty(cpmConfigItemAssembleDTOList)) {
            return list;
        }
        for (ECCpmConfigItemAssembleDTO ecCpmConfigItemAssembleDTO : cpmConfigItemAssembleDTOList) {
            // 判断服务器SN是否一二层校验，否则抛出异常
            if (ecCpmConfigItemAssembleDTO.getParentRecordId() != null && ecCpmConfigItemAssembleDTO.getParentRecordId() != Constant.INT_0) {
                String[] params = new String[]{ecCpmConfigItemAssembleDTO.getItemBarcode()};
                String exceptionMsg = CommonUtils.getLmbMessage(MessageId.SERVER_SN_NOT_IN_FIRST_TWO_LAYERS, params);
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, exceptionMsg);
            }
            // 根据EntityId获取任务对应的装配物料
            List<ECCpmConfigItemAssembleDTO> configDetailDTOList = wsmAssembleLinesService.getAssemblyMaterialsByEntityIdWithParent(
                    Integer.valueOf(ecCpmConfigItemAssembleDTO.getEntityId()));
            if (CollectionUtils.isEmpty(configDetailDTOList)) {
                continue;
            }
            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = convertToAssembleLinesList(configDetailDTOList);

            // 部件信息查询需要查询四层，物料代码为1开头及边框模组的需要再向下查询
            getAllWsmAssembleLines(wsmAssembleLinesList);

            // 获取合同信息(合同号、任务号、组织ID)
            List<ECMaterialAssemblyDTO> ecMaterialAssemblyDTOList = barcodeContractService.getZmsEntityListByEntityId(
                    Integer.valueOf(cpmConfigItemAssembleDTOList.get(0).getEntityId()));
            // 组装结果
            list.add(convertToMaterialAssemblyDTO(ecCpmConfigItemAssembleDTO.getItemBarcode(), ecMaterialAssemblyDTOList, wsmAssembleLinesList));
        }
        return list;
    }

    /**
     * 转换为装配关系列表
     *
     * @param configDetailDTOList 配置详情DTO列表
     * @return 装配关系列表
     */
    private List<ECWsmAssembleLinesEntityWithNameDTO> convertToAssembleLinesList(List<ECCpmConfigItemAssembleDTO> configDetailDTOList) {
        List<ECWsmAssembleLinesEntityWithNameDTO> assembleLinesList = new ArrayList<>();
        for (ECCpmConfigItemAssembleDTO itemAssembleDTO : configDetailDTOList) {
            ECWsmAssembleLinesEntityWithNameDTO dto = new ECWsmAssembleLinesEntityWithNameDTO();
            dto.setItemBarcode(itemAssembleDTO.getItemBarcode());
            dto.setScanType(itemAssembleDTO.getBarcodeType());
            dto.setItemCode(itemAssembleDTO.getItemCode());
            dto.setItemName(itemAssembleDTO.getItemName());
            dto.setItemQty(Integer.valueOf(itemAssembleDTO.getBarcodeQty()));
            dto.setCreatedBy(itemAssembleDTO.getCreatedBy());
            dto.setCreationDate(itemAssembleDTO.getCreationDate());
            dto.setParentItemBarcode(itemAssembleDTO.getParentItemBarcode());
            assembleLinesList.add(dto);
        }
        return assembleLinesList;
    }

    /**
     * 转换为物料装配关系DTO
     *
     * @param serverSn                  服务器SN
     * @param ecMaterialAssemblyDTOList 合同信息列表
     * @param wsmAssembleLinesList      装配关系列表
     * @return 物料装配关系DTO
     */
    private ECMaterialAssemblyDTO convertToMaterialAssemblyDTO(String serverSn,
                                                               List<ECMaterialAssemblyDTO> ecMaterialAssemblyDTOList,
                                                               List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {
        ECMaterialAssemblyDTO materialAssemblyDTO = new ECMaterialAssemblyDTO();
        // 服务器SN
        materialAssemblyDTO.setServerSn(serverSn);
        if (!CollectionUtils.isEmpty(ecMaterialAssemblyDTOList)) {
            // 合同号
            materialAssemblyDTO.setContractNumber(ecMaterialAssemblyDTOList.get(0).getContractNumber());
            // 任务号
            materialAssemblyDTO.setEntityName(ecMaterialAssemblyDTOList.get(0).getEntityName());
            // 组织ID
            materialAssemblyDTO.setOrgId(ecMaterialAssemblyDTOList.get(0).getOrgId());
        }
        // 组装明细列表
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            List<ECMaterialAssemblyItemDTO> assembleList = new ArrayList<>();
            for (ECWsmAssembleLinesEntityWithNameDTO dto : wsmAssembleLinesList) {
                ECMaterialAssemblyItemDTO materialAssemblyItemDTO = new ECMaterialAssemblyItemDTO();
                // 子条码
                materialAssemblyItemDTO.setItemBarcode(dto.getItemBarcode());
                // 条码类型
                materialAssemblyItemDTO.setItemType(dto.getScanType());
                // 物料代码
                materialAssemblyItemDTO.setItemCode(dto.getItemCode());
                // 物料名称
                materialAssemblyItemDTO.setItemName(dto.getItemName());
                // 数量
                materialAssemblyItemDTO.setQty(dto.getItemQty());
                // 父条码
                materialAssemblyItemDTO.setParentItemBarcode(dto.getParentItemBarcode());
                // 自建任务号
                materialAssemblyItemDTO.setWpEntityName(dto.getEntityName());
                // 绑定人
                // TODO 是否转换成名称待定
                materialAssemblyItemDTO.setAssembleby(String.valueOf(dto.getCreatedBy()));
                // 绑定时间
                materialAssemblyItemDTO.setAssembleDate(dto.getCreationDate());
                assembleList.add(materialAssemblyItemDTO);
            }
            materialAssemblyDTO.setAssembleList(assembleList);
        }
        return materialAssemblyDTO;
    }

    /**
     * 部件信息查询需要查询四层，物料代码为1开头的需要再向下查询
     * 使用循环方式优化，避免深度嵌套
     *
     * @param wsmAssembleLinesList 装配关系列表，既是输入也是输出
     */
    public void getAllWsmAssembleLines(List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {
        // 机框模组物料代码字典查询
        List<SysLookupValues> lookupValues = wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE);
        List<String> shelfModItemCodeList = lookupValues.stream().map(SysLookupValues::getDescription)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        List<String> currentLevelBarcodes = getBeExpandedBarcodes(wsmAssembleLinesList, shelfModItemCodeList);

        // 从第2层开始循环查询
        for (int level = 2; level <= Constant.MAX_LEVELS_4; level++) {
            // 如果当前层没有需要查询的条码，提前退出循环
            if (CollectionUtils.isEmpty(currentLevelBarcodes)) {
                break;
            }
            // 查询当前层级的装配关系
            List<ECWsmAssembleLinesEntityWithNameDTO> currentLevelResults =
                    wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(currentLevelBarcodes);
            // 如果当前层查询无结果，后续层也不会有结果，提前退出
            if (CollectionUtils.isEmpty(currentLevelResults)) {
                break;
            }
            // 将当前层的查询结果添加到总结果列表中
            wsmAssembleLinesList.addAll(currentLevelResults);

            // 准备下一层的查询条码列表（从当前层结果中筛选以"1"开头的和机框模组物料代码）
            currentLevelBarcodes = getBeExpandedBarcodes(currentLevelResults, shelfModItemCodeList);
        }
    }

    /**
     * 获取需要继续展开的条码列表
     *
     * @param wsmAssembleLinesList 装配关系列表
     * @param shelfModItemCodeList 机框模组物料代码列表
     * @return 需要继续展开的条码列表
     */
    private List<String> getBeExpandedBarcodes(List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList,
                                               List<String> shelfModItemCodeList) {
        // 获取初始的需要查询的条码列表（以"1"开头的物料代码）
        List<String> currentLevelBarcodes = wsmAssembleLinesList.stream()
                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList());

        // 增加机框模组代码（非1开头的物料代码）
        currentLevelBarcodes.addAll(wsmAssembleLinesList.stream()
                .filter(i -> shelfModItemCodeList.contains(i.getItemCode()))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList()));
        return currentLevelBarcodes;
    }
}
