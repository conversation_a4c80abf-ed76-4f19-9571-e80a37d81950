package com.zte.application;

import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryReturnDTO;
import com.zte.springbootframe.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/22
 * @description :
 */
public interface ArchiveSpmFactoryOrderDeliverDataService {
    /**
     * 根据计划编号查询外协订单下达归档DTO
     * @param billNo 计划编号
     * @return 外协订单下达归档DTO
     */
    ArchiveSpmFactoryOrderDeliverDTO getByRecordId(String billNo);

    /**
     * 根据时间范围参数查询外协订单下达归档DTO
     * @param archiveQueryParamDTO 时间范文
     * @return 外协订单下达归档DTO
     */
    Page<ArchiveSpmFactoryOrderDeliverDTO> getByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO);

    /**
     * 根据外协订单下达id查询回货需求
     * @param recordId 订单记录id
     * @return 回货需求
     */
    List<ArchiveSpmFactoryReturnDTO> getSpmFactoryReturnByRecordId(String recordId);

    /**
     * 根据外协订单下达id查询外协订单
     * @param recordId 外协订单id
     * @return 外协订单
     */
    List<ArchiveSpmFactoryOrderDeliverDTO> getSpmProdPlanByRecordId(String recordId,String organizationId);

    /**
     * 根据外协订单id查询外包工序
     * @param recordId 外协订单id
     * @return 外包工序
     */
    List<ArchiveSpmFactoryReturnDTO> getSpmFactoryProcessDefineByOrderId(String recordId);
}
