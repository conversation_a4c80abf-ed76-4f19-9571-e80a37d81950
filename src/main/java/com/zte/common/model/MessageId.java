package com.zte.common.model;

/**
 * 消息ID
 * <AUTHOR>
 */
public class MessageId {

	public static final String BILL_NO_EXISTS = "bill.no.exists";
	public static final String EMPTY_INPUT_CONTRACT_EG = "empty.input.contract.eg";
	public static final String ONLY_ITEMNAME = "only.itemname";
	

	public static final String ENTITY_NAME_NOT_ALL_EXIST = "entity.name.not.all.exist";
	public static final String EXIST_NOT_BOUND_ENTITY_NAME = "exist.not.bound.entity.name";
	public static final String EXIST_NOT_BOUND_MFG_SITE_ID = "exist.not.bound.mfg.site.id";

	public static final String VALIDATE_INPUT_TYPE_OF_CHECK_BINDING_ERROR = "validate.input.type.of.check.binding.error";
	public static final String PARAMS_LIST_IS_NULL = "params.list.is.null";
	public static final String SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING = "size.out.bound.params.list.of.check.binding";

	// 任务号不能为空
	public static final String ENTITY_NAME_NOT_NULL = "entity.name.not.null";

	//箱号不能为空或大于设置的上限
	public static final String BOXUP_IS_NOT_NULL_OR_GT_SET_UPPER_LIMIT="boxup.is.not.null.or.gt.set.upper.limit.%d";

	//输入的任务号站点编码不能超过10个
	public static final String ENTITYSITECODE_LIST_LENGTH_SHOULD_LESS_10 = "entitySiteCode.list.length.should.less.10";
	/**
	 * SN号为空或超过设置上限
	 */
	public static final String SN_IS_NULL_OR_EXCEEDS = "SN.is.null.or.exceeds.the.set.upper.limit";
	/**
	 * 自动上传标识不能为空
	 */
	public static final String AUTO_FLAG_IS_NOT_NULL = "auto.flag.is.not.null";

	public static final String RETCODE_PERMISSIONDENIED="RetCode.PermissionDenied";

	//输入的箱号至少有一个不存在
	public static final String BOXUP_AT_LEAST_ONE_IS_EXISTS="boxup.at.least.one.is.exists";

	// 任务号不能超过50个
	public static final String ENTITY_NAME_LIST_LENGTH_SHOULD_LESS_50 = "entity.name.list.length.should.less.50";

	// 任务号多个的话，页数必输
	public static final String CURRENT_PAGE_NOT_NULL = "current.page.not.null";

	// 任务号多个的话，页数必输
	public static final String PAGE_SIZE_NOT_GREATER_THAN_200 = "page.size.not.greater.than.200";
	public static final String GET_BOARD_ASSEMBLY_RELATIONSHIP_ERROR = "get_board_assembly_relationship_error";


	// 请输入仓库
	public static final String PLEASE_INPUT_WMWHSEID = "Please.Input.WareHouse";
	// 订单号,波次号,至少要有一个输入条件
	public static final String PLEASE_INPUT_ORDERKEY_WAVEKEY = "Please.Input.OrderKey.WaveKey";
	// 请输入箱号
	public static final String PLEASE_INPUT_ID = "Please.Input.Id";
	// SO单号,外部单号2,波次号,落放ID,至少要有一个输入条件
	public static final String PLEASE_INPUT_SO_EX_WAVE_ID = "Please.Input.OrderKey.ExternalOrderKey2.WaveKey.Id";
   //订单号,外部订单号2,至少要有一个输入条件
	public static final String PLEASE_INPUT_ORDERKEY_EXTERNALORDERKEY2 = "Please.Input.OrderKey.ExternalOrderKey2";
    // 合同号超过50行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_CONTRACTNUMBER_FIFTY = "Please.Import.Excel.NoMoreThan.Contractnumber.Fifty";
	// 发货指令号超过500行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_DOID_FIVEHUNDRED = "Please.Import.Excel.NoMoreThan.Doid.FiveHundred";
	// 装箱单号超过500行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_BILLNUMBER_FIVEHUNDRED = "Please.Import.Excel.NoMoreThan.BillNumber.FiveHundred";
	// 文件格式错误，请使用excel导入！
	public static final String PLEASE_CHECK_IMPORT_FILE_TYPE_EXCEL = "Please.Check.Import.File.Type.Excel";
	/**
     * 包装箱号不能为空,必须提供
     */
    public static final String PLEASE_INPUT_PACKNO = "Please.Input.PackIdIsNotNull";
    /**
     * 输入的条件箱号错误
     */
    public static final String INPUT_BOXPACKNO = "Input.Param.BoxPackNO.Format.Error";
    /**
     * 输入的条件任务和箱信息错误
     */
    public static final String INPUT_TASK_AND_BOXPACKNO = "Input.Param.TaskAndBoxPackNO.Format.Error";
    /**
     * 包装箱号必须是数字
     */
    public static final String INPUT__BOXPACKNO_IS_NUMBER = "Input.Param.BoxPackNO.NotNumber.Error";
	/**
	 * 查询失败
	 */
	public static final String SELECT_ERROR = "select.error";
	public static final String UPDATE_STEP_STOCK_LOCKED = "update_step_stock_locked";
	public static final String DELETE_PARAM_ERROR = "delete_param_error";
	public static final String ITEMBARCODE_STOCKNO_IS_NULL = "itembarcode_stockno_is_null";
	public static final String ITEMBARCODE_NOT_EXIST = "itembarcode_not_exist";

	public static final String TASK_NO_IS_BATCH="task.no.is.batch";
	public static final String JOB_STOP_SYSN="job.stop.sysn";

	public static final String CONDITION_NOT_FOUND = "condition.not.find";

	public static final String FACTORY_ID_IS_NULL = "factory.id.is.null";
	//该单板不属于本部件
	public static final String BOARD_NOT_BELONG = "board.not.belong";
	//该模块条码不存在
	public static final String MODULE_BARCODE_NOT_EXISTS = "module.barcode.not.exists";

	public static final String WORKORDER_SOURCESYS_IS_NULL = "workorder.sourcesys.is.null";

	public static final String MAIN_SN_PRODPLAN_IS_NULL = "main.sn.prodplan.is.null";

	public static final String SUB_SN_PRODPLAN_IS_NULL = "sub.sn.prodplan.is.null";

	public static final String SUB_SN_IS_NULL = "sub.sn.is.null";

	public static final String MAIN_SN_IS_NULL = "main.sn.is.null";

    public static final String DATA_WB_CALL_PARAM_MISS = "data.wb.call.param.miss";
	public static final String EMP_NO_IS_NULL = "emp.no.is.null";
	public static final String TIME_FORMAT_ERROR = "time.format.error";
	public static final String SFC_CUR_VALUE_IS_NULL = "sfc.return.cur.value.is.null";
	public static final String SFC_CUR_VALUE_IS_NOT_NUM = "sfc.return.cur.value.is.not.number";
	public static final String SFC_CUR_VALUE_IS_MORE_THAN_MAX = "sfc.return.cur.value.is.more.than.max";

	public static final String PLEASE_INPUT_QUERY_CONDITION = "Please.Input.query.Condition";

	public static final String PLEASE_INPUT_BILLNUMBER = "Please.Input.BillNumber";

	public static final String QUERY_MORE_BILLNUMBER = "Query.More.BillNumber";
	public static final String MODEL_QUERY_MORE_BILLNUMBER = "The.case.number.exceeds.the.allowed.maximum.input.quantity";

	public static final String ITEM_ID_FOUND_ERROR = "item.id.found.error";

	public static final String WIP_MOVE_MTL_INSERT_ERROR = "wip.move.mtl.insert.error";

	public static final String MRP_WIP_ISSUE_INSERT_ERROR = "mrp.wip.issue.insert.eror";

	public static final String TASK_NO_IS_NULL = "task.no.is.null";

	public static final String NO_DATA_FOUND = "no.data.found";
	public static final String REQUIRED_FIELDS_CANNOT_BE_EMPTY = "required_fields_cannot_be_empty";

	public static final String BOX_NO_IS_NULL = "boxNo.no.is.null";
	public static final String ITEM_TYPE_IS_NULL = "itemType.no.is.null";
	public static final String ITEM_NAME_IS_NULL = "itemName.no.is.null";
	public static final String ITEM_CODE_IS_NULL = "itemCode.no.is.null";
	public static final String MFG_SITE_ID_IS_NULL = "MFGSITE.ID.IS.NULL";
	public static final String CREATE_BARCODE_QTY_BETWEEN_1_AND_50 ="CREATE.BARCODE.QTY.BETWEEN.1.AND.50";
	public static final String SEARCH_IS_NULL_BY_ENTITYNAME_MFGSITEID_ITEMCODE_LAYER1_LAYER4="SEARCH.IS.NULL.BY.ENTITYNAME.MFGSITEID.ITEMCODE.LAYER1.LAYER4";
	public static final String CREATE_BARCODE_FAILURE="CREATE.BARCODE.FAILURE";
	public static final String ONE_LAYER_ITEMCODE_IS_NOT_PDVM="PTO.ITEMCODE.IS_NOT.PDVM";
	public static final String FOURE_LAYER_ITEMCODE_DO_NOT_MARK="FOURE.LAYER.ITEMCODE.DO.NOT.MARK";
	public static final String DATA_IS_NULL="data.is.null";
	public static final String MONTH_COMPUTE_TIME="month.compute.time";
	public static final String BARCODE_IS_NO_STOCK="barcode.is.no.stock";
	public static final String ASSET_NO_STOCK="asset.no.stock";
	public static final String BARCODE_INSUFFICIENT_INVENTORY="barcode.insufficient.inventory";
	public static final String MOLDASSETS_INSUFFICIENT_INVENTORY="moldassets.insufficient.inventory";
	public static final String MAIN_COMPONENT_IS_EMPTY="main.component.prodplanid.is.empty";
	public static final String CORRECT_PRODPLANID="correct.main.component.prodplanid";
	public static final String CAN_NOT_FIND_DATA="can.not.find.data";
	public static final String INPUT_PAGEROWS="input.pagerows";
	public static final String MSGCODE_WITH_ENTITYNAME="masgcode.with.entityname";
	public static final String MAIN_COMPONENT_BARCODE_IS_EMPTY="main.component.barcode.is.empty";
	public static final String BARCODE_MUST_BE_NUMBER="main.component.barcode.must.be.number";
	public static final String BARCODE_BITS_IS_INCORRECT="main.component.barcode.bits.is.incorrect";
	public static final String SUBCOMPONENT_BARCODE_HAS_LETTERS="the.subcomponent.barcode.has.letters";
	public static final String INPUT_VALUE_OF_THE_CHECK_BOX_IS_NULL="the.input.value.of.the.check.box.is.null";
	public static final String THE_INCOMING_VALUE_IS_NULL="the.incoming.value.is.null";
	public static final String SUBCOMPONENT_BARCODE_IS_EMPTY="subcomponent.barcode.is.empty";
	public static final String OPERATE_PERSON_IS_EMPTY="operate.person.is.empty";
	public static final String THE_QUANTITY_MUST_BE_NUMBER="the.quantity.must.be.number";
	public static final String VERIFICATION_MODE_MUST_BE_NUMBER="verification.mode.must.be.number";
	public static final String THE_MAINPART_PATCH_NOT_MEET_THE_INPUT_VALUE="the.mainpart.batch.not.meet.the.input.value.of.the.check.box";
	public static final String THE_QUERY_RECORD_IS_NULL="the.query.record.id.is.null";
	public static final String MATERAIL_LIST_MUST_BE_CONTAIN_15_CHARACTERS="the.material.list.code.should.contain.15.characters";
	public static final String NO_BOM_INFORMATION_IS_FOUND="no.bom.information.is.found";
	public static final String MAXIMUM_OF_BATCH_NUMBERS_ARE_ALLOWED="maximum.of.%d.batch.numbers.are.allowed";
	public static final String OPERATE_SUCCESS="operate.success";
	public static final String THE_BATCH_NUMBERS_NOT_BELONGS_TO_THE_SAME_BOM="the.batch.numbers.do.not.belong.to.the.same.bom";
	public static final String PRODPLANID_DOES_NOT_EXISTS="prodplanid.does.not.exist";
	public static final String PARAMS_CAN_NOT_EMPTY="params.can.not.empty";
	public static final String PARAMS_ERROR="params.error";
	public static final String PLEASE_ENTER_A_CORRECT_PARAMETER_AND_A_VALID_NUMBER="please.enter.a.correct.parameter.and.a.valid.number";
	public static final String THE_NUMBER_OF_COMPONENTS_TO_BE_QUERIED_SERO="the.number.of.components.to.be.queried.is.zero";
	public static final String QUERYING_THE_SUBBOARD_BOUND_TO_A_COMPONENT_PLAN_NUMBER="querying.the.subboard.bound.to.a.component.plan.number.is.0";
	public static final String THIS_BARCODE_HAS_TECHNICAL_MODIFICATION_REQUIREMENTS="this.barcode.has.technical.modification.requirements";
	public static final String QUERING_THE_SUBOARD_BOUND_TO_A_COMPONENT_PLAN_INFO_IS_EMPTY="querying.the.subBoard.bound.to.a.component.plan.info.is.empty";
	public static final String THE_NUMBER_OF_COMPONENTS_QUERID_IS_NULL="the.number.of.components.queried.is.null";
	public static final String INCORRECT_INPUT_INFORMATION="incorrect.input.information";
	public static final String SUBBOARD="subboard";
	public static final String COLLECT_MATETIALS_FIRST="collect.materials.first";
	public static final String FAILED_TO_OBTAIN_BATCH_INFORMATION="failed.to.obtain.batch.information";
	public static final String THE_BATCH_INFORMATION_TO_BE_QUERIED_IS_NULL="the.batch.information.to.be.queried.is.null";
	public static final String ID_IS_EMPTY="id.is.empty";
	public static final String PLEASE_ENTER_THE_CORRECT_ID_NO="please.enter.the.correct.id.no";
	public static final String NETER_THE_CORRECT_LEGAL_NUMBER_ID="please.enter.the.correct.legal.number.id.([1-5])";
	public static final String INPUT_PARAMS_IS_ILLEGAL="input.params.is.illegal";
	public static final String FAILED_TO_OBTAIN_THE_WHITELIST_DATA="failed.to.obtain.the.whitelist.data";
	public static final String FAILED_TO_OBTAIN_THE_SOLDERING_SCAN_DATA="failed.to.obtain.the.soldering.scan.data";
	public static final String REALTIONSHIPS_NOT_SCANNED="the.relationships.between.mother.boards.and.modules.are.not.scanned";
	public static final String FAILED_TO_SCANNING_NODE="failed.to.get.the.bimu.of.the.mount.welding.management.control.node.or.the.current.scanning.node";
	public static final String NODE_IS_NULL="obtain.that.the.bimu.corresponding.to.the.welded.pipe.control.node.is.null";
	public static final String FAILED_TO_CONTROL_INFORMATION="failed.to.obtain.the.soldering.scan.management.and.control.information";
	public static final String THE_BATCH_HAS_BEEN_LOCKED_ON_THE_SCAN_NODE="the.batch.has.been.locked.on.the.scan.node";
	public static final String THERE_IS_NO_MATERAIL_RECEIVING_RECORD="there.is.no.material.receiving.record";
	public static final String CURRENT_NODE_IS_EMPTY="obtain.that.the.bimu.corresponding.to.the.current.node.is.null";
	public static final String THE_RESULT_IS_EMPTY="the.result.is.empty";
	public static final String THE_IMU_IS_CONTROLED="the.imu.is.controled";
	public static final String SUBMITTING_ERROR="synchronizing.and.submitting.tables.and.result.tables.to.the.boardonlinezq";
	public static final String CURRENT_TIME="current.time";
	public static final String EXCEL_FORMAT="the.file.format.is.wrong.please.use.excel.to.import.it";
	public static final String QUERY_SUCCESS="query.success";
	public static final String QUERY_FAILED="query.failed";
	public static final String DOWNLOAD_ADDRESS="download.address";
	public static final String ADRESS_MAY_BE="address.may.be";
	public static final String AUTOMATIC_DELETE="automatic.delete";
	public static final String CLICK_DOWNLOAD_ADDRESS="click.download.address";
	public static final String STEPTOP_BOX_DELIVERY_INFORMATION_QUERY="settop.box.delivery.information.query";
	public static final String WHSEID_CAN_NOT_BE_EMPTY="whseid.can.not.be.empty";
	public static final String INVOKE_SUCCESS="invoke.success";
	public static final String UPDATA_SUCCESS="updata.success";
	public static final String BILLNUMBER_ISEMPTY="billnumber.isEmpty";
	public static final String DEVICE_SITE_CODE_IS_EMPTY="device.site.code.isEmpty";
	public static final String CALCULATE_THE_NUMBER_OF_BOARD_SCANNING_RECORDS="calculating.the.number.of.board.scanning.records";
	public static final String THE_SCHEDULED_CALCULATION_OF_BOARDS_AND_BATCH_MATERAIL="the.scheduled.calculation.of.boards.and.batch.material.preparation.and.production.cycle.invoking.are.abnormal";
	public static final String THE_BOARD_AND_BATCH_MATERIAL_PREPARATION="the.board.and.batch.material.preparation.and.production.cycle.are.being.calculated.and.this.step.is.skipped";
	public static final String ORGNIIZATON_635="the.635.organization.cannot.generate.the.box.number";
	public static final String BOXES_NUMBER_CAN_NOT_LESS_THAN_1="the.number.of.boxes.cannot.be.less.than.1";
	public static final String DELETE_SUCCESSFULLY="the.data.in.the.table.is.deleted.successfully.and.the.data.is.inserted.successfully";
	public static final String SERVICE_IS_ABNORMAL="the.service.is.abnormal";
	public static final String ENTER_THE_MATERIAL_REQUISITION_FORM="please.enter.the.material.requisition.form";
	public static final String EXCEDDS_THE_UPPER_LIMIT="the.length.of.the.requisition.order.exceeds.the.upper.limit";
	public static final String LARGER_THAN_MAXIMUM="the.number.of.current.forward.lines.is.larger.than.the.maximum.number.of.data.lines";
	public static final String NOT_A_POSITIVE_INTEGER="the.current.page.or.row.number.is.not.a.positive.integer";
	public static final String WAREHOUSE_IS_WRONG="the.warehouse.is.wrong.and.only.warehouses.*******.10.13.16.17.and.18.have.board.data";
	public static final String THE_BATCH_CONTAIN_SEVEN_DIGITS="the.batch.can.contain.only.seven.digits";
	public static final String ENTER_THE_BATCH_NUMBER="enter.the.batch.number";
	public static final String CORRECT_BATCH_NUMBER="please.enter.the.correct.batch.number";
	public static final String LENGTH_EXCEEDS_100="the.length.of.the.batch.number.exceeds.100";
	public static final String TASK_NO_IS_NOT_EXISTS="task.no.is.not.exists";
	public static final String INFO_EDIT_SUCCESS="info.edit.success";
	public static final String THE_EXCEL_BEING_GENERATED="the.excel.is.being.generated.please.notice.the.mail";
	public static final String TASK_NO_IS_EMPTY="task.no.is.empty";
	public static final String DATA_INSERT_SUCCESSFULLY="data.insert.successfully";
	public static final String DATA_EXISTED="data.is.existed";
	public static final String BOMCODE_IS_NOT_EMPTY="bomcode.is.not.empty";
	public static final String BIND_SUCCESS="bind.success";
	public static final String BIND_FAILED="bind.failed";
	public static final String INSERT="insert";
	public static final String BARCODE_DATA_SUCCESS="barcode.data.success";
	public static final String BARCODE_DATA_FAILED ="barcode.data.failed";
	public static final String DATA_EMPTY = "select.data.err";

	public static final String SYS_GET_NULL = "sys.get.Null";
	public static final String TO_WMS_ERROR = "to.wms.error";
	public static final String POST_INFOR_ERROR = "post.infor.error";
	public static final String SAVE_INFOR_ERROR = "save.infor.error";
	public static final String POST_HIS_ERROR = "post.his.error";


	public static final String TASK_TO_BE_QUERIED_MESSAGE = "The.query.result.does.not.meet.the.requirements";
	public static final String IMPUT_PARAM_IS_NULL = "Query.parameters.are.not.set";
	public static final String QUERY_CONDITION_EMPTY = "query.condition.empty";
	public static final String ERP_SUBINVENTORY_CODE_EMPTY = "erp.subinventory.code.empty";
	public static final String ERP_LOCATOR_ID_EMPTY = "erp.locator.id.empty";
	public static final String TRANSACTION_DATE_EMPTY = "transaction.date.empty";
	public static final String TRANSACTION_TYPE_EMPTY = "transaction.type.empty";
	public static final String LOOK_UP_TYPE_SERVICE_ERROR = "look.up.type.service.error";
	public static final String CONTRACT_NUMBER_ENTITY_NUMBER = "more.than.contractNumber.no.or.entityNumber.no";
	public static final String CONTRACT_NUMBER_PRODUCT_TYPE = "The.number.of.contract.numbers.should.between.zero.and.ten";
	public static final String SYSTEM_ABNORMAL_UPLOAD_AGAIN = "The.system.is.abnormal.please.upload.again";
	public static final String UPLODE_FAILED_BOXNUM_IS_NOT_EXIST = "The.upload.failed.the.boxNum.does.not.exist";
	public static final String UPLODE_FAILED_CHECK_THE_LIMIT_WEIGHT = "The.upload.failed.check.the.limit.weight";
	public static final String NO_PARAMETERS_WERE_ENTERED="No.parameters.were.entered";
	public static final String INPUT_NUMBER_IS_NOT_EXISTS="input.number.is.not.exists";
	public static final String NOT_BELONGS_TO_THIS_TASKNO="input.not.belongs.to.this.taskno";
	public static final String BOX_HAS_BEEN_SCANNED="this.box.has.been.scaned";
	public static final String THE_NUMBER_EXCEED_ALLOWED_SCANED_NUMBER="the.number.exceed.can.be.scaned.number";
	public static final String ITEM_INFO_IS_NOT_EXISTS="item.info.is.not.exists";
	public static final String PALLET_INFO_IS_NOT_EXISTS="pallet.info.is.not.exists";
	public static final String ITEM_INFO_NOT_EXISTTS_IN_ITEM_LIST="item.info.not.exists.in.item.list";
	public static final String PARAM_NO_WERE_EMPTY = "paramNo.were.empty";
	public static final String PLEASE_INOUT_ONE_CONDITION = "please.input.one.condition";
	public static final String GET_NULL_STENCIL_LIST ="get.no.stencil.list.to.be.sychronizationed";
	public static final String GET_ITEM_ID_NULL ="get.item.id.null";

	public static final String GET_WAREHOUSD_NULL ="get.warehousd.null";

	public static final String REQUEST_PARAM_ERRO ="request.param.is.error";
	public static final String URL_ERROR ="request.path.is.null.or.url.is.error";
	public static final String INPUT_BILL_ID="find.exception.please.input.bill.id.again";
	public static final String BILL_ID_IS_NULL="bill.id.is.null.or.not.exieit.please.input.apain";

	public static final String BILLID_BILLNO_IS_NULL="billid.billno.is.null.or.not.exieit.please.input.apain";
	public static final String BILL_INFO_IS_NULL="from.bill.id.can.not.find.box.info";
	public static final String SUCCESS_GET_BOX_INFO="success.get.box.info";
	public static final String TASKLIST_IS_NULL="tasklist.is.null";
	public static final String UPDATE_FLOW_STATUS_ERROR="update.flow.status.error";
	public static final String SYS_NOT_SET="sys.not.set";
	public static final String GET_FLOW_QTY_ERROR="get.flow.qty.error";



	public static final String NO_MORE_THAN_THREE_CONTACT="Each.input.cannot.exceed.three.contract.numbers";

	public static final String NO_MORE_THEN_HUNDRED_BOX="Each.input.cannot.exceed.hundred.box.numbers";

	public static final String NO_DATA_FIND_FROM_SUBBAROCE="find.fail.because.input.subbarcode.is.null";
	public static final String NO_DATA_FIND_SUBBAROCE_TOO_LONG="find.fail.because.input.subbarcode.is.more.than.200";
	public static final String SUCCESS_FIND_BARCODE_DATA="data.success.find";
	public static final String SUCCESS_OPERATION_BUT_NO_FIND_DATA="data.find.operation.is.ok.but.not.find.data";
	public static final String INPUT_DATA_TYPE_IS_ERROR="find.fail.because.input.data.type.is.not.C";
	public static final String INPUT_DATA_SHOULD_BE_ENGLISH_CHAR="find.fail.because.input.data.should.be.english";
	public static final String INPUT_DATA_IS_TOO_GIG="no.more.than.200.barcode.of.production.material.can.be.input.each.time";
    public static final String INPUT_ENTITYNAME_IS_NULL_PLEASE_CHECK_ENTITYNAME="input.entityname.is.null.please.check.entityname";
	public static final String INPUT_PALLTEDESC_IS_NULL_PLEASE_CHECK_PALLTEDESC="input.palltedesc.is.null.please.check.palltedesc";
	public static final String INPUT_PRODUCTDESC_IS_NULL_PLEASE_CHECK_PRODUCTDESC="input.productdesc.is.null.please.check.productdesc";
	public static final String INPUT_BOXTYPE_IS_NULL_PLEASE_CHECK_BOXTYPE="input.boxtype.is.null.please.check.boxtype";
	public static final String INPUT_STACKNUM_IS_NULL_PLEASE_CHECK_STACKNUM="input.stacknum.is.null.please.check.stacknum";
	public static final String INPUT_CREATEBY_IS_NULL_PLEASE_CHECK_CREATEBY="input.createby.is.null.please.check.createby";
	public static final String INPUT_ORGANIZATIONID_IS_NULL_PLEASE_CHECK_ORGANIZATIONID="input.organizationid.is.null.please.check.organizationid";
	public static final String BILLNO_AND_PALLETNO_INPUT_ONLY_ONE ="billno.and.palletno.input.only.one";
	public static final String BILLNO_NO_PALLET_NO_GET_BILL_DETAIL_INFO="billno.no.pallet.no.get.bill.detail.info";
	public static final String PALLET_EXISTS_NO_SCAN_END_BILL="pallet.exists.no.scan.end.bill";
	public static final String INPUT_PALLTEDESC_NUMBER_IS_OVER_TWO_HUNDRED="input.palltedesc.number.is.over.two.hundred";
	public static final String INPUT_ENTITYNAME_NOT_FIND_PLEASE_CHECK_ENTITYNAME="input.entityname.not.find.please.check.entityname";
	public static final String BOXTYPE_NOT_FIND_WIDTH_AND_LENGTH_PLEASE_CHECK="boxtype.not.find.width.and.length.please.check";
	public static final String PALLETID_NOT_FIND_PLEASE_CHECK="palletid.not.find.please.check";
	public static final String PALLETNO_NOT_FIND_PLEASE_CHECK="palletno.not.find.please.check";
	public static final String SAVE_PALLET_ERROR_PLEASE_CHECK="save.pallet.error.please.check";
	public static final String SAVE_PALLET_SUCCESS="save.pallet.seccess";
	public static final String INPUT_CREATEBY_NOT_EIGHT_PLEASE_CHECK_CREATEBY="input.createby.not.eight.please.cheack";

    public static final String OPERATION_SUCCESS="operation.success";
	public static final String ENTITYNAME_OR_MATERIALCODE_LESS_ONE="entityname.or.materialcode.at.least.one";
	public static final String BARCODES_CAN_NOT_GT_50="barcodes.can.not.gt.50";
	public static final String INPUT_PARAM_CAN_NOT_FIND_DATA="input.param.can.not.find.data";
	public static final String CAN_NOT_FIND_ENTITY_OR_SITE_INFOR="can.not.find.entity.or.site.infor";
	public static final String PALLET_INFORMATION_IS_NO="pallet.information.is.no";
	public static final String PALLET_INFORMATION_IS_NOT_SCANED="pallet.information.is.not.scaned";
	public static final String PALLET_HEIGHT_IS_MORE_THAN_ZERO="the.height.of.pallet.must.be.more.than.zero";
	public static final String PALLET_HEIGHT_IS_NO_MORE_THREE_THOUSAND="the.height.of.pallet.is.no.more.than.three.thousand";
	public static final String ONLY_TWO_DECIMAL_PLACES_ARE_ALLOWED_FOR="only.two.decimal.places.are.allowed.for.palletizing.height";
    public static final String THE_TOTAL_WEIGHT_IS_MORE_THAN_ZERO="the.total.weight.of.pallet.must.be.more.than.zero";
	public static final String PALLET_HEIGHT_IS_NO_MORE_NINE="The.maximum.total.weight.of.pallet.cannot.exceed.9999.9";
	public static final String ONLY_ONE_DECIMAL_PLACES_ARE_ALLOWED_FOR="only.one.decimal.place.is.allowed.for.the.total.weight.of.pallet";
	public static final String PALLET_IS_NOT_EMPTY="pallet.and.height.and.weight.and.lastUpdateby.are.not.empty";
	public static final String PALLET_IS="pallet.is";
	public static final String EMPLEE_NO="job.number.does.not.exist.please.re-enter";

	public static final String INPUT_PROUCDTESC_NOT_FIND_PLEASE_CHECK_PROUCDTESC="input.productdesc.not.find.please.check.productdesc";

	public static final String BILLNO_ALREADER_BIND_PALLETNO="billno.already.bind.palletno";
	public static final String NO_FIND_PALLET_INFO="not.find.pallet.info";
	public static final String FAILED_TO_SAVE_SCAN_INFO="failed.to.save.scan.info";
	public static final String FAILED_TO_UPDATE_PALLET_INFO="failed.to.update.scan.info";
	public static final String PALLET_MFG_SITE_ADDRESS_DIFF="pallet.mfg.site.address.different";
	public static final String VALIDATE_NUM_ERR="validate.number.err";
	public static final String BOX_WEIGHT_IS_ZORO="box.weight.is.zero";
	public static final String BOX_AND_PALLET_ENTITYNAME_IS_DIFF="box.and.pallet.entityname.is.diff";
	public static final String NO_FIND_PALLET_INFO_PLEASE_CHECK="not.find.pallet.info.please.check";
	public static final String FILL_EMPTY_BOX_BILL_COUNT_ERR="fill.empty.box.bill.count.err";
	public static final String STACK_COUNT_ERR="stack.count.err";
	public static final String PALLET_END_FLAG_IS_YES="pallet.end.flag.is.yes";
	public static final String PALLET_ALREADY_SCANED="pallet.had.already.scaned";
	public static final String PALLET_ALREADY_SCANED_IN_CURRENT="pallet.had.been.scan.in.current";
	public static final String REEL_BOX_COUNT_ERR="real.box.count.err";
	public static final String BOX_BILL_COUNT_ERR="box.bill.count.err";
	public static final String PALLET_NOT_EXISTS="pallet.number.not.exists";
	public static final String PALLET_SITE_DIFF_WITH_BOX_BILL_SITE="pallet.site.diff.with.box.bill.site";
	public static final String BILL_BOX_IS_NULL="bill.box.is.null";
	public static final String PALLET_IS_EMPTY="pallet.is.empty";
	public static final String ORGNIIZATON_IS_EMPTY="oranization.is.empty";
	public static final String SCANBY_IS_EMPTY="scanby.is.empty";
	public static final String SCANBY_IS_EMPTY_GT20="scanby.is.empty.gt20";
	public static final String SCANDATE_IS_EMPTY ="scandate.is.empty";
	public static final String POSITION_IS_EMPTY ="position.is.empty";
	public static final String ENTRY_BILL_DETAILIDS_NOT_NULL="entry.bill.detailids.not.null";

	public static final String UPDATE_ROWS_IS_ZERO="update.rows.is.zero";
	public static final String ENTRY_BILL_STATUS_NOT_NULL="entry.bill.status.not.null";
	public static final String ENTRY_BILL_RETURN_REASON_GT200="entry.bill.return.reason.gt200";
 	public static final String ENTRY_BILL_POSTBY_GT20="entry.bill.postby.gt20";
	public static final String ENTRY_BILL_SUBMITBY_GT20="entry.bill.submitby.gt20";
	public static final String ENTRY_BILL_RECEIVEBY_GT20="entry.bill.receiveby.gt20";
	public static final String ENTRY_BILL_RETURNBY_GT20="entry.bill.returnby.gt20";
	public static final String ENTRY_BILL_NOT_NULL="entry.bill.not.null";
	public static final String STOCKNO_NOT_NULL="stockno.not.null";
	public static final String BILLNO_OR_PALLETNO_NOT_NULL="billno.or.palletno.not.null";
	public static final String SCANBY_IS_ERR="scanby.is.err";
	public static final String PALLET_ID_ERR="pallet.id.is.err";

	public static final String WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX="wis.id.list.is.empty.or.exceed.max";
	public static final String SKU_LIST_IS_EMPTY_OR_EXCEED_MAX="sku.list.is.empty.or.exceed.max";
	public static final String WIS_ID_CAN_ONLY_CONTAINS_NUMBER_LETTER_UNDERLINE="wis.id.can.only.contains.number.letter.underline";


	public static final String CONTRACT_IS_NULL="contract.is.null";
	public static final String ENTITY_IS_NULL="entity.is.null";
	public static final String BARCODE_IS_EMPTY="barcode.is.null";
	public static final String ITEMCODE_IS_EMPTY="itemcode.is.null";
	public static final String BOXNO_IS_EMPTY="boxno.is.null";
	public static final String UPDATEMAN_IS_EMPTY="updateman.is.null";
	public static final String INPUT_PARAM_IS_BATCH="input.param.is.batch";

public static final String PALLET_ITEMNO_NOT_SAM_TO_INPUT_ITEMNO="pallet.itemNo.not.same.to.input.itemNo";

	public static final String JOB_NUMBER_NO_EXITS="Job.number.does.not.exist";
	public static final String ANY_THING_NO="any.thing.no";
	public static final String TASK_NO="task.no";
	public static final String NO_SITE="no.site";
	public static final String NO_MIFG_CONFIG_SITE="there.are.no.materials.configured.for.this.site";
	public static final String ENTITY_NO_MATCH_EN="task.number.does.not.match.with.environmental.attributes";
	public static final String NO_BARCODE_INFORMATION="barcode.information.is.not.found";
	public static final String NO_ENTITY_NO_SITE_BIND="the.barcode.has.been.bound";
	public static final String BARCODE_END="The.barcode.under.this.configuration.has.been.scanned";
	public static final String BARCODE_END_END="Barcode.under.configuration.modification.has.been.scanned";
	public static final String BARCODE_NO_CONFIG_INFORMATION="No.corresponding.configuration.information.was.found.for.barcode";
	public static final String CONFIG_ASSEMBLE="The.number.of.scanned.bar.code.is.greater.than.the.number.of.configured.bar.code";

	public static final String CONFIG_ASSEMBLE_SS="The.main.bar.code.is.controlled.by.technical.transformation";
	public static final String ERROR_INSERT_MAIN="Insert.main.bar.code.to.report.error";
    public static final String SUCCESS_GOOD="Operation.successful";
	
	
	public static final String NO_FIND_BOX_INFO="no.find.box.info";
	public static final String NO_BOM_INFORMATION_FROM_BOXINFO="no.bom.information.from.boxinfo";
	public static final String TEMPLATE_INFO_IS_NULL="template.info.is.null";
	public static final String BOM_NOT_LOADED="bom.not.loaded";
	public static final String BOX_IS_VIRTURE="box.is.virture";
	public static final String NO_CBOM_INFORMATION_FROM_BOXINFO="no.cbom.information.from.boxinfo";

	public static final String STOCK_NO_IS_NULL="stock.no.is.null";
	public static final String WAREHOUSEID_IS_NULL="warehouseId.is.null";

    public static final String ENTITY_TRACE_INPUT_NULL="entity.trace.input.is.null";
	public static final String ERP_START_TIME_AND_END_TIME="erp.start.time.and.end.time";
	public static final String PAGESIZE_IS_EMPTY="pagesize.is.empty";


	public static final String DQS_VALITE="dqas.verification.failed";

	public static final String MANY_MATERIAL_INFORMATION="many.mateial.Information";
	
	public static final String NUM_MUST_OVER_ZERO="num.must.over.zero";
	public static final String PAGE_IS_EMPTY="page.is.null";
	public static final String ROWS_IS_EMPTY="rows.is.null";
	public static final String ROWS_IS_OVER_200="rows.is.over.200";
	public static final String ITEM_CODE_NUM_IS_OVER_50="item.code.num.is.over.50";
	public static final String INPUT_DATA_CONTAIN_POINT="input.data.contain.point";
    public static final String MANY_EVERT_PAMATER="Each.parameter.must.be.entered";
	public static final String SOFTTASKNO_STARTIMEENDTIME_ATLEASETONE ="Softtaskno.startimeendtime.atleastone";

	public static final String PER_ORG_CANNOT_REPEAT_IN_8000220="Per.org.cannot.repeat.in.8000220";
	public static final String PAGE_TOO_BIG="Paging.parameter.cannot.be.greater.than.200";

	public static final String PAGE_MANY_QUALITY="The.number.of.entries.per.page.cannot.be.less.than.or.equalled.to.0";
	public static final String PAGE_EVERY_QUALITY="The.requested.page.number.cannot.be.less.than.or.equalled.to.0";
	public static final String TIME_VALIDATE_END_CV="The.start.time.of.query.cannot.be.greater.than.the.end.time";

	public static final String TASK_SOFT_NO_LESS_100="TASK.SOFT.NO.LESS.100";

	public static final String NO_DATA_FAILURE="There.is.no.data.for.pushing.PDM.during.this.period.or.the.push.fails";
	public static final String BOX_TOO_BIG_LIST="Box.list.quantity.is.too.large";

    


	public static final String TIMER_SYNCHRONIZE_FAIL_WMES_HEAD = "timer.synchronize.fail";
	public static final String TIMER_SYNCHRONIZE_FAIL_WMES_LINE = "timer.synchronize.wmes.fail";
	public static final String TIMER_SYNCHRONIZE_FAIL_HEAD = "timer.synchronize.fail.head";
	public static final String TIMER_SYNCHRONIZE_FAIL_LINE = "timer.synchronize.fail.line";
	public static final String ORGNIZATION_ID="Organization.Id.must.be.entered";
	public static final String SUB_STRING_BARCODE="Sub.bar.codes.of";
	public static final String TECHNO_TRANS="are.controlled.by.technological.transformation";
	public static final String MAIN_BARCODE="main.barcode.of";
	public static final String BARCODE_NEED_ENTERED="Material.barcode.must.be.entered";
	public static final String FORME_NAME_DT="The.function.module.must.be.entered";

	public static final String PAGE_TOO_PIGE_DATA="The.page.is.too.big";
	public static final String QUERY_PARAM_IS_EMPTY="query.param.is.empty";
	public static final String PALLET_NO_OVER_50="pallet.number.is.over.50";

	public static final String TASK_LIST_PARAMATER_EMPTY="query.task.list.is.empty";
	public static final String TASK_LIST_TOO_BIG="query.task.is.no.more.than.200";
	public static final String THE_BARCODE_IS_LARGER="The.barcode.is.larger.than.five.layers.and.cannot.be.bound";
	public static final String THE_ITEM_CODE_REPLACED="Same.code.same.material.code.with.substitution.relationship";
	public static final String MAIN_SUB_ITEM_CODE="The.cannot.be.bound.hierarchically.as.a.primary.sub.relationship";
	public static final String THE_BARCODE_BIND="This.barcode.has.been.bound.to";
	public static final String NO_BINDING_IS_ALLOWED="No.binding.is.allowed.on.the.barcode";
	public static final String ENTITY_NAME="entityName";
	public static final String MAIN_CODE_SUB_CODE="This.barcode.is.a.primary.barcode.and.cannot.be.bound.to.its.own.sub.barcode";
	public static final String SUB_BIND_FATHER_CODE="If.this.subassembly.is.bound.to.this.parent.subassembly.the.binding.level.will.be.greater.than.5";
	public static final String SAME_SUB_MAIN_SUB_CODE="Primary.barcode.and.sub.barcode.cannot.be.the.same.barcode";

	public static final String BARCODE_BIND_ENTITY="The.barcode.is.bound.to.the.task.number";
	public static final String BIND_NO_BIND="Next.to.bind.please.unbind.the.barcode";
	public static final String VALIDATE_SUCESS="Verification.succeeded";
	public static final String OTHER_SITE_ID="This.barcode.has.been.bound.in.other.sites.To.bind.please.unbind.the.barcode";
	public static final String SCANED_HAVE="The.barcode.has.been.scanned";
	public static final String THE_SCAN_ITEM_CODE="this.barcode.dt.barcode";
	public static final String IS_ITEM_CODE="itemCode.dt.itemCode";
	public static final String ITEM_ID_DT="messageID.dt.messageID";
	public static final String REPLEACE_ITEM="repleacedID.dt.repleacedID";

	public static final String INPUTE_DIC_NULL_NOT="diction.is.null";
	public static final String SCAN_NO_END ="scan.no.end";

	public static final String BILLNO_AND_ORGID_CANNOT_EMPTY ="bill.and.orgid.cannot.empty";
	public static final String NO_BILL_INFO="no.bill.info";
	public static final String TOKEN_VALIDATE="token.is.not.null";
	public static final String SESSIONID_IS_NOT_NULL ="sessionid.is.not.null";

	public static final String NO_TASKNAME="no.taskname";
	public static final String EMPNO_VALIDATE="Abnormal.verification.of.job.number";

	public static final String BOX_LIST_LARGE="The.maximum.value.of.the.box.list.cannot.exceed";

	public static final String PAGE_NO_MORE_THAN="The.page.number.is.no.more.then";

	public static final String PAGE_NO_EMPTY_NO="The.page.or.The.CurrentPage.is.no.negative";

	public static final String PARAM_EMPTY="the.param.is.empty";

	public static final String BOX_EMPTY_PALLET_EMPTY="the.box.is.empty";

	public static final String BOX_EMPTY_PALLET_NOT_EMPTY="the.box.is.not.empty";
	public static final String ENTITY_IS_EMPTY="the.entity.is.not.exist";

	public static final String DT_SUB_INVCODE="the.SubInvCode.is.not.exist";
	public static final String BOX_NUMBER="boxnumber";
	public static final String AUAX_STIMULATE_SUMMIT="auax.stimulate.summit";
	public static final String VIRTUAL_BOX_NUMBER="virtual.box.number";
	public static final String MAP_BOX_PALLET="map.box.pallet";
	public static final String ENTRY_WARE_HOUSE="entry.ware.house";
	public static final String PALLET_SCAN_OVER="Pallet.not.canned.end";

	public static final String NO_TERMAIL_TASK_IS_MUST_APPROVAL_BY_FACTORY="No.termail.task.is.must.approval.by.factory";

	public static final String BILL_STATUS_NOT_IS_SUBMITTED ="Bill.status.not.is.submitted";

	public static final String TASKNO_IS_NOT_MOVE_IN_ERP="Taskno.is.not.move.in.erp";

	public static final String TASKNO_IS_NOT_EXISTS_IN_ERP="Taskno.is.not.exists.in.erp";

	public static final String TASKNO_STATUS_ISNOT_PACK="Taskno.status.isnot.pack";
	public static final String BOX_UNSCANED_OVER="not.canned.end";

	public static final String NOT_FOUND_21_CA="The.sub.barcode.cannot.be.a.barcode.starting.with.21CA";

	public static final String WHOLE_FOUR_ITEM_CODE="Under.the.whole.machine.task.the.sub.barcode.can.only.have.four.layers.of.material.codes";

	public static final String WHOLE_TWO_THREE_ITEM_CODE="Under.the.task.of.the.whole.machine.the.primary.barcode.cannot.be.a.two.layer.and.three.layer.material.code";

	public static final String BOX_NO_ITEM_NO_SCANED="Box.no.item.no.scaned";
	public static final String PALLET_NO_BOX_DT="There.is.no.case.number.on.the.pallet.number";

	public static final String PALLET_NO_SCAN_END ="pallent.no.scan.end";

	public static final String EMPTY_BOX_ERROR="do.not.enter.empty.box";

	public static final String BOX_PALLET_NO_EXIST="box.pallet.is.not.exist";
	public static final String BOX_CANNOT_BOX="cannot.input.box";
	public static final String GROSS_NET_WEIGHT="the.gross.and.netweight.is.not.zero";
	public static final String EXISTS_IN_ORDER="exists.in.order";
	public static final String ITEMBARCODE_BETWEEN_0_AND_50="Itembarcode.between.0.and.50";

	public static final String PAGESIZE_BETWEEN_0_AND_500="Pagesize.between.0.and.%d";
	public static final String PRODPLAN_ID_NULL = "prodplan.id.null";
	public static final String THE_MAIN_BARCODE_IS_21CA="The.main.barcode.is.21CA.which.must.be.a.complete.machine.site";

	public static final String CANNOT_BE_GREATER_500 = "query.count.cannot.be.greater.than.500";
	public static final String REDIS_LOCK_FAIL = "redis.lock.fail";
	public static final String SN_MISS_BOARD_ONLINE = "sn.miss.board.online";

	public static final String CUSTOMER_NAME_ISNOT_NULL ="customer.name.isnot.null";
	public static final String SN_NOT_MORE_THAN_500="sn.not.more.than.500";
	public static final String TASKNO_AND_SN_ISNOT_NULL="taskno.and.sn.isnot.null";

	public static final String SUBMIT_TIME_INTERVAL_ERROR = "submit.time.interval.more.than.180.days";
	public static final String VERIFY_TIME_INTERVAL_ERROR = "verify.time.interval.more.than.180.days";
	public static final String QUERY_PARAM_ERROR = "query.param.error";
	//获取redis锁资源失败，请重试
	public static  final String FAILED_TO_GET_REDIS_LOCK="failed.to.get.redis.lock";
	public static final String CALL_SERVICE_ERROR = "call.service.error";

	public static final String FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION = "failed_to_obtain_customer_basic_information";
	public static final String SYS_LOOK_NOT_CONFIG = "sys.look.not.config";
	public static final String ENTITY_NAME_NEED_USER_ADDRESS = "entity.name.need.user.address";
	public static final String FAILED_TO_CALL_MDS_INTERFACE = "failed_to_call_mds_interface";
	public static final String CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED="call.barCode.center.expandQuery.barCode.falied";
	public static final String FAILED_TO_GET_BARCODE_CENTER_URL = "failed_to_get_barcode_center_url";
	public static final String GET_ZSLOG_STATION_ERROR="get.zs.log.station.error";

	public static final String AUTOFLAG_IS_NULL = "autoflag.is.not.null";
	public static final String TASKS_CANNOT_EXCEED_20 = "tasks_cannot_exceed_20";

	public static final String ARCHIVE_EXTRACT_TASK_EXECUTING="archive.extract.task.executing";
	public static final String ARCHIVE_TASK_EXECUTING="archive.task.executing";

	public static final String ARCHIVE_SEMIPROD_NULL = "archive.semiprod.null";
	public static final String ARCHIVE_SEMIPROD_DETAIL_NULL = "archive.semiprod.detail.null";
	public static final String ARCHIVE_PAYABLE_NULL = "archive.payable.null";

	public static final String ARCHIVE_MANUFACTURINGORDER_SERVERERROR = "archive.manufacturingorder.server.error";
	public static final String UNARCHIVE_MANUFACTURINGORDER_SERVERERROR = "unarchive.manufacturingorder.server.error";
	public static final String ARCHIVE_TESTPROCESS_SERVERERROR = "archive.testprocess.server.error";
	public static final String UNARCHIVE_TESTPROCESS_SERVERERROR = "unarchive.testprocess.server.error";

	public static final String B2B_RETURN_CN = "b2b.return.cn";
	public static final String TASK_NO_NOT_EXIST = "task.no.not.exist";
	public static final String TASK_NO_NOT_MORE_THAN_100 = "task.no.not.more.than.100";
	public static final String TYPE_NO_IS_NULL = "type.no.is.null";
	public static final String REGULATION_IS_NULL = "regulation.is.null";
	public static final String MASTER_TYPE_IS_NULL = "master.type.is.null";
	public static final String SMALL_TYPE_IS_NULL = "small.type.is.null";
	public static final String SUB_TYPE_IS_NULL = "sub.type.is.null";
	public static final String IS_ITEM_IS_NULL = "is.item.is.null";
	public static final String CATEGORY_IS_NULL = "category.is.null";
	public static final String DEFECTS_QTY_IS_NULL = "defects.qty.is.null";
	public static final String CONTRACT_AND_TASK_IS_NULL = "contract.and.task.is.null";

	public static final String CHOOSE_NO_IMEI_REPEAT = "choose.no.imei.repeat";
	public static final String CHOOSE_NO_REPEAT = "choose.no.repeat";
	public static final String CHOOSE_NO_EXIST = "choose.no.exist";
	public static final String IMEI_BOX_NOT_FIND = "imei.box.not.find";
	public static final String BOX_INFO_NOT_FIND = "box.info.not.find";
	public static final String BOX_IMEI_ITEM_DIFF = "box.imei.item.diff";
	public static final String BOX_IMEI_SUB_DIFF = "box.imei.sub.diff";
	public static final String BOX_SUBINVENTORY_NOT_FIND = "box.subinventory.not.find";
	public static final String BOX_WAREHOUSE_NOT_IN = "box.warehouse.not.in";
	public static final String IMEI_SCANED = "imei.scaned";

	public static final String ISSUE_DATE_NOT_NULL = "issue.date.not.null";

	public static final String TRAY_CODE_IS_NULL = "tray.code.is.null";

	public static final String OUTER_BOX_LIST_IS_NULL = "outer.box.list.is.null";

	public static final String EN_CODE_LIST_IS_NULL = "en.code.list.is.null";

	public static final String TRAY_CODE_MES_INFO_IS_NULL = "mes.info.is.null";
	public static final String FORWARD_AND_MTP_TEST_UPLOAD_SUCCESS_NOT_EXISTS = "forward.and.mtp.test.upload.success.not.exists";

	public static final String DISCRETE_PARAM_NAME_ERROR = "discrete.param.name.error";
	public static final String EXIST_NOT_PARAM_NAME = "exist.not.param.name";
	public static final String PARAM_LIST_IS_NULL = "param.list.is.null";
	public static final String PARAM_TYPE_IS_INCORRECT = "param.type.is.incorrect";
	public static final String EXIST_NOT_PARAM_TYPE = "exist.not.data.type";
	public static final String PARAM_NAME_REPEATED = "exist.not.param.Repeated";
	public static final String ITEM_CODE_OF_MES_IS_DIFFERENT = "item.code.of.mes.is.different";

	public static final String MES_PACKING_INFO_IS_NULL = "mes.packing.info.is.null";
	public static final String FAILED_TO_SPECIFIED_PS_TASK = "failed_to_specified_ps_task";

	public static final String ITEM_CONTROL_ENTRY_FAIL = "item_control_entry_fail";
}
