/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 *   1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import com.zte.domain.model.authentication.ComSupProduce;
import com.zte.interfaces.authentication.dto.ComSupProduceDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class ComSupProduceAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return ComSupProduceDTO
     **/
    public static ComSupProduceDTO toDTO(ComSupProduce entity) {
        ComSupProduceDTO dto = new ComSupProduceDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return ComSupProduce
     **/
    public static ComSupProduce toEntity(ComSupProduceDTO dto) {
        ComSupProduce entity = new ComSupProduce();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<ComSupProduceDTO>
     **/
    public static java.util.List<ComSupProduceDTO> toComSupProduceDTOList(java.util.List<ComSupProduce> entityList) {
        List<ComSupProduceDTO> dtoList = new ArrayList<ComSupProduceDTO>();
        for (ComSupProduce entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<ComSupProduce>
     **/
    public static java.util.List<ComSupProduce> toComSupProduceList(java.util.List<ComSupProduceDTO> dtoList) {
        List<ComSupProduce> entityList = new ArrayList<ComSupProduce>();
        for (ComSupProduceDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}