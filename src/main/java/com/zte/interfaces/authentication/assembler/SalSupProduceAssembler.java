/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 *   1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import com.zte.domain.model.authentication.SalSupProduce;
import com.zte.interfaces.authentication.dto.SalSupProduceDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class SalSupProduceAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return SalSupProduceDTO
     **/
    public static SalSupProduceDTO toDTO(SalSupProduce entity) {
        SalSupProduceDTO dto = new SalSupProduceDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return SalSupProduce
     **/
    public static SalSupProduce toEntity(SalSupProduceDTO dto) {
        SalSupProduce entity = new SalSupProduce();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<SalSupProduceDTO>
     **/
    public static java.util.List<SalSupProduceDTO> toSalSupProduceDTOList(java.util.List<SalSupProduce> entityList) {
        List<SalSupProduceDTO> dtoList = new ArrayList<SalSupProduceDTO>();
        for (SalSupProduce entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<SalSupProduce>
     **/
    public static java.util.List<SalSupProduce> toSalSupProduceList(java.util.List<SalSupProduceDTO> dtoList) {
        List<SalSupProduce> entityList = new ArrayList<SalSupProduce>();
        for (SalSupProduceDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}