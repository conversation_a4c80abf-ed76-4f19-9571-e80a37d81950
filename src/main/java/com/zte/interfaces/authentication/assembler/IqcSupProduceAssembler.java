/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 *   1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import com.zte.domain.model.authentication.IqcSupProduce;
import com.zte.interfaces.authentication.dto.IqcSupProduceDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class IqcSupProduceAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return IqcSupProduceDTO
     **/
    public static IqcSupProduceDTO toDTO(IqcSupProduce entity) {
        IqcSupProduceDTO dto = new IqcSupProduceDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return IqcSupProduce
     **/
    public static IqcSupProduce toEntity(IqcSupProduceDTO dto) {
        IqcSupProduce entity = new IqcSupProduce();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<IqcSupProduceDTO>
     **/
    public static java.util.List<IqcSupProduceDTO> toIqcSupProduceDTOList(java.util.List<IqcSupProduce> entityList) {
        List<IqcSupProduceDTO> dtoList = new ArrayList<IqcSupProduceDTO>();
        for (IqcSupProduce entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<IqcSupProduce>
     **/
    public static java.util.List<IqcSupProduce> toIqcSupProduceList(java.util.List<IqcSupProduceDTO> dtoList) {
        List<IqcSupProduce> entityList = new ArrayList<IqcSupProduce>();
        for (IqcSupProduceDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}