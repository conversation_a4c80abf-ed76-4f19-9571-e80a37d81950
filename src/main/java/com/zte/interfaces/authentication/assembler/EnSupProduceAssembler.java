/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-03
 * 修改历史 :
 *   1. [2019-04-03] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.zte.domain.model.authentication.EnSupProduce;
import com.zte.interfaces.authentication.dto.EnSupProduceDTO;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class EnSupProduceAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return EnSupProduceDTO
     **/
    public static EnSupProduceDTO toDTO(EnSupProduce entity) {
        EnSupProduceDTO dto = new EnSupProduceDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return EnSupProduce
     **/
    public static EnSupProduce toEntity(EnSupProduceDTO dto) {
        EnSupProduce entity = new EnSupProduce();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<EnSupProduceDTO>
     **/
    public static java.util.List<EnSupProduceDTO> toEnSupProduceDTOList(java.util.List<EnSupProduce> entityList) {
        List<EnSupProduceDTO> dtoList = new ArrayList<EnSupProduceDTO>();
        for (EnSupProduce entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<EnSupProduce>
     **/
    public static java.util.List<EnSupProduce> toEnSupProduceList(java.util.List<EnSupProduceDTO> dtoList) {
        List<EnSupProduce> entityList = new ArrayList<EnSupProduce>();
        for (EnSupProduceDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}