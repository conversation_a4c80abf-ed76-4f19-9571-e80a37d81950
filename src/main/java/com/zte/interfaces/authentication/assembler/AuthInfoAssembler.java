/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-03-21
 * 修改历史 :
 *   1. [2019-03-21] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import com.zte.domain.model.authentication.AuthInfo;
import com.zte.interfaces.authentication.dto.AuthInfoDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class AuthInfoAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return AuthenticationMaintainInfoDTO
     **/
    public static AuthInfoDTO toDTO(AuthInfo entity) {
        AuthInfoDTO dto = new AuthInfoDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return AuthenticationMaintainInfo
     **/
    public static AuthInfo toEntity(AuthInfoDTO dto) {
        AuthInfo entity = new AuthInfo();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<AuthenticationMaintainInfoDTO>
     **/
    public static java.util.List<AuthInfoDTO> toAuthenticationMaintainInfoDTOList(java.util.List<AuthInfo> entityList) {
        List<AuthInfoDTO> dtoList = new ArrayList<AuthInfoDTO>();
        for (AuthInfo entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<AuthenticationMaintainInfo>
     **/
    public static java.util.List<AuthInfo> toAuthenticationMaintainInfoList(java.util.List<AuthInfoDTO> dtoList) {
        List<AuthInfo> entityList = new ArrayList<AuthInfo>();
        for (AuthInfoDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}