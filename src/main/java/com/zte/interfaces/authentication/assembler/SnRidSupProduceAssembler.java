/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 *   1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.assembler;

import com.zte.domain.model.authentication.SnRidSupProduce;
import com.zte.interfaces.authentication.dto.SnRidSupProduceDTO;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class SnRidSupProduceAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return SnRidSupProduceDTO
     **/
    public static SnRidSupProduceDTO toDTO(SnRidSupProduce entity) {
        SnRidSupProduceDTO dto = new SnRidSupProduceDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return SnRidSupProduce
     **/
    public static SnRidSupProduce toEntity(SnRidSupProduceDTO dto) {
        SnRidSupProduce entity = new SnRidSupProduce();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<SnRidSupProduceDTO>
     **/
    public static java.util.List<SnRidSupProduceDTO> toSnRidSupProduceDTOList(java.util.List<SnRidSupProduce> entityList) {
        List<SnRidSupProduceDTO> dtoList = new ArrayList<SnRidSupProduceDTO>();
        for (SnRidSupProduce entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<SnRidSupProduce>
     **/
    public static java.util.List<SnRidSupProduce> toSnRidSupProduceList(java.util.List<SnRidSupProduceDTO> dtoList) {
        List<SnRidSupProduce> entityList = new ArrayList<SnRidSupProduce>();
        for (SnRidSupProduceDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}