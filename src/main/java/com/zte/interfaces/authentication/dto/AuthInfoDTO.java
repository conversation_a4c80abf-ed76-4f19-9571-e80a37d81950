/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-03-21
 * 修改历史 :
 * 1. [2019-03-21] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthInfoDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal factoryId;

	private String inCode;

	private String inName;

	private String publicKey;

	private String privateKey;

	private String symSecretKey;

	private String createBy;

	private Date createDate;

	private String lastUpdateBy;

	private Date lastUpdateDate;

	private String attribute1;

	private String attribute2;

	private String attribute3;

	private String attribute4;

	private String attribute5;

	private String enabledFlag;

	@ApiModelProperty(value = "排序字段(值errorCode等)", hidden = true)
	private String sort;

	@ApiModelProperty(value = "排序方式(默认升序,设为desc时降序)", hidden = true)
	private String order;

	@ApiModelProperty(value = "请求的页码", hidden = true)
	private Long page;

	@ApiModelProperty(value = "每页条数", hidden = true)
	private Long rows;

	@ApiModelProperty(value = "开始行数", hidden = true)
	private Long startRow;

	@ApiModelProperty(value = "结束行数", hidden = true)
	private Long endRow;

	public String getSort() {

		return sort;
	}

	public void setSort(String sort) {

		this.sort = sort;
	}

	public String getOrder() {

		return order;
	}

	public void setOrder(String order) {

		this.order = order;
	}

	public Long getPage() {

		return page;
	}

	public void setPage(Long page) {

		this.page = page;
	}

	public Long getStartRow() {

		return startRow;
	}

	public void setStartRow(Long startRow) {

		this.startRow = startRow;
	}

	public Long getEndRow() {

		return endRow;
	}

	public void setEndRow(Long endRow) {

		this.endRow = endRow;
	}

	public void setInCode(String inCode) {

		this.inCode = inCode;
	}

	public String getInCode() {

		return inCode;
	}

	public void setInName(String inName) {

		this.inName = inName;
	}

	public String getInName() {

		return inName;
	}

	public void setPublicKey(String publicKey) {

		this.publicKey = publicKey;
	}

	public String getPublicKey() {

		return publicKey;
	}

	public void setPrivateKey(String privateKey) {

		this.privateKey = privateKey;
	}

	public String getPrivateKey() {

		return privateKey;
	}

	public void setSymSecretKey(String symSecretKey) {

		this.symSecretKey = symSecretKey;
	}

	public String getSymSecretKey() {

		return symSecretKey;
	}

	public void setCreateBy(String createBy) {

		this.createBy = createBy;
	}

	public String getCreateBy() {

		return createBy;
	}

	public void setCreatTime(Date createDate) {

		this.createDate = createDate;
	}

	public Date getCreatTime() {

		return createDate;
	}

	public void setLastUpdateBy(String lastUpdateBy) {

		this.lastUpdateBy = lastUpdateBy;
	}

	public String getLastUpdateBy() {

		return lastUpdateBy;
	}

	public void setLastUpdateDate(Date lastUpdateDate) {

		this.lastUpdateDate = lastUpdateDate;
	}

	public Date getLastUpdateDate() {

		return lastUpdateDate;
	}

	public void setAttribute1(String attribute1) {

		this.attribute1 = attribute1;
	}

	public String getAttribute1() {

		return attribute1;
	}

	public void setAttribute2(String attribute2) {

		this.attribute2 = attribute2;
	}

	public String getAttribute2() {

		return attribute2;
	}

	public void setAttribute3(String attribute3) {

		this.attribute3 = attribute3;
	}

	public String getAttribute3() {

		return attribute3;
	}

	public void setAttribute4(String attribute4) {

		this.attribute4 = attribute4;
	}

	public String getAttribute4() {

		return attribute4;
	}

	public void setAttribute5(String attribute5) {

		this.attribute5 = attribute5;
	}

	public String getAttribute5() {

		return attribute5;
	}

	public void setEnabledFlag(String enabledFlag) {

		this.enabledFlag = enabledFlag;
	}

	public String getEnabledFlag() {

		return enabledFlag;
	}

	public void setFactoryId(BigDecimal factoryId) {

		this.factoryId = factoryId;
	}

	public BigDecimal getFactoryId() {

		return factoryId;
	}

	public Long getRows() {

		return rows;
	}

	public void setRows(Long rows) {

		this.rows = rows;
	}
}
