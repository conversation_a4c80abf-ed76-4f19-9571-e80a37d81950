package com.zte.interfaces.authentication.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.dto.busmanage.BmShuttleDispatchingDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class OutFactoryTableDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "工单任务结存DTO")
	private EnSupProduceDTO enSupProduceDTO;

	@ApiModelProperty(value = "整机绑定表DTO")
	private ComSupProduceDTO comSupProduceDTO;

	@ApiModelProperty(value = "外协自购物料IQCDTO")
	private IqcSupProduceDTO iqcSupProduceDTO;

	@ApiModelProperty(value = "客供客售转化DTO")
	private SalCSupProduceDTO salCSupProduceDTO;

	@ApiModelProperty(value = "客供客售库存DTO")
	private SalSupProduceDTO salSupProduceDTO;

	@ApiModelProperty(value = "单板SN与ReelIdDTO")
	private SnRidSupProduceDTO snRidSupProduceDTO;

	@ApiModelProperty(value = "行ID号")
	private List<String> recordId;

	@ApiModelProperty(value = "生产单位")
	private String inCode;

	@ApiModelProperty(value = "表上送类型")
	private String outFactoryTable;

	@ApiModelProperty(value = "创建人")
	private String createBy;

	@ApiModelProperty(value = "开始日期", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String startTime;

	@ApiModelProperty(value = "结束日期", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String endTime;

	@ApiModelProperty(value = "排序字段(值errorCode等)", hidden = true)
	private String sort;

	@ApiModelProperty(value = "排序方式(默认升序,设为desc时降序)", hidden = true)
	private String order;

	@ApiModelProperty(value = "请求的页码", hidden = true)
	private Long page;

	@ApiModelProperty(value = "每页条数", hidden = true)
	private Long rows;

	@ApiModelProperty(value = "开始行数", hidden = true)
	private Long startRow;

	@ApiModelProperty(value = "结束行数", hidden = true)
	private Long endRow;

	@ApiModelProperty(value = "工厂ID", hidden = true)
	private BigDecimal factoryId;

	@ApiModelProperty(value = "附加文件", hidden = true)
	private transient MultipartFile file;
    
    //excel校验结果
    private String validResp;
    
    private List<OutFactoryTableDTO> dataList;

	public List<OutFactoryTableDTO> getDataList() {
		return dataList;
	}

	public void setDataList(List<OutFactoryTableDTO> dataList) {
		this.dataList = dataList;
	}

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public MultipartFile getFile() {
		return file;
	}

	public void setFile(MultipartFile file) {
		this.file = file;
	}

	public List<String> getRecordId() {
		return recordId;
	}

	public void setRecordId(List<String> recordId) {
		this.recordId = recordId;
	}

	public String getInCode() {
		return inCode;
	}

	public void setInCode(String inCode) {
		this.inCode = inCode;
	}

	public String getOutFactoryTable() {
		return outFactoryTable;
	}

	public void setOutFactoryTable(String outFactoryTable) {
		this.outFactoryTable = outFactoryTable;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getSort() {
		return sort;
	}

	public void setSort(String sort) {
		this.sort = sort;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public Long getPage() {
		return page;
	}

	public void setPage(Long page) {
		this.page = page;
	}

	public Long getRows() {
		return rows;
	}

	public void setRows(Long rows) {
		this.rows = rows;
	}

	public Long getStartRow() {
		return startRow;
	}

	public void setStartRow(Long startRow) {
		this.startRow = startRow;
	}

	public Long getEndRow() {
		return endRow;
	}

	public void setEndRow(Long endRow) {
		this.endRow = endRow;
	}

	public BigDecimal getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(BigDecimal factoryId) {
		this.factoryId = factoryId;
	}

	public EnSupProduceDTO getEnSupProduceDTO() {
		return enSupProduceDTO;
	}

	public void setEnSupProduceDTO(EnSupProduceDTO enSupProduceDTO) {
		this.enSupProduceDTO = enSupProduceDTO;
	}

	public ComSupProduceDTO getComSupProduceDTO() {
		return comSupProduceDTO;
	}

	public void setComSupProduceDTO(ComSupProduceDTO comSupProduceDTO) {
		this.comSupProduceDTO = comSupProduceDTO;
	}

	public IqcSupProduceDTO getIqcSupProduceDTO() {
		return iqcSupProduceDTO;
	}

	public void setIqcSupProduceDTO(IqcSupProduceDTO iqcSupProduceDTO) {
		this.iqcSupProduceDTO = iqcSupProduceDTO;
	}

	public SalCSupProduceDTO getSalCSupProduceDTO() {
		return salCSupProduceDTO;
	}

	public void setSalCSupProduceDTO(SalCSupProduceDTO salCSupProduceDTO) {
		this.salCSupProduceDTO = salCSupProduceDTO;
	}

	public SalSupProduceDTO getSalSupProduceDTO() {
		return salSupProduceDTO;
	}

	public void setSalSupProduceDTO(SalSupProduceDTO salSupProduceDTO) {
		this.salSupProduceDTO = salSupProduceDTO;
	}

	public SnRidSupProduceDTO getSnRidSupProduceDTO() {
		return snRidSupProduceDTO;
	}

	public void setSnRidSupProduceDTO(SnRidSupProduceDTO snRidSupProduceDTO) {
		this.snRidSupProduceDTO = snRidSupProduceDTO;
	}

}
