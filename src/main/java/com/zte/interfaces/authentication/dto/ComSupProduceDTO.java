/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 * 1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.math.BigDecimal;

import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComSupProduceDTO extends BaseDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "单板SN")
	private String bomSn;

	@ApiModelProperty(value = "中兴PO号")
	private String ztePo;

	@ApiModelProperty(value = "外协工单号")
	private String contractNumber;

	@ApiModelProperty(value = "整机SN/EN/产品SN")
	private String completeSn;

	@ApiModelProperty(value = "MAC")
	private String mac;

	@ApiModelProperty(value = "BOSA条码")
	private String bosaSn;

	@ApiModelProperty(value = "电源适配器条码")
	private String powerCode;

	@ApiModelProperty(value = "遥控器条码")
	private String remoteCode;

	@ApiModelProperty(value = "GPON_SN")
	private String gponSn;

	@ApiModelProperty(value = "D_SN")
	private String dSn;

	@ApiModelProperty(value = "机顶盒STBID")
	private String stbid;

	@ApiModelProperty(value = "设备标识")
	private String equipmrntName;

	@ApiModelProperty(value = "产品代码")
	private String prodItem;

	@ApiModelProperty(value = "设备型号")
	private String prodType;

	@ApiModelProperty(value = "备用字段1")
	private String preserved1;

	@ApiModelProperty(value = "备用字段2")
	private String preserved2;

	@ApiModelProperty(value = "备用字段3")
	private String preserved3;

	@ApiModelProperty(value = "备用字段4")
	private String preserved4;

	@ApiModelProperty(value = "备用字段5")
	private String preserved5;

	@ApiModelProperty(value = "工厂ID")
	private BigDecimal factoryId;

	@ApiModelProperty(value = "行ID号")
	private String recordId;

	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;
	
	@ApiModelProperty(value = "外协工厂ID")
	private BigDecimal inFactoryId;

	@ApiModelProperty(value = "接入方编码")
	private String inCode;
    
    private String validResp; // excel校验结果

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public String getRecordId() {

		return recordId;
	}

	public void setRecordId(String recordId) {

		this.recordId = recordId;
	}

	public String getInFactoryName() {

		return inFactoryName;
	}

	public void setInFactoryName(String inFactoryName) {

		this.inFactoryName = inFactoryName;
	}

	public BigDecimal getInFactoryId() {

		return inFactoryId;
	}

	public void setInFactoryId(BigDecimal inFactoryId) {

		this.inFactoryId = inFactoryId;
	}

	public String getInCode() {

		return inCode;
	}

	public void setInCode(String inCode) {

		this.inCode = inCode;
	}

	public void setFactoryId(BigDecimal factoryId) {

		this.factoryId = factoryId;
	}

	public BigDecimal getFactoryId() {

		return factoryId;
	}

	public void setBomSn(String bomSn) {

		this.bomSn = bomSn;
	}

	public String getBomSn() {

		return bomSn;
	}

	public void setZtePo(String ztePo) {

		this.ztePo = ztePo;
	}

	public String getZtePo() {

		return ztePo;
	}

	public void setContractNumber(String contractNumber) {

		this.contractNumber = contractNumber;
	}

	public String getContractNumber() {

		return contractNumber;
	}

	public void setCompleteSn(String completeSn) {

		this.completeSn = completeSn;
	}

	public String getCompleteSn() {

		return completeSn;
	}

	public void setMac(String mac) {

		this.mac = mac;
	}

	public String getMac() {

		return mac;
	}

	public void setBosaSn(String bosaSn) {

		this.bosaSn = bosaSn;
	}

	public String getBosaSn() {

		return bosaSn;
	}

	public void setPowerCode(String powerCode) {

		this.powerCode = powerCode;
	}

	public String getPowerCode() {

		return powerCode;
	}

	public void setRemoteCode(String remoteCode) {

		this.remoteCode = remoteCode;
	}

	public String getRemoteCode() {

		return remoteCode;
	}

	public void setGponSn(String gponSn) {

		this.gponSn = gponSn;
	}

	public String getGponSn() {

		return gponSn;
	}

	public void setdSn(String dSn) {

		this.dSn = dSn;
	}

	public String getdSn() {

		return dSn;
	}

	public void setStbid(String stbid) {

		this.stbid = stbid;
	}

	public String getStbid() {

		return stbid;
	}

	public void setEquipmrntName(String equipmrntName) {

		this.equipmrntName = equipmrntName;
	}

	public String getEquipmrntName() {

		return equipmrntName;
	}

	public void setProdItem(String prodItem) {

		this.prodItem = prodItem;
	}

	public String getProdItem() {

		return prodItem;
	}

	public void setProdType(String prodType) {

		this.prodType = prodType;
	}

	public String getProdType() {

		return prodType;
	}

	public void setPreserved1(String preserved1) {

		this.preserved1 = preserved1;
	}

	public String getPreserved1() {

		return preserved1;
	}

	public void setPreserved2(String preserved2) {

		this.preserved2 = preserved2;
	}

	public String getPreserved2() {

		return preserved2;
	}

	public void setPreserved3(String preserved3) {

		this.preserved3 = preserved3;
	}

	public String getPreserved3() {

		return preserved3;
	}

	public void setPreserved4(String preserved4) {

		this.preserved4 = preserved4;
	}

	public String getPreserved4() {

		return preserved4;
	}

	public void setPreserved5(String preserved5) {

		this.preserved5 = preserved5;
	}

	public String getPreserved5() {

		return preserved5;
	}
}
