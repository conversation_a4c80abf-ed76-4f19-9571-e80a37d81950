/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 * 1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalSupProduceDTO extends BaseDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "外协工ID")
	private BigDecimal factoryId;

	@ApiModelProperty(value = "客供客售单号")
	private String salesNumber;

	@ApiModelProperty(value = "库存截止时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date deadlineTime;

	@ApiModelProperty(value = "行ID号")
	private String recordId;

	@ApiModelProperty(value = "物料代码")
	private String itemNo;

	@ApiModelProperty(value = "物料条码")
	private String itemCode;

	@ApiModelProperty(value = "ZTE_Reel_ID")
	private String zteReelId;

	@ApiModelProperty(value = "外协Reel_ID")
	private String outReelId;

	@ApiModelProperty(value = "库存数量")
	private BigDecimal inventoryQty;

	@ApiModelProperty(value = "备用字段1")
	private String preserved1;

	@ApiModelProperty(value = "备用字段2")
	private String preserved2;

	@ApiModelProperty(value = "备用字段3")
	private String preserved3;

	@ApiModelProperty(value = "备用字段 4")
	private String preserved4;

	@ApiModelProperty(value = "备用字段5")
	private String preserved5;

	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;

	@ApiModelProperty(value = "外协工厂id")
	private BigDecimal inFactoryId;

	@ApiModelProperty(value = "接入方编码")
	private String inCode;
    
    private String validResp; // excel校验结果

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public BigDecimal getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(BigDecimal factoryId) {
		this.factoryId = factoryId;
	}

	public String getSalesNumber() {
		return salesNumber;
	}

	public void setSalesNumber(String salesNumber) {
		this.salesNumber = salesNumber;
	}

	public Date getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(Date deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getRecordId() {
		return recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getZteReelId() {
		return zteReelId;
	}

	public void setZteReelId(String zteReelId) {
		this.zteReelId = zteReelId;
	}

	public String getOutReelId() {
		return outReelId;
	}

	public void setOutReelId(String outReelId) {
		this.outReelId = outReelId;
	}

	public BigDecimal getInventoryQty() {
		return inventoryQty;
	}

	public void setInventoryQty(BigDecimal inventoryQty) {
		this.inventoryQty = inventoryQty;
	}

	public String getPreserved1() {
		return preserved1;
	}

	public void setPreserved1(String preserved1) {
		this.preserved1 = preserved1;
	}

	public String getPreserved2() {
		return preserved2;
	}

	public void setPreserved2(String preserved2) {
		this.preserved2 = preserved2;
	}

	public String getPreserved3() {
		return preserved3;
	}

	public void setPreserved3(String preserved3) {
		this.preserved3 = preserved3;
	}

	public String getPreserved4() {
		return preserved4;
	}

	public void setPreserved4(String preserved4) {
		this.preserved4 = preserved4;
	}

	public String getPreserved5() {
		return preserved5;
	}

	public void setPreserved5(String preserved5) {
		this.preserved5 = preserved5;
	}

	public String getInFactoryName() {
		return inFactoryName;
	}

	public void setInFactoryName(String inFactoryName) {
		this.inFactoryName = inFactoryName;
	}

	public BigDecimal getInFactoryId() {
		return inFactoryId;
	}

	public void setInFactoryId(BigDecimal inFactoryId) {
		this.inFactoryId = inFactoryId;
	}

	public String getInCode() {
		return inCode;
	}

	public void setInCode(String inCode) {
		this.inCode = inCode;
	}

}
