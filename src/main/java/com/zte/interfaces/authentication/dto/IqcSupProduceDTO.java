/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 * 1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class IqcSupProduceDTO extends BaseDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "供应商")
	private String supplier;

	@ApiModelProperty(value = "物料名称")
	private String imcomeMaterial;

	@ApiModelProperty(value = "物料型号")
	private String materialModel;

	@ApiModelProperty(value = "物料PO号")
	private String materialPo;

	@ApiModelProperty(value = "本批数量")
	private BigDecimal batchQty;

	@ApiModelProperty(value = "抽检数量")
	private BigDecimal samplingQty;

	@ApiModelProperty(value = "来料日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date materialsTime;

	@ApiModelProperty(value = "检测日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date inspectionTime;

	@ApiModelProperty(value = "检测单号")
	private String inspectionNumber;

	@ApiModelProperty(value = "文件封号")
	private String gycode;

	@ApiModelProperty(value = "封样编号")
	private String sealNumber;

	@ApiModelProperty(value = "抽样方式")
	private String samplMethod;

	@ApiModelProperty(value = "性能参数")
	private String performanceParameter;

	@ApiModelProperty(value = "检测结果")
	private String detectionResult;

	@ApiModelProperty(value = "备用字段1")
	private String preserved1;

	@ApiModelProperty(value = "备用字段2")
	private String preserved2;

	@ApiModelProperty(value = "备用字段3")
	private String preserved3;

	@ApiModelProperty(value = "备用字段4")
	private String preserved4;

	@ApiModelProperty(value = "备用字段5")
	private String preserved5;

	@ApiModelProperty(value = "工厂ID")
	private BigDecimal factoryId;

	@ApiModelProperty(value = "行ID号")
	private String recordId;

	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;

	@ApiModelProperty(value = "外协工厂ID")
	private BigDecimal inFactoryId;

	@ApiModelProperty(value = "接入方编码")
	private String inCode;
    
    private String validResp; // excel校验结果

	public String getDetectionResult() {
		return detectionResult;
	}

	public void setDetectionResult(String detectionResult) {
		this.detectionResult = detectionResult;
	}

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public String getRecordId() {

		return recordId;
	}

	public void setRecordId(String recordId) {

		this.recordId = recordId;
	}

	public String getInFactoryName() {

		return inFactoryName;
	}

	public void setInFactoryName(String inFactoryName) {

		this.inFactoryName = inFactoryName;
	}

	public BigDecimal getInFactoryId() {

		return inFactoryId;
	}

	public void setInFactoryId(BigDecimal inFactoryId) {

		this.inFactoryId = inFactoryId;
	}

	public String getInCode() {

		return inCode;
	}

	public void setInCode(String inCode) {

		this.inCode = inCode;
	}

	public void setFactoryId(BigDecimal factoryId) {

		this.factoryId = factoryId;
	}

	public BigDecimal getFactoryId() {

		return factoryId;
	}

	public void setSupplier(String supplier) {

		this.supplier = supplier;
	}

	public String getSupplier() {

		return supplier;
	}

	public void setImcomeMaterial(String imcomeMaterial) {

		this.imcomeMaterial = imcomeMaterial;
	}

	public String getImcomeMaterial() {

		return imcomeMaterial;
	}

	public void setMaterialModel(String materialModel) {

		this.materialModel = materialModel;
	}

	public String getMaterialModel() {

		return materialModel;
	}

	public void setMaterialPo(String materialPo) {

		this.materialPo = materialPo;
	}

	public String getMaterialPo() {

		return materialPo;
	}

	public void setBatchQty(BigDecimal batchQty) {

		this.batchQty = batchQty;
	}

	public BigDecimal getBatchQty() {

		return batchQty;
	}

	public void setSamplingQty(BigDecimal samplingQty) {

		this.samplingQty = samplingQty;
	}

	public BigDecimal getSamplingQty() {

		return samplingQty;
	}

	public void setMaterialsTime(Date materialsTime) {

		this.materialsTime = materialsTime;
	}

	public Date getMaterialsTime() {

		return materialsTime;
	}

	public void setInspectionTime(Date inspectionTime) {

		this.inspectionTime = inspectionTime;
	}

	public Date getInspectionTime() {

		return inspectionTime;
	}

	public void setInspectionNumber(String inspectionNumber) {

		this.inspectionNumber = inspectionNumber;
	}

	public String getInspectionNumber() {

		return inspectionNumber;
	}

	public void setGycode(String gycode) {

		this.gycode = gycode;
	}

	public String getGycode() {

		return gycode;
	}

	public void setSealNumber(String sealNumber) {

		this.sealNumber = sealNumber;
	}

	public String getSealNumber() {

		return sealNumber;
	}

	public void setSamplMethod(String samplMethod) {

		this.samplMethod = samplMethod;
	}

	public String getSamplMethod() {

		return samplMethod;
	}

	public void setPerformanceParameter(String performanceParameter) {

		this.performanceParameter = performanceParameter;
	}

	public String getPerformanceParameter() {

		return performanceParameter;
	}

	public void setDetectiomResult(String detectionResult) {

		this.detectionResult = detectionResult;
	}

	public String getDetectiomResult() {

		return detectionResult;
	}

	public void setPreserved1(String preserved1) {

		this.preserved1 = preserved1;
	}

	public String getPreserved1() {

		return preserved1;
	}

	public void setPreserved2(String preserved2) {

		this.preserved2 = preserved2;
	}

	public String getPreserved2() {

		return preserved2;
	}

	public void setPreserved3(String preserved3) {

		this.preserved3 = preserved3;
	}

	public String getPreserved3() {

		return preserved3;
	}

	public void setPreserved4(String preserved4) {

		this.preserved4 = preserved4;
	}

	public String getPreserved4() {

		return preserved4;
	}

	public void setPreserved5(String preserved5) {

		this.preserved5 = preserved5;
	}

	public String getPreserved5() {

		return preserved5;
	}
}
