/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-03
 * 修改历史 :
 *   1. [2019-04-03] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnSupProduceDTO extends BaseDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "截止时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endedTime;

	@ApiModelProperty(value = "外协工单号")
	private String contractNumber;

	@ApiModelProperty(value = "单板结存")
	private BigDecimal bomQty;

	@ApiModelProperty(value = "测试包结存")
	private BigDecimal packageQty;

	@ApiModelProperty(value = "产品库结存")
	private BigDecimal productQty;

	@ApiModelProperty(value = "备用字段1")
	private String preserved1;

	@ApiModelProperty(value = "备用字段2")
	private String preserved2;

	@ApiModelProperty(value = "备用字段3")
	private String preserved3;

	@ApiModelProperty(value = "备用字段4")
	private String preserved4;

	@ApiModelProperty(value = "备用字段5")
	private String preserved5;

	@ApiModelProperty(value = "计划数量")
	private BigDecimal planNumber;

	@ApiModelProperty(value = "中兴PO号")
	private String ztePo;

	@ApiModelProperty(value = "产品大类")
	private String prodClass;

	@ApiModelProperty(value = "产品型号")
	private String prodType;

	@ApiModelProperty(value = "产品名称")
	private String prodName;

	@ApiModelProperty(value = "产品代码")
	private String prodItem;

	@ApiModelProperty(value = "发货区域")
	private String issueZone;

	@ApiModelProperty(value = "已发货数")
	private BigDecimal sendedNumber;

	@ApiModelProperty(value = "工厂ID")
	private BigDecimal factoryId;

	@ApiModelProperty(value = "行ID号")
	private String recordId;

	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;

	@ApiModelProperty(value = "外协工厂ID")
	private BigDecimal inFactoryId;

	@ApiModelProperty(value = "接入方编码")
	private String inCode;
    
    private String validResp; // excel校验结果

	public BigDecimal getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(BigDecimal factoryId) {
		this.factoryId = factoryId;
	}

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public String getRecordId() {

		return recordId;
	}

	public void setRecordId(String recordId) {

		this.recordId = recordId;
	}

	public String getInFactoryName() {

		return inFactoryName;
	}

	public void setInFactoryName(String inFactoryName) {

		this.inFactoryName = inFactoryName;
	}

	public BigDecimal getInFactoryId() {

		return inFactoryId;
	}

	public void setInFactoryId(BigDecimal inFactoryId) {

		this.inFactoryId = inFactoryId;
	}

	public String getInCode() {

		return inCode;
	}

	public void setInCode(String inCode) {

		this.inCode = inCode;
	}

	public void setEndedTime(Date endedTime) {
		this.endedTime = endedTime;
	}

	public Date getEndedTime() {
		return endedTime;
	}

	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}

	public String getContractNumber() {
		return contractNumber;
	}

	public void setBomQty(BigDecimal bomQty) {
		this.bomQty = bomQty;
	}

	public BigDecimal getBomQty() {
		return bomQty;
	}

	public void setPackageQty(BigDecimal packageQty) {
		this.packageQty = packageQty;
	}

	public BigDecimal getPackageQty() {
		return packageQty;
	}

	public void setProductQty(BigDecimal productQty) {
		this.productQty = productQty;
	}

	public BigDecimal getProductQty() {
		return productQty;
	}

	public void setPreserved1(String preserved1) {
		this.preserved1 = preserved1;
	}

	public String getPreserved1() {
		return preserved1;
	}

	public void setPreserved2(String preserved2) {
		this.preserved2 = preserved2;
	}

	public String getPreserved2() {
		return preserved2;
	}

	public void setPreserved3(String preserved3) {
		this.preserved3 = preserved3;
	}

	public String getPreserved3() {
		return preserved3;
	}

	public void setPreserved4(String preserved4) {
		this.preserved4 = preserved4;
	}

	public String getPreserved4() {
		return preserved4;
	}

	public void setPreserved5(String preserved5) {
		this.preserved5 = preserved5;
	}

	public String getPreserved5() {
		return preserved5;
	}

	public void setPlanNumber(BigDecimal planNumber) {
		this.planNumber = planNumber;
	}

	public BigDecimal getPlanNumber() {
		return planNumber;
	}

	public void setZtePo(String ztePo) {
		this.ztePo = ztePo;
	}

	public String getZtePo() {
		return ztePo;
	}

	public void setProdClass(String prodClass) {
		this.prodClass = prodClass;
	}

	public String getProdClass() {
		return prodClass;
	}

	public void setProdType(String prodType) {
		this.prodType = prodType;
	}

	public String getProdType() {
		return prodType;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}

	public String getProdName() {
		return prodName;
	}

	public void setProdItem(String prodItem) {
		this.prodItem = prodItem;
	}

	public String getProdItem() {
		return prodItem;
	}

	public void setIssueZone(String issueZone) {
		this.issueZone = issueZone;
	}

	public String getIssueZone() {
		return issueZone;
	}

	public void setSendedNumber(BigDecimal sendedNumber) {
		this.sendedNumber = sendedNumber;
	}

	public BigDecimal getSendedNumber() {
		return sendedNumber;
	}
}
