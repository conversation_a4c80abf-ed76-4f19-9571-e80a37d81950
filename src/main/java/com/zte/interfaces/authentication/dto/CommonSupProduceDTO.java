package com.zte.interfaces.authentication.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonSupProduceDTO extends BaseDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	
    private List<CommonSupProduceDTO> dataList;
	@ApiModelProperty(value = "表上送类型")
	private String outFactoryTable;
    
    // ---- SnRidSupProduceDTO字段
	@ApiModelProperty(value = "接入方编码")
	private String inCode;
	@ApiModelProperty(value = "外协工厂id")
	private BigDecimal inFactoryId;
	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;
	@ApiModelProperty(value = "单板SN")
	private String bomSn;
	@ApiModelProperty(value = "行ID号")
	private String recordId;
	@ApiModelProperty(value = "外协Reel_ID")
	private String outReelId;
	@ApiModelProperty(value = "外协工单号")
	private String contractNumber;
	@ApiModelProperty(value = "产品代码")
	private String prodItem;
	@ApiModelProperty(value = "产品型号")
	private String prodType;
	@ApiModelProperty(value = "单板批次")
	private String prodplanId;
	@ApiModelProperty(value = "线体")
	private String lineName;
	@ApiModelProperty(value = "SN扫描时间")
	private Date scanTime;
	@ApiModelProperty(value = "上料数量")
	private BigDecimal feedQty;
	@ApiModelProperty(value = "上料时间")
	private Date feedTime;
	@ApiModelProperty(value = "备用字段1")
	private String preserved1;
	@ApiModelProperty(value = "备用字段2")
	private String preserved2;
	@ApiModelProperty(value = "备用字段3")
	private String preserved3;
	@ApiModelProperty(value = "备用字段4")
	private String preserved4;
	@ApiModelProperty(value = "备用字段5")
	private String preserved5;
    
	// ---- EnSupProduceDTO字段
	@ApiModelProperty(value = "截止时间")
	private Date endedTime;
	@ApiModelProperty(value = "单板结存")
	private BigDecimal bomQty;
	@ApiModelProperty(value = "测试包结存")
	private BigDecimal packageQty;
	@ApiModelProperty(value = "产品库结存")
	private BigDecimal productQty;
	@ApiModelProperty(value = "计划数量")
	private BigDecimal planNumber;
	@ApiModelProperty(value = "中兴PO号")
	private String ztePo;
	@ApiModelProperty(value = "产品大类")
	private String prodClass;
	@ApiModelProperty(value = "产品名称")
	private String prodName;
	@ApiModelProperty(value = "发货区域")
	private String issueZone;
	@ApiModelProperty(value = "已发货数")
	private BigDecimal sendedNumber;
	
	// ---- ComSupProduceDTO字段
	@ApiModelProperty(value = "整机SN/EN/产品SN")
	private String completeSn;
	@ApiModelProperty(value = "MAC")
	private String mac;
	@ApiModelProperty(value = "BOSA条码")
	private String bosaSn;
	@ApiModelProperty(value = "电源适配器条码")
	private String powerCode;
	@ApiModelProperty(value = "遥控器条码")
	private String remoteCode;
	@ApiModelProperty(value = "GPON_SN")
	private String gponSn;
	@ApiModelProperty(value = "D_SN")
	private String dSn;
	@ApiModelProperty(value = "机顶盒STBID")
	private String stbid;
	@ApiModelProperty(value = "设备标识")
	private String equipmrntName;
	
	// ---- IqcSupProduceDTO字段
	@ApiModelProperty(value = "供应商")
	private String supplier;
	@ApiModelProperty(value = "物料名称")
	private String imcomeMaterial;
	@ApiModelProperty(value = "物料型号")
	private String materialModel;
	@ApiModelProperty(value = "物料PO号")
	private String materialPo;
	@ApiModelProperty(value = "本批数量")
	private BigDecimal batchQty;
	@ApiModelProperty(value = "抽检数量")
	private BigDecimal samplingQty;
	@ApiModelProperty(value = "来料日期")
	private Date materialsTime;
	@ApiModelProperty(value = "检测日期")
	private Date inspectionTime;
	@ApiModelProperty(value = "检测单号")
	private String inspectionNumber;
	@ApiModelProperty(value = "文件封号")
	private String gycode;
	@ApiModelProperty(value = "封样编号")
	private String sealNumber;
	@ApiModelProperty(value = "抽样方式")
	private String samplMethod;
	@ApiModelProperty(value = "性能参数")
	private String performanceParameter;
	@ApiModelProperty(value = "检测结果")
	private String detectionResult;
	
	// ---- SalSupProduceDTO字段
	@ApiModelProperty(value = "客供客售单号")
	private String salesNumber;
	@ApiModelProperty(value = "库存截止时间")
	private Date deadlineTime;
	@ApiModelProperty(value = "物料代码")
	private String itemNo;
	@ApiModelProperty(value = "物料条码")
	private String itemCode;
	@ApiModelProperty(value = "ZTE_Reel_ID")
	private String zteReelId;
	@ApiModelProperty(value = "库存数量")
	private BigDecimal inventoryQty;
	
	// ---- SalCSupProduceDTO字段
	@ApiModelProperty(value = "物料名称")
	private String itemName;
	@ApiModelProperty(value = "Reel_ID数量")
	private BigDecimal reelIdQty;
	@ApiModelProperty(value = "关联时间")
	private Date associationTime;
	
	// ==== 
	public List<CommonSupProduceDTO> getDataList() {
		return dataList;
	}
	public void setDataList(List<CommonSupProduceDTO> dataList) {
		this.dataList = dataList;
	}
	public String getInCode() {
		return inCode;
	}
	public void setInCode(String inCode) {
		this.inCode = inCode;
	}
	public BigDecimal getInFactoryId() {
		return inFactoryId;
	}
	public void setInFactoryId(BigDecimal inFactoryId) {
		this.inFactoryId = inFactoryId;
	}
	public String getInFactoryName() {
		return inFactoryName;
	}
	public void setInFactoryName(String inFactoryName) {
		this.inFactoryName = inFactoryName;
	}
	public String getBomSn() {
		return bomSn;
	}
	public void setBomSn(String bomSn) {
		this.bomSn = bomSn;
	}
	public String getRecordId() {
		return recordId;
	}
	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}
	public String getOutReelId() {
		return outReelId;
	}
	public void setOutReelId(String outReelId) {
		this.outReelId = outReelId;
	}
	public String getContractNumber() {
		return contractNumber;
	}
	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}
	public String getProdItem() {
		return prodItem;
	}
	public void setProdItem(String prodItem) {
		this.prodItem = prodItem;
	}
	public String getProdType() {
		return prodType;
	}
	public void setProdType(String prodType) {
		this.prodType = prodType;
	}
	public String getProdplanId() {
		return prodplanId;
	}
	public void setProdplanId(String prodplanId) {
		this.prodplanId = prodplanId;
	}
	public String getLineName() {
		return lineName;
	}
	public void setLineName(String lineName) {
		this.lineName = lineName;
	}
	public Date getScanTime() {
		return scanTime;
	}
	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}
	public BigDecimal getFeedQty() {
		return feedQty;
	}
	public void setFeedQty(BigDecimal feedQty) {
		this.feedQty = feedQty;
	}
	public Date getFeedTime() {
		return feedTime;
	}
	public void setFeedTime(Date feedTime) {
		this.feedTime = feedTime;
	}
	public String getPreserved1() {
		return preserved1;
	}
	public void setPreserved1(String preserved1) {
		this.preserved1 = preserved1;
	}
	public String getPreserved2() {
		return preserved2;
	}
	public void setPreserved2(String preserved2) {
		this.preserved2 = preserved2;
	}
	public String getPreserved3() {
		return preserved3;
	}
	public void setPreserved3(String preserved3) {
		this.preserved3 = preserved3;
	}
	public String getPreserved4() {
		return preserved4;
	}
	public void setPreserved4(String preserved4) {
		this.preserved4 = preserved4;
	}
	public String getPreserved5() {
		return preserved5;
	}
	public void setPreserved5(String preserved5) {
		this.preserved5 = preserved5;
	}
	public Date getEndedTime() {
		return endedTime;
	}
	public void setEndedTime(Date endedTime) {
		this.endedTime = endedTime;
	}
	public BigDecimal getBomQty() {
		return bomQty;
	}
	public void setBomQty(BigDecimal bomQty) {
		this.bomQty = bomQty;
	}
	public BigDecimal getPackageQty() {
		return packageQty;
	}
	public void setPackageQty(BigDecimal packageQty) {
		this.packageQty = packageQty;
	}
	public BigDecimal getProductQty() {
		return productQty;
	}
	public void setProductQty(BigDecimal productQty) {
		this.productQty = productQty;
	}
	public BigDecimal getPlanNumber() {
		return planNumber;
	}
	public void setPlanNumber(BigDecimal planNumber) {
		this.planNumber = planNumber;
	}
	public String getZtePo() {
		return ztePo;
	}
	public void setZtePo(String ztePo) {
		this.ztePo = ztePo;
	}
	public String getProdClass() {
		return prodClass;
	}
	public void setProdClass(String prodClass) {
		this.prodClass = prodClass;
	}
	public String getProdName() {
		return prodName;
	}
	public void setProdName(String prodName) {
		this.prodName = prodName;
	}
	public String getIssueZone() {
		return issueZone;
	}
	public void setIssueZone(String issueZone) {
		this.issueZone = issueZone;
	}
	public BigDecimal getSendedNumber() {
		return sendedNumber;
	}
	public void setSendedNumber(BigDecimal sendedNumber) {
		this.sendedNumber = sendedNumber;
	}
	public String getCompleteSn() {
		return completeSn;
	}
	public void setCompleteSn(String completeSn) {
		this.completeSn = completeSn;
	}
	public String getMac() {
		return mac;
	}
	public void setMac(String mac) {
		this.mac = mac;
	}
	public String getBosaSn() {
		return bosaSn;
	}
	public void setBosaSn(String bosaSn) {
		this.bosaSn = bosaSn;
	}
	public String getPowerCode() {
		return powerCode;
	}
	public void setPowerCode(String powerCode) {
		this.powerCode = powerCode;
	}
	public String getRemoteCode() {
		return remoteCode;
	}
	public void setRemoteCode(String remoteCode) {
		this.remoteCode = remoteCode;
	}
	public String getGponSn() {
		return gponSn;
	}
	public void setGponSn(String gponSn) {
		this.gponSn = gponSn;
	}
	public String getdSn() {
		return dSn;
	}
	public void setdSn(String dSn) {
		this.dSn = dSn;
	}
	public String getStbid() {
		return stbid;
	}
	public void setStbid(String stbid) {
		this.stbid = stbid;
	}
	public String getEquipmrntName() {
		return equipmrntName;
	}
	public void setEquipmrntName(String equipmrntName) {
		this.equipmrntName = equipmrntName;
	}
	public String getSupplier() {
		return supplier;
	}
	public void setSupplier(String supplier) {
		this.supplier = supplier;
	}
	public String getImcomeMaterial() {
		return imcomeMaterial;
	}
	public void setImcomeMaterial(String imcomeMaterial) {
		this.imcomeMaterial = imcomeMaterial;
	}
	public String getMaterialModel() {
		return materialModel;
	}
	public void setMaterialModel(String materialModel) {
		this.materialModel = materialModel;
	}
	public String getMaterialPo() {
		return materialPo;
	}
	public void setMaterialPo(String materialPo) {
		this.materialPo = materialPo;
	}
	public BigDecimal getBatchQty() {
		return batchQty;
	}
	public void setBatchQty(BigDecimal batchQty) {
		this.batchQty = batchQty;
	}
	public BigDecimal getSamplingQty() {
		return samplingQty;
	}
	public void setSamplingQty(BigDecimal samplingQty) {
		this.samplingQty = samplingQty;
	}
	public Date getMaterialsTime() {
		return materialsTime;
	}
	public void setMaterialsTime(Date materialsTime) {
		this.materialsTime = materialsTime;
	}
	public Date getInspectionTime() {
		return inspectionTime;
	}
	public void setInspectionTime(Date inspectionTime) {
		this.inspectionTime = inspectionTime;
	}
	public String getInspectionNumber() {
		return inspectionNumber;
	}
	public void setInspectionNumber(String inspectionNumber) {
		this.inspectionNumber = inspectionNumber;
	}
	public String getGycode() {
		return gycode;
	}
	public void setGycode(String gycode) {
		this.gycode = gycode;
	}
	public String getSealNumber() {
		return sealNumber;
	}
	public void setSealNumber(String sealNumber) {
		this.sealNumber = sealNumber;
	}
	public String getSamplMethod() {
		return samplMethod;
	}
	public void setSamplMethod(String samplMethod) {
		this.samplMethod = samplMethod;
	}
	public String getPerformanceParameter() {
		return performanceParameter;
	}
	public void setPerformanceParameter(String performanceParameter) {
		this.performanceParameter = performanceParameter;
	}
	public String getDetectionResult() {
		return detectionResult;
	}
	public void setDetectionResult(String detectionResult) {
		this.detectionResult = detectionResult;
	}
	public String getSalesNumber() {
		return salesNumber;
	}
	public void setSalesNumber(String salesNumber) {
		this.salesNumber = salesNumber;
	}
	public Date getDeadlineTime() {
		return deadlineTime;
	}
	public void setDeadlineTime(Date deadlineTime) {
		this.deadlineTime = deadlineTime;
	}
	public String getItemNo() {
		return itemNo;
	}
	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}
	public String getItemCode() {
		return itemCode;
	}
	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}
	public String getZteReelId() {
		return zteReelId;
	}
	public void setZteReelId(String zteReelId) {
		this.zteReelId = zteReelId;
	}
	public BigDecimal getInventoryQty() {
		return inventoryQty;
	}
	public void setInventoryQty(BigDecimal inventoryQty) {
		this.inventoryQty = inventoryQty;
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public BigDecimal getReelIdQty() {
		return reelIdQty;
	}
	public void setReelIdQty(BigDecimal reelIdQty) {
		this.reelIdQty = reelIdQty;
	}
	public Date getAssociationTime() {
		return associationTime;
	}
	public void setAssociationTime(Date associationTime) {
		this.associationTime = associationTime;
	}
	public String getOutFactoryTable() {
		return outFactoryTable;
	}
	public void setOutFactoryTable(String outFactoryTable) {
		this.outFactoryTable = outFactoryTable;
	}
	
}
