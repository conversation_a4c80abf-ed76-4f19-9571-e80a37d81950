package com.zte.interfaces.authentication.dto;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 认证基类
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseAuthDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "上送类型")
    private String sendType;

    @ApiModelProperty(value = "签名")
    private String sign;

    @ApiModelProperty(value = "业务数据记录(业务实体列表转换成的json数组字符串)")
    private JSONArray records;

    /**
     * 接入方编码
     */
    @ApiModelProperty(value = " 接入方编码")
    protected String inCode;
    
    
    public String getInCode() {
    
        return inCode;
    }

    
    public void setInCode(String inCode) {
    
        this.inCode = inCode;
    }
    @Override
    public String toString() {

        return "BaseAuthDTO [sendType=" + sendType + ", sign=" + sign + ", records=" + records.toString() + "]";
    }
    
    public String getSendType() {

        return sendType;
    }

    public void setSendType(String sendType) {

        this.sendType = sendType;
    }

    public String getSign() {

        return sign;
    }

    public void setSign(String sign) {

        this.sign = sign;
    }
    
    public JSONArray getRecords() {
    
        return records;
    }
    
    public void setRecords(JSONArray records) {
    
        this.records = records;
    }
    
}
