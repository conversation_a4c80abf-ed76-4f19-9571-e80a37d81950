/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-03-21
 * 修改历史 :
 * 1. [2019-03-21] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 上送数据dto
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "接入方编码")
    private String inCode;

    @ApiModelProperty(value = "接入业务数据")
    private String data;

    @Override
    public String toString() {

        return "SendDataDTO [inCode=" + inCode + ", data=" + data + "]";
    }

    public String getData() {

        return data;
    }

    public void setData(String data) {

        this.data = data;
    }

    public String getInCode() {

        return inCode;
    }

    public void setInCode(String inCode) {

        this.inCode = inCode;
    }

}
