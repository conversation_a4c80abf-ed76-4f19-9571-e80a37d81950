/**
 * 项目名称 : zte-mes-manufactureshare-centerfactory
 * 创建日期 : 2019-04-04
 * 修改历史 :
 * 1. [2019-04-04] 创建文件 by 6055000030
 **/
package com.zte.interfaces.authentication.dto;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.dto.busmanage.BaseDTO;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class SnRidSupProduceDTO extends BaseDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "工厂id")
	private BigDecimal factoryId;

	@ApiModelProperty(value = "单板SN")
	private String bomSn;

	@ApiModelProperty(value = "行ID号")
	private String recordId;

	@ApiModelProperty(value = "外协Reel_ID")
	private String outReelId;

	@ApiModelProperty(value = "外协工单号")
	private String contractNumber;

	@ApiModelProperty(value = "产品代码")
	private String prodItem;

	@ApiModelProperty(value = "产品型号")
	private String prodType;

	@ApiModelProperty(value = "单板批次")
	private String prodplanId;

	@ApiModelProperty(value = "线体")
	private String lineName;

	@ApiModelProperty(value = "SN扫描时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date scanTime;

	@ApiModelProperty(value = "上料数量")

	private BigDecimal feedQty;

	@ApiModelProperty(value = "上料时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date feedTime;

	@ApiModelProperty(value = "备用字段1")
	private String preserved1;

	@ApiModelProperty(value = "备用字段2")
	private String preserved2;

	@ApiModelProperty(value = "备用字段3")
	private String preserved3;

	@ApiModelProperty(value = "备用字段4")
	private String preserved4;

	@ApiModelProperty(value = "备用字段5")
	private String preserved5;

	@ApiModelProperty(value = "外协工厂名称")
	private String inFactoryName;

	@ApiModelProperty(value = "外协工厂id")
	private BigDecimal inFactoryId;

	@ApiModelProperty(value = "接入方编码")
	private String inCode;
    
    private String validResp; // excel校验结果

	public String getValidResp() {
		return validResp;
	}

	public void setValidResp(String validResp) {
		this.validResp = validResp;
	}

	public BigDecimal getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(BigDecimal factoryId) {
		this.factoryId = factoryId;
	}

	public String getBomSn() {
		return bomSn;
	}

	public void setBomSn(String bomSn) {
		this.bomSn = bomSn;
	}

	public String getRecordId() {
		return recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getOutReelId() {
		return outReelId;
	}

	public void setOutReelId(String outReelId) {
		this.outReelId = outReelId;
	}

	public String getContractNumber() {
		return contractNumber;
	}

	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}

	public String getProdItem() {
		return prodItem;
	}

	public void setProdItem(String prodItem) {
		this.prodItem = prodItem;
	}

	public String getProdType() {
		return prodType;
	}

	public void setProdType(String prodType) {
		this.prodType = prodType;
	}

	public String getProdplanId() {
		return prodplanId;
	}

	public void setProdplanId(String prodplanId) {
		this.prodplanId = prodplanId;
	}

	public String getLineName() {
		return lineName;
	}

	public void setLineName(String lineName) {
		this.lineName = lineName;
	}

	public Date getScanTime() {
		return scanTime;
	}

	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}

	public BigDecimal getFeedQty() {
		return feedQty;
	}

	public void setFeedQty(BigDecimal feedQty) {
		this.feedQty = feedQty;
	}

	public Date getFeedTime() {
		return feedTime;
	}

	public void setFeedTime(Date feedTime) {
		this.feedTime = feedTime;
	}

	public String getPreserved1() {
		return preserved1;
	}

	public void setPreserved1(String preserved1) {
		this.preserved1 = preserved1;
	}

	public String getPreserved2() {
		return preserved2;
	}

	public void setPreserved2(String preserved2) {
		this.preserved2 = preserved2;
	}

	public String getPreserved3() {
		return preserved3;
	}

	public void setPreserved3(String preserved3) {
		this.preserved3 = preserved3;
	}

	public String getPreserved4() {
		return preserved4;
	}

	public void setPreserved4(String preserved4) {
		this.preserved4 = preserved4;
	}

	public String getPreserved5() {
		return preserved5;
	}

	public void setPreserved5(String preserved5) {
		this.preserved5 = preserved5;
	}

	public String getInFactoryName() {
		return inFactoryName;
	}

	public void setInFactoryName(String inFactoryName) {
		this.inFactoryName = inFactoryName;
	}

	public BigDecimal getInFactoryId() {
		return inFactoryId;
	}

	public void setInFactoryId(BigDecimal inFactoryId) {
		this.inFactoryId = inFactoryId;
	}

	public String getInCode() {
		return inCode;
	}

	public void setInCode(String inCode) {
		this.inCode = inCode;
	}

}
