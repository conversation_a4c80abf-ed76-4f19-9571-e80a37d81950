package com.zte.interfaces.dto.alibaba;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * packageName com.zte.interfaces.dto.alibaba
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/5/16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProblemRequestDTO {
    /**
     * 整机/主板对应站位的request_id
     */
    @JsonProperty("problem_request_id")
    private String problemRequestId;

    /**
     * 测试站位类型；BOARD_CAPTURE(主板站位/器件), MCT_CAPTURE(整机站位/模块)
     */
    private String type;

    /**
     * 失败的站位名
     */
    @JsonProperty("station_name")
    private String stationName;

    /**
     * 失败设备的SN
     */
    private String sn;

    /**
     * 品牌信息(英文)
     */
    private String brand;

    /**
     * 一阶错误码；设备为SWITCH时，非必填
     */
    @JsonProperty("error_code")
    private String errorCode;

    /**
     * 二阶错误码；可选
     */
    @JsonProperty("error_code_second")
    private String errorCodeSecond;

    /**
     * 三阶错误码；具体不良描述
     */
    @JsonProperty("error_code_third")
    private String errorCodeThird;

    /**
     * 失败部件名称或位置
     */
    @JsonProperty("error_position")
    private String errorPosition;

    /**
     * 失败详细原因
     */
    @JsonProperty("error_msg")
    private String errorMsg;

    /**
     * 失败定位后的方案码；设备为SWITCH时，非必填
     */
    @JsonProperty("action_code")
    private String actionCode;

    /**
     * 失败定位后的方案描述；设备为SWITCH时，非必填
     */
    @JsonProperty("action_msg")
    private String actionMsg;

    /**
     * 关联异常分析系统工单ID
     */
    @JsonProperty("external_system_id")
    private String externalSystemId;

    /**
     * 故障发生时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("failed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date failedTime;

    /**
     * 进维修站时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("rework_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date reworkTime;

    /**
     * 出维修站时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("finish_rework_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date finishReworkTime;

    /**
     * 维修完成后重新进入产线的站位名
     */
    @JsonProperty("retest_station")
    private String retestStation;

    /**
     * 重新进产线的时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("retest_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date retestTime;

    /**
     * 部件更换信息列表
     */
    @JsonProperty("replace_components")
    private List<ReplaceComponentDTO> replaceComponents;
}
