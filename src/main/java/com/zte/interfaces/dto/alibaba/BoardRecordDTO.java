package com.zte.interfaces.dto.alibaba;/**
 * @date 2025-05-15 20:54
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * packageName com.zte.interfaces.dto.alibaba
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/5/15
 */
@Data
public class BoardRecordDTO {
    /**
     * // 幂等ID，必须保证唯一
     */
    @JsonProperty(value = "request_id")
    private String requestId;
    /**
     * // 设备类型
     */
    @JsonProperty(value = "type")
    private String type;
    /**
     * // 供应商主板生产工单编号
     */
    @JsonProperty(value = "workorder_id")
    private String workorderId;
    /**
     * // 主板SN
     */
    @JsonProperty(value = "board_sn")
    private String boardSn;
    /**
     * // 品牌名称（英文）
     */
    @JsonProperty(value = "brand")
    private String brand;
    /**
     * // 站位名称
     */
    @JsonProperty(value = "station_name")
    private String stationName;
    /**
     * // 站位开始时间
     */
    @JsonProperty(value = "started_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startedTime;
    /**
     * // 站位的完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonProperty(value = "finished_time")
    private Date finishedTime;
    /**
     * // 可选值为：Pass/Fail
     */
    @JsonProperty(value = "result")
    private String result;
    /**
     * // 工序站位检查结果失败时的原因说明
     */
    @JsonProperty(value = "message")
    private String message;
    /**
     * // 当前站位日志
     */
    private List<String> ossFileKey;
    /**
     private Json param;
     *
     */
    private String param;
    @JsonProperty(value = "version")
    private String version; // 工序版本信息
    /**
     * // 工厂名称
     */
    @JsonProperty(value = "manufacturer_name")
    private String manufacturerName;
    /**
     * // 主板MPN
     */
    @JsonProperty(value = "board_mpn")
    private String boardMpn;
    /**
     * // 主板型号
     */
    @JsonProperty(value = "board_model")
    private String boardModel;
    /**
     * // 主板规格
     */
    @JsonProperty(value = "board_form")
    private String boardForm;
    /**
     * // 线别
     */
    @JsonProperty(value = "line")
    private String line;
}
