package com.zte.interfaces.dto.alibaba;/**
 * @date 2025-05-16 9:30
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * packageName com.zte.interfaces.dto.alibaba
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/5/16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReplaceComponentDTO {
    /**
     * 更换部件编号
     */
    @JsonProperty("component_id")
    private String componentId;

    /**
     * 更换部件名称
     */
    @JsonProperty("component_name")
    private String componentName;
}
