package com.zte.interfaces.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-12-29 11:20
 */
@Data
@ToString
@EqualsAndHashCode
@Accessors(chain=true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WarehouseBarcodeQueryDTO implements Serializable {
    private String prodplanId;
    private String taskNo;
    private String billType;
    private String factoryId;
}
