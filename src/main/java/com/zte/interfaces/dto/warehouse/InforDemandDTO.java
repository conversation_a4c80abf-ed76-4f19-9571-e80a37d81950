package com.zte.interfaces.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InforDemandDTO implements Serializable {

    @JsonProperty(value = "EDITDATE")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private Date editdate;

    @JsonProperty(value = "EXTERNALORDERKEY2")
    private String externalorderkey2;
}
