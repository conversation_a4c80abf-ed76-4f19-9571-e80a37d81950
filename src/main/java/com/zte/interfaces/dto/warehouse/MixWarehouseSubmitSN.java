package com.zte.interfaces.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-29 11:20
 */
@Setter
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class MixWarehouseSubmitSN {
    private String sn;
    private String workOrderNo;
    private String currProcessCode;
    private String currProcessName;
    private String craftSection;
    private String workStation;
    private String lpn;
    private String planId;
    private String taskNo;
    private String itemCode;
    private String itemName;
    private String isLead;
    private BigDecimal taskQty;
}
