package com.zte.interfaces.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-29 11:20
 */
@Setter
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class MixWarehouseSubmitDTO implements Serializable {
    private static final long serialVersionUID = -9084790316302358613L;
    private String stockType;
    private String stockName;
    private String subStock;
    private String locatorName;
    private String orgId;
    private BigDecimal locatorId;
    private String remark;
    private String scannedLpn;
    private List<String> wipSns;
}
