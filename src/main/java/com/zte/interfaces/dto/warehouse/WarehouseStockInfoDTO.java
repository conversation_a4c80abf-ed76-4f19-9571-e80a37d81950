package com.zte.interfaces.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WarehouseStockInfoDTO implements Serializable {

    private static final long serialVersionUID = -895378680615899938L;
    /**
     * 单据号
     */
    private String billNo;

    /**
     * 批次号
     **/
    private String prodplanId;
    /**
     * 生产计划单号
     **/
    private String prodplanNo;
    /**
     * 产品代码
     **/
    private String itemNo;
    /**
     * 产品名称
     **/
    private String itemName;

    /**
     * 计划总数量
     **/
    private BigDecimal taskQty;
    /**
     * 软件版本
     **/
    private String softwareVersion;
    private String contractNo;
    private String internalType;
    /**
     * 环保属性
     **/
    private String isLead;
    /**
     * 已提交数量
     **/
    private BigDecimal commitedQty;
    /**
     * 提交数量
     **/
    private BigDecimal submitQty;
    /**
     * 订单编号
     **/
    private String planId;

    /**
     * 小批量标识
     **/
    private boolean small;

    /**
     * 重度维修
     **/
    private boolean repair;

    /**
     * 指令号
     */
    private String workOrderNo;

    /**
     * 仓库类型
     */
    private String stockType;
    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 备注
     **/
    private String remark;
    private String taskId;
    private BigDecimal orgId;
    //任务编号
    private String taskNo;
    private String billType;
    private List<String> prodplanIdList;
    private String currProcess;
    private String applyNo;
    // 来源系统
    private String sourceSys;
    private String empNo;
    private String factoryId;
    private String taskIdList;
    //交货仓
    private String stock;
    private String productType;
    //任务类型
    private String taskType;
    @ApiModelProperty(value = "前端打印需要用的时间")
    @JsonFormat(pattern = "yyyy年MM月dd日 HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date submitDate;
    /**
     * 是否外协,只包含外协>入库节点
     */
    private Integer isOutsource;
}
