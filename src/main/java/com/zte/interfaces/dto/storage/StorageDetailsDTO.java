package com.zte.interfaces.dto.storage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.common.utils.MpConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther:
 * @Date: 2020/6/11 17
 * @Description:
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StorageDetailsDTO implements Serializable {
    private static final long serialVersionUID = -409016451494697176L;
    private List<String> splitSrcList;
    /**
     * 分页条数
     */
    private int pageSize = 10;
    /**
     * 当前页
     */
    private int currentPage = 1;

    private String id;
    /**
     * 需求编号
     */
    private String reqId;
    /**
     * 需求明细ID
     */
    private String reqDetailId;
    /**
     * 任务头
     */
    private String taskHeadId;
    /**
     * 任务明细
     */
    private String taskDetailId;
    /**
     * 单据类型
     */
    private String billTypeCode;

    /**
     * 操作类型 操作类型 (1-INSERT 2-UPDATE 3-DELETE 4-SPLIT)
     */
    private String operateTypeCode;
    /**
     * 箱号
     */
    private String boxNo;
    /**
     * 物料代码
     */
    private String itemNo;
    /**
     * 条码
     */
    private String itemBarcode;

    private String reelid;
    /**
     * 22批次码
     */
    private String ttBarcode;
    /**
     * Infor仓库的拣料容器
     */
    private String lfid;
    /**
     * 库存类型编码 ('box''item' 'barcode')
     */
    private String stockTypeCode;
    /**
     * 库存类型
     */
    private String stockType;
    /**
     * 出入库类型 (0-入库1-出库)
     */
    private Integer actionCode;
    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date journalDate;

    /**
     * 现有库存修改前的值
     */
    private BigDecimal qtyBefore;
    /**
     * 现有库存修改后的值
     */
    private BigDecimal qtyAfter;
    /**
     * 现有库存变化的值
     */
    private BigDecimal qtyChange;
    /**
     * 可用库存修改前的值
     */
    private BigDecimal canuseQtyBefore;
    /**
     * 可用库存修改后的值
     */
    private BigDecimal canuseQtyAfter;
    /**
     * 可用库存变化的值
     */
    private BigDecimal canuseQtyChange;
    /**
     * 出库库存ID
     */
    private String fromSummaryId;
    /**
     * 入库库存ID
     */
    private String toSummaryId;

    /**
     * 入库库存父ID
     */
    private String toParentId;
    /**
     * 交易ID
     */
    private String journalId;

    /**
     * 交易类型(1-入在途，2-出在途，3-入物流，4-出物流，5-入工程/客户库，6-出工程/客户库)
     */
    private String actionType;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    /**
     * 更新人
     */
    private String lastUpdatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    /**
     * 有效标识
     */
    private String enableFlag;
    /**
     * 单据号
     */
    private String billNo;
    /**
     * 原始单据号
     */
    private String srcBillNo;
    /**
     * 关联单据号
     */
    private String relatedBillNo;
    /**
     * 站位
     */
    private String toLocationId;
    /**
     * 模组
     */
    private String toStockId;
    /**
     * 线体
     */
    private String toWarehouseId;

    /**
     * 物料集合
     */
    private String itemNos;

    private boolean searchCount = true;
    private String accessSystem = MpConstant.WAREHOUSE_TYPE_DEFAULT;

    @ApiModelProperty(value = "物料名称")
    private String itemName;
    @ApiModelProperty(value = "abc分类")
    private String abcType;
    @ApiModelProperty(value = "环保属性")
    private String isLead;
    @ApiModelProperty(value = "防潮等级")
    private String wetLevel;
    @ApiModelProperty(value = "规格型号")
    private String style;
    @ApiModelProperty(value = "品牌名称")
    private String bgBrandNo;
    @ApiModelProperty(value = "仓库ID")
    private String warehouseId;
    @ApiModelProperty(value = "货区ID")
    private String stockId;
    @ApiModelProperty(value = "货位ID")
    private String locationId;
    @ApiModelProperty(value = "交易时间start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date journalDateStart;
    @ApiModelProperty(value = "交易时间end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date journalDateEnd;
    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;
    @ApiModelProperty(value = "货区编码")
    private String stockNo;
    @ApiModelProperty(value = "货位编码")
    private String locationNo;
    @ApiModelProperty(value = "编排器参数，页数")
    private int pageNo;
    @ApiModelProperty(value = "编排器参数，交易时间")
    private String journalDateRange;
    @ApiModelProperty(value = "工厂ID")
    private String factoryId;
    @ApiModelProperty(value = "ERP交易ID")
    private String ediJournalId;
    @ApiModelProperty(value = "ERP处理标识")
    private String processFlag;
    @ApiModelProperty(value = "ERP处理消息")
    private String errorMsg;
    @ApiModelProperty(value = "erp交易类型ID")
    private String transactionTypeId;
    @ApiModelProperty(value = "erp交易类型名称")
    private String transactionTypeName;
    @ApiModelProperty(value = "erp交易数量")
    private String transactionQuantity;
    private String empNo;
    private String warehouseName;
    private String stockName;

    /**
     * 批次
     */
    private String productionBatch;

    private List<String> returnColumns;

    /**
     * 计划跟踪单
     */
    private String taskNo;
}
