package com.zte.interfaces.dto.storage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓储中心交易
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class StockDetailsDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "需求编号")
    private String reqId;
    @ApiModelProperty(value = "需求明细ID")
    private String reqDetailId;
    @ApiModelProperty(value = "任务头")
    private String taskHeadId;
    @ApiModelProperty(value = "任务明细")
    private String taskDetailId;
    @ApiModelProperty(value = "单据类型")
    private String billTypeCode;
    @ApiModelProperty(value = "操作类型 (1-INSERT 2-UPDATE 3-DELETE 4-SPLIT)")
    private String operateTypeCode;
    @ApiModelProperty(value = "箱号")
    private String boxNo;
    @ApiModelProperty(value = "物料代码")
    private String itemNo;
    @ApiModelProperty(value = "条码")
    private String itemBarcode;
    @ApiModelProperty(value = "reelid")
    private String reelid;
    @ApiModelProperty(value = "22批次条码")
    private String ttBarcode;
    @ApiModelProperty(value = "Infor仓库的拣料容器")
    private String lfid;
    @ApiModelProperty(value = "库存类型编码 ('box''item' 'barcode')")
    private String stockTypeCode;
    @ApiModelProperty(value = "库存类型")
    private String stockType;
    @ApiModelProperty(value = "出入库类型 (0-入库1-出库)")
    private BigDecimal actionCode;
    @ApiModelProperty(value = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date journalDate;
    @ApiModelProperty(value = "现有库存修改前的值")
    private BigDecimal qtyBefore;
    @ApiModelProperty(value = "现有库存修改后的值")
    private BigDecimal qtyAfter;
    @ApiModelProperty(value = "现有库存变化的值")
    private BigDecimal qtyChange;
    @ApiModelProperty(value = "可用库存修改前的值")
    private BigDecimal canuseQtyBefore;
    @ApiModelProperty(value = "可用库存修改后的值")
    private BigDecimal canuseQtyAfter;
    @ApiModelProperty(value = "可用库存变化的值")
    private BigDecimal canuseQtyChange;
    @ApiModelProperty(value = "出库库存ID")
    private String fromSummaryId;
    @ApiModelProperty(value = "入库库存ID")
    private String toSummaryId;
    @ApiModelProperty(value = "入库库存父ID")
    private String toParentId;
    @ApiModelProperty(value = "交易ID")
    private String journalId;
    @ApiModelProperty(value = "交易类型(1-入在途，2-出在途，3-入物流，4-出物流，5-入工程/客户库，6-出工程/客户库)")
    private BigDecimal actionType;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;
    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;
    @ApiModelProperty(value = "接入业务系统标示")
    private BigDecimal accessSystem;
    @ApiModelProperty(value = "有效标示")
    private String enableFlag;
    @ApiModelProperty(value = "字段扩展1")
    private String rfStr01;
    @ApiModelProperty(value = "字段扩展2")
    private String rfStr02;
    @ApiModelProperty(value = "字段扩展3")
    private String rfStr03;
    @ApiModelProperty(value = "字段扩展4")
    private String rfStr04;
    @ApiModelProperty(value = "字段扩展5")
    private String rfStr05;
    @ApiModelProperty(value = "字段扩展6")
    private String rfStr06;
    @ApiModelProperty(value = "字段扩展7")
    private String rfStr07;
    @ApiModelProperty(value = "字段扩展8")
    private String rfStr08;
    @ApiModelProperty(value = "字段扩展9")
    private String rfStr09;
    @ApiModelProperty(value = "字段扩展10")
    private String rfStr10;
    @ApiModelProperty(value = "字段扩展11")
    private String rfStr11;
    @ApiModelProperty(value = "字段扩展12")
    private String rfStr12;
    @ApiModelProperty(value = "字段扩展13")
    private String rfStr13;
    @ApiModelProperty(value = "字段扩展14")
    private String rfStr14;
    @ApiModelProperty(value = "字段扩展15")
    private String rfStr15;
    @ApiModelProperty(value = "字段扩展16")
    private String rfStr16;
    @ApiModelProperty(value = "字段扩展17")
    private String rfStr17;
    @ApiModelProperty(value = "字段扩展18")
    private String rfStr18;
    @ApiModelProperty(value = "字段扩展19")
    private String rfStr19;
    @ApiModelProperty(value = "字段扩展20")
    private String rfStr20;
    @ApiModelProperty(value = "数值扩展1")
    private BigDecimal rfInt1;
    @ApiModelProperty(value = "数值扩展2")
    private BigDecimal rfInt2;
    @ApiModelProperty(value = "数值扩展3")
    private BigDecimal rfInt3;
    @ApiModelProperty(value = "数值扩展4")
    private BigDecimal rfInt4;
    @ApiModelProperty(value = "时间扩展1")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rfDate1;
    @ApiModelProperty(value = "时间扩展2")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rfDate2;
    @ApiModelProperty(value = "时间扩展3")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rfDate3;
    @ApiModelProperty(value = "时间扩展4")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rfDate4;
    @ApiModelProperty(value = "时间扩展5")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rfDate5;
    @ApiModelProperty(value = "json扩展字段（XXXX，XXXX")
    private String rfFeature;
    @ApiModelProperty(value = "单据编号")
    private String billNo;
    @ApiModelProperty(value = "原始单据编号")
    private String srcBillNo;
    @ApiModelProperty(value = "关联单据编号")
    private String relatedBillNo;
    @ApiModelProperty(value = "批次")
    private String productBatch;
    @ApiModelProperty(value = "单据类型")
    private String billInfoType;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getReqDetailId() {
        return reqDetailId;
    }

    public void setReqDetailId(String reqDetailId) {
        this.reqDetailId = reqDetailId;
    }

    public String getTaskHeadId() {
        return taskHeadId;
    }

    public void setTaskHeadId(String taskHeadId) {
        this.taskHeadId = taskHeadId;
    }

    public String getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(String taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    public String getBillTypeCode() {
        return billTypeCode;
    }

    public void setBillTypeCode(String billTypeCode) {
        this.billTypeCode = billTypeCode;
    }

    public String getOperateTypeCode() {
        return operateTypeCode;
    }

    public void setOperateTypeCode(String operateTypeCode) {
        this.operateTypeCode = operateTypeCode;
    }

    public String getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(String boxNo) {
        this.boxNo = boxNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getReelid() {
        return reelid;
    }

    public void setReelid(String reelid) {
        this.reelid = reelid;
    }

    public String getTtBarcode() {
        return ttBarcode;
    }

    public void setTtBarcode(String ttBarcode) {
        this.ttBarcode = ttBarcode;
    }

    public String getLfid() {
        return lfid;
    }

    public void setLfid(String lfid) {
        this.lfid = lfid;
    }

    public String getStockTypeCode() {
        return stockTypeCode;
    }

    public void setStockTypeCode(String stockTypeCode) {
        this.stockTypeCode = stockTypeCode;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public BigDecimal getActionCode() {
        return actionCode;
    }

    public void setActionCode(BigDecimal actionCode) {
        this.actionCode = actionCode;
    }

    public Date getJournalDate() {
        return journalDate;
    }

    public void setJournalDate(Date journalDate) {
        this.journalDate = journalDate;
    }

    public BigDecimal getQtyBefore() {
        return qtyBefore;
    }

    public void setQtyBefore(BigDecimal qtyBefore) {
        this.qtyBefore = qtyBefore;
    }

    public BigDecimal getQtyAfter() {
        return qtyAfter;
    }

    public void setQtyAfter(BigDecimal qtyAfter) {
        this.qtyAfter = qtyAfter;
    }

    public BigDecimal getQtyChange() {
        return qtyChange;
    }

    public void setQtyChange(BigDecimal qtyChange) {
        this.qtyChange = qtyChange;
    }

    public BigDecimal getCanuseQtyBefore() {
        return canuseQtyBefore;
    }

    public void setCanuseQtyBefore(BigDecimal canuseQtyBefore) {
        this.canuseQtyBefore = canuseQtyBefore;
    }

    public BigDecimal getCanuseQtyAfter() {
        return canuseQtyAfter;
    }

    public void setCanuseQtyAfter(BigDecimal canuseQtyAfter) {
        this.canuseQtyAfter = canuseQtyAfter;
    }

    public BigDecimal getCanuseQtyChange() {
        return canuseQtyChange;
    }

    public void setCanuseQtyChange(BigDecimal canuseQtyChange) {
        this.canuseQtyChange = canuseQtyChange;
    }

    public String getFromSummaryId() {
        return fromSummaryId;
    }

    public void setFromSummaryId(String fromSummaryId) {
        this.fromSummaryId = fromSummaryId;
    }

    public String getToSummaryId() {
        return toSummaryId;
    }

    public void setToSummaryId(String toSummaryId) {
        this.toSummaryId = toSummaryId;
    }

    public String getToParentId() {
        return toParentId;
    }

    public void setToParentId(String toParentId) {
        this.toParentId = toParentId;
    }

    public String getJournalId() {
        return journalId;
    }

    public void setJournalId(String journalId) {
        this.journalId = journalId;
    }

    public BigDecimal getActionType() {
        return actionType;
    }

    public void setActionType(BigDecimal actionType) {
        this.actionType = actionType;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public BigDecimal getAccessSystem() {
        return accessSystem;
    }

    public void setAccessSystem(BigDecimal accessSystem) {
        this.accessSystem = accessSystem;
    }

    public String getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(String enableFlag) {
        this.enableFlag = enableFlag;
    }

    public String getRfStr01() {
        return rfStr01;
    }

    public void setRfStr01(String rfStr01) {
        this.rfStr01 = rfStr01;
    }

    public String getRfStr02() {
        return rfStr02;
    }

    public void setRfStr02(String rfStr02) {
        this.rfStr02 = rfStr02;
    }

    public String getRfStr03() {
        return rfStr03;
    }

    public void setRfStr03(String rfStr03) {
        this.rfStr03 = rfStr03;
    }

    public String getRfStr04() {
        return rfStr04;
    }

    public void setRfStr04(String rfStr04) {
        this.rfStr04 = rfStr04;
    }

    public String getRfStr05() {
        return rfStr05;
    }

    public void setRfStr05(String rfStr05) {
        this.rfStr05 = rfStr05;
    }

    public String getRfStr06() {
        return rfStr06;
    }

    public void setRfStr06(String rfStr06) {
        this.rfStr06 = rfStr06;
    }

    public String getRfStr07() {
        return rfStr07;
    }

    public void setRfStr07(String rfStr07) {
        this.rfStr07 = rfStr07;
    }

    public String getRfStr08() {
        return rfStr08;
    }

    public void setRfStr08(String rfStr08) {
        this.rfStr08 = rfStr08;
    }

    public String getRfStr09() {
        return rfStr09;
    }

    public void setRfStr09(String rfStr09) {
        this.rfStr09 = rfStr09;
    }

    public String getRfStr10() {
        return rfStr10;
    }

    public void setRfStr10(String rfStr10) {
        this.rfStr10 = rfStr10;
    }

    public String getRfStr11() {
        return rfStr11;
    }

    public void setRfStr11(String rfStr11) {
        this.rfStr11 = rfStr11;
    }

    public String getRfStr12() {
        return rfStr12;
    }

    public void setRfStr12(String rfStr12) {
        this.rfStr12 = rfStr12;
    }

    public String getRfStr13() {
        return rfStr13;
    }

    public void setRfStr13(String rfStr13) {
        this.rfStr13 = rfStr13;
    }

    public String getRfStr14() {
        return rfStr14;
    }

    public void setRfStr14(String rfStr14) {
        this.rfStr14 = rfStr14;
    }

    public String getRfStr15() {
        return rfStr15;
    }

    public void setRfStr15(String rfStr15) {
        this.rfStr15 = rfStr15;
    }

    public String getRfStr16() {
        return rfStr16;
    }

    public void setRfStr16(String rfStr16) {
        this.rfStr16 = rfStr16;
    }

    public String getRfStr17() {
        return rfStr17;
    }

    public void setRfStr17(String rfStr17) {
        this.rfStr17 = rfStr17;
    }

    public String getRfStr18() {
        return rfStr18;
    }

    public void setRfStr18(String rfStr18) {
        this.rfStr18 = rfStr18;
    }

    public String getRfStr19() {
        return rfStr19;
    }

    public void setRfStr19(String rfStr19) {
        this.rfStr19 = rfStr19;
    }

    public String getRfStr20() {
        return rfStr20;
    }

    public void setRfStr20(String rfStr20) {
        this.rfStr20 = rfStr20;
    }

    public BigDecimal getRfInt1() {
        return rfInt1;
    }

    public void setRfInt1(BigDecimal rfInt1) {
        this.rfInt1 = rfInt1;
    }

    public BigDecimal getRfInt2() {
        return rfInt2;
    }

    public void setRfInt2(BigDecimal rfInt2) {
        this.rfInt2 = rfInt2;
    }

    public BigDecimal getRfInt3() {
        return rfInt3;
    }

    public void setRfInt3(BigDecimal rfInt3) {
        this.rfInt3 = rfInt3;
    }

    public BigDecimal getRfInt4() {
        return rfInt4;
    }

    public void setRfInt4(BigDecimal rfInt4) {
        this.rfInt4 = rfInt4;
    }

    public Date getRfDate1() {
        return rfDate1;
    }

    public void setRfDate1(Date rfDate1) {
        this.rfDate1 = rfDate1;
    }

    public Date getRfDate2() {
        return rfDate2;
    }

    public void setRfDate2(Date rfDate2) {
        this.rfDate2 = rfDate2;
    }

    public Date getRfDate3() {
        return rfDate3;
    }

    public void setRfDate3(Date rfDate3) {
        this.rfDate3 = rfDate3;
    }

    public Date getRfDate4() {
        return rfDate4;
    }

    public void setRfDate4(Date rfDate4) {
        this.rfDate4 = rfDate4;
    }

    public Date getRfDate5() {
        return rfDate5;
    }

    public void setRfDate5(Date rfDate5) {
        this.rfDate5 = rfDate5;
    }

    public String getRfFeature() {
        return rfFeature;
    }

    public void setRfFeature(String rfFeature) {
        this.rfFeature = rfFeature;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getSrcBillNo() {
        return srcBillNo;
    }

    public void setSrcBillNo(String srcBillNo) {
        this.srcBillNo = srcBillNo;
    }

    public String getRelatedBillNo() {
        return relatedBillNo;
    }

    public void setRelatedBillNo(String relatedBillNo) {
        this.relatedBillNo = relatedBillNo;
    }

    public String getProductBatch() {
        return productBatch;
    }

    public void setProductBatch(String productBatch) {
        this.productBatch = productBatch;
    }

    public String getBillInfoType() {
        return billInfoType;
    }

    public void setBillInfoType(String billInfoType) {
        this.billInfoType = billInfoType;
    }
}
