package com.zte.interfaces.dto.middle;/**
 * @date 2025-04-23 23:22
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.domain.model.PsWorkOrderBasic;
import com.zte.interfaces.dto.BSProcessDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * packageName com.zte.interfaces.dto.middle
 * <AUTHOR>
 * @version JDK 8
 * @className PmRepairCommonDTO (此处以class为例)
 * @date 2025/4/23
 * @description TODO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmRepairCommonDTO {
    private List<BSProcessDTO> processList;
    private List<PsWorkOrderBasic> workOrderBasicList;
    private List<CtRouteDetailDTO> routeDetailList;
    private List<String> snList;
}
