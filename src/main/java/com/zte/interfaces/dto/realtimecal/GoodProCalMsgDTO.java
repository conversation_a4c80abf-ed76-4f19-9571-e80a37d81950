package com.zte.interfaces.dto.realtimecal;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 良品数计算DTO
 * <AUTHOR> 
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoodProCalMsgDTO implements Serializable {

    /**
     * 序列化id
     */
    private static final long serialVersionUID = 1L;

    /**
     * 换班时间
     */
    private String shiftTime;

    /**
     * 良品数计算DTO
     */
    private List<GoodProCalDTO> calList;

    @Override
    public String toString() {

        return "GoodProCalMsgDTO [shiftTime=" + shiftTime + ", calList=" + calList + "]";
    }

    public GoodProCalMsgDTO() {

        super();
    }

    public GoodProCalMsgDTO(String shiftTime, List<GoodProCalDTO> calList) {

        super();
        this.shiftTime = shiftTime;
        this.calList = calList;
    }

    public String getShiftTime() {

        return shiftTime;
    }

    public void setShiftTime(String shiftTime) {

        this.shiftTime = shiftTime;
    }

    public List<GoodProCalDTO> getCalList() {

        return calList;
    }

    public void setCalList(List<GoodProCalDTO> calList) {

        this.calList = calList;
    }

}
