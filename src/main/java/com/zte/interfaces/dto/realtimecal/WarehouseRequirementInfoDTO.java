package com.zte.interfaces.dto.realtimecal;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WarehouseRequirementInfoDTO {
    //ID
    private String requirementId;
    //需求单号
    private String billNo;
    //仓库
    private String warehouseCode;
    //子库存
    private String erpSubinventory;
    //ERP货位
    private String erpGoodsallocation;
    //物料代码
    private String itemCode;
    //物料名称
    private String itemName;
    //环保属性
    private String leadFlag;
    //数量
    private BigDecimal qty;
    //关联任务
    private String relatedTask;
    //状态（0：已提交，1：执行中，2：已完成，3：已关闭，4：已取消）
    private Integer status;
    //申请人
    private String applyPerson;
    //申请时间
    private Date applyTime;

    public WarehouseRequirementInfoDTO() {
        super();
    }

    @Override
    public String toString() {
        return "WarehouseRequirementInfo{" +
                "requirementId='" + requirementId + '\'' +
                ", billNo='" + billNo + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", erpSubinventory='" + erpSubinventory + '\'' +
                ", erpGoodsallocation='" + erpGoodsallocation + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", leadFlag='" + leadFlag + '\'' +
                ", qty=" + qty +
                ", relatedTask='" + relatedTask + '\'' +
                ", status=" + status +
                ", applyPerson='" + applyPerson + '\'' +
                ", applyTime=" + applyTime +
                '}';
    }
}
