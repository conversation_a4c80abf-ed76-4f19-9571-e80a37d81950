package com.zte.interfaces.dto.realtimecal;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 良品数计算DTO
 * <AUTHOR> 
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoodProCalDTO implements Serializable {

    /**
     * 序列化id
     */
    private static final long serialVersionUID = 1L;

    /**
     * 工厂id
     */
    private String factoryId;

    /**
     * 质检时间
     */
    private Date checkTime;

    /**
     * 任务号
     */
    private String workOrderNo;

    /**
     * 良品数
     */
    private int goodProNum;

    /**
     * 换班时间（冗余字段）
     */
    private String shiftTime;

    @Override
    public String toString() {

        return "GoodProCalDTO [factoryId=" + factoryId + ", checkTime=" + checkTime + ", workOrderNo="
                + workOrderNo + ", goodProNum=" + goodProNum + ", shiftTime=" + shiftTime + "]";
    }

    public String getFactoryId() {

        return factoryId;
    }

    public void setFactoryId(String factoryId) {

        this.factoryId = factoryId;
    }

    public Date getCheckTime() {

        return checkTime;
    }

    public void setCheckTime(Date checkTime) {

        this.checkTime = checkTime;
    }

    public String getWorkOrderNo() {

        return workOrderNo;
    }

    public void setWorkOrderNo(String workOrderNo) {

        this.workOrderNo = workOrderNo;
    }

    public int getGoodProNum() {

        return goodProNum;
    }

    public void setGoodProNum(int goodProNum) {

        this.goodProNum = goodProNum;
    }

    public String getShiftTime() {

        return shiftTime;
    }

    public void setShiftTime(String shiftTime) {

        this.shiftTime = shiftTime;
    }

}
