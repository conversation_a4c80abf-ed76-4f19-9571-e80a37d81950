package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-28 16:53
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeHeadDTO implements Serializable {
    private static final long serialVersionUID = -7939067331168652582L;
    private String headId;
    @ApiModelProperty("技改单号")
    private String chgReqNo;
    @ApiModelProperty("技改名称")
    private String chgReqName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("技改单状态")
    private String technicalStatus;
    @ApiModelProperty("状态描述")
    private String technicalStatusDesc;
    private String enabledFlag;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    private String createdBy;
    private String lastUpdatedBy;
    @ApiModelProperty("邮件接收人")
    private String emailSend;
    @ApiModelProperty("邮件操送人")
    private String emailCopy;
    @ApiModelProperty("临时BOM")
    private String tempBom;
    private String lastUpdatedDateStart;
    private String lastUpdatedDateEnd;
    private String sn;
    private String prodplanId;
    @ApiModelProperty("料单代码")
    private String productCode;
    @ApiModelProperty("主工序")
    private String craftSection;
    private String issuanceCraftSection;
    private String factoryId;
    private String empNo;
    @ApiModelProperty("编辑按钮显示")
    private Boolean editButton;
    @ApiModelProperty("执行扫描按钮显示")
    private Boolean scanButton;
    @ApiModelProperty("删除按钮显示")
    private Boolean deleteButton;
    @ApiModelProperty("查看按钮显示")
    private Boolean viewButton;
    @ApiModelProperty("来源系统")
    private String sourceSystem;
    @ApiModelProperty("原因")
    private String changeReason;

    private List<String> snList;

    private List<String> prodplanIdList;
    //没有执行记录的批次
    private List<TechnicalChangeHeadDTO> noExecList;

    private List<TechnicalChangeDetailDTO> details;

    private List<String> technicalStatusList;

}
