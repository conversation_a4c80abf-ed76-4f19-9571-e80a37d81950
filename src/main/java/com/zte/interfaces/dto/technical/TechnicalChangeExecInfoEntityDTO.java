package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.interfaces.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeExecInfoEntityDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    public enum StateEnum {
        // 确认解锁
        CONFIRMED,
        // 驳回
        REJECT;

        public static StateEnum getEnumByOrdinal(int ordinal) {
            StateEnum[] values = StateEnum.values();
            if (ordinal < 0 || ordinal >= values().length) {
                return null;
            }
            return values[ordinal];
        }
    }

    private Boolean extendSnBool;
    /**
     * 主键id
     */
    private String id;
    /**
     * 技改单号
     */
    private String chgReqNo;
    /**
     * 批次
     */
    private String prodplanId;
    /**
     * 条码
     */
    private String sn;
    /**
     * 批次列表
     */
    private List<String> prodplanIdList;
    /**
     * 条码列表
     */
    private List<String> snList;
    /**
     * 当前工序
     */
    private String currProcess;
    /**
     * 备注
     */
    private String remark;
    /**
     * $column.comments
     */
    private String enabledFlag;
    /**
     * 0 完成技改，1 无需技改 2 本工序完成技改 3 本工序无需技改
     */
    private String unlockType;
    private String unlockTypeDesc;
    /**
     * $column.comments
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    /**
     * $column.comments
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdatedDate;
    /**
     * $column.comments
     */
    private String createdBy;
    /**
     * $column.comments
     */
    private String lastUpdatedBy;
    /**
     * 解锁工序
     */
    private String unlockCraftSection;
    /**
     * 驳回原因
     */
    private String rejectionReason;
    /**
     * QC确认人
     */
    private String confirmedBy;
    /**
     * QC确认时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date confirmedDate;
    /**
     * 解锁状态 0 已提交 1 已确认 2 已驳回
     */
    private Integer unlockStatus;
    private String unlockStatusDesc;

    @ApiModelProperty(value = "开始时间")
    private String unlockStartTime;

    @ApiModelProperty(value = "结束时间")
    private String unlockEndTime;

    @ApiModelProperty(value = "需要更新解锁状态的列表")
    private List<TechnicalChangeExecInfoEntityDTO> needUpdateList;

    @ApiModelProperty(value = "需要更新解锁状态的列表")
    private List<String> idList;

    private Long num;

    private String factoryId;

    private BigDecimal organizationId;

    public BigDecimal getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(BigDecimal organizationId) {
        this.organizationId = organizationId;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public List<String> getProdplanIdList() {
        return prodplanIdList;
    }

    public void setProdplanIdList(List<String> prodplanIdList) {
        this.prodplanIdList = prodplanIdList;
    }

    public List<String> getSnList() {
        return snList;
    }

    public void setSnList(List<String> snList) {
        this.snList = snList;
    }
}
