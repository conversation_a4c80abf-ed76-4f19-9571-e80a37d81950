package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeExcelAnalysisDTO {
    private List<TechnicalChangeSnDTO> detailList;
    /**
     * 上传导入文件返回值
     */
    private String importFileId;
    private String importFileName;
    private String importFileSize;
}
