package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/10 17:26
 * @Description
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeSnDTO {
    private String detailId;
    /**
     * 条码
     */
    private String sn;
    /**
     * 当前工序（子工序）
     */
    private String currProcess;
    private String currProcessName;
    /**
     * 指令
     */
    private String workOrderNo;

    /**
     * 技改单号
     */
    private String chgReqNo;
    /**
     * 技改名称
     */
    private String chgReqName;
    /**
     * 批次
     */
    private String prodplanId;
    /**
     * 料单代码
     */
    private String productCode;
    /**
     * 料单名称
     */
    private String productName;
    /**
     * 解锁工序--主工序
     */
    private String unlockCraftSection;
    /**
     * 错误信息
     */
    private String errorMsg;
}
