package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.interfaces.dto.BarcodeLockHeadEntityDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-28 16:53
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalAndLockInfoDTO implements Serializable {
    private static final long serialVersionUID = -7939067331168652582L;

    //技改信息
    private List<TechnicalChangeHeadDTO> technicalChangeHeadDTOList;

    //锁定信息
    private List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList;

    private String prodplanId;
    /**
     * 来源工厂
     */
    private String formFactoryId;
    private String formFactoryName;

    /**
     * 目的工厂
     */
    private String toFactoryId;

    private String empNo;

}
