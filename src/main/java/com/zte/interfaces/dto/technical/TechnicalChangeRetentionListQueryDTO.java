package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/10 15:13
 * @Description
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeRetentionListQueryDTO {
    private String chgReqNo;
    private String prodplanId;
    private String productCode;
    private String craftSection;
    // 结存主工序
    private String surplusSection;
    private String sn;
    /**
     * 条码列表
     */
    private List<String> snList;
    private String unlockType;
    private List<String> excludeSnList;
    private String sourceSys;
    private String factoryId;
    private List<TechnicalChangeRetentionListQueryDTO> excludeList;

    private String remark;
    private String technicalDepartment;
    private String technicalTeam;
}
