package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeExcelImportDTO implements Serializable {
    @ApiModelProperty(value = "上传文件")
    private MultipartFile file;
    private String sourceSys;
    private String chgReqNo;
    private String prodplanId;
}
