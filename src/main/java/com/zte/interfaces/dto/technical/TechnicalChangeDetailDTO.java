package com.zte.interfaces.dto.technical;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.interfaces.technical.TechnicalHeadController;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-28 16:53
 */
@Setter
@Getter
@ColumnWidth(15)
@FieldNameConstants
@ExcelIgnoreUnannotated
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,wrapped = BooleanEnum.TRUE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalChangeDetailDTO implements Serializable {
    private static final long serialVersionUID = 8174989225532016735L;
    /**
     * 技改单号、技改单状态、批次，料单代码、料单名称、技改主工序、条码、解锁类型、解锁工序、解锁状态、执行人、执行时间、QC确认人、备注、QC确认时间
     */
    private String headId;
    private String detailId;
    @ExcelProperty(value = "技改单号", order = 1)
    private String chgReqNo;
    // 技改名称
    @ExcelProperty(value = "技改名称",order = 2 )
    private String chgReqName;
    private String technicalStatusHead;
    @ExcelProperty(value = "单据状态", order = 2)
    private String technicalStatusHeadDesc;
    @ExcelProperty(value = "批次", order = 5)
    private String prodplanId;
    /**
     * 是否已完工 Y N
     */
    @ColumnWidth(18)
    @ExcelProperty(value = "是否完工任务", order = 3)
    private String completionStatus;
    @ExcelProperty(value = "技改明细状态", order = 6)
    private String technicalStatusDesc;
    @ExcelProperty(value = "料单代码", order = 24)
    private String productCode;
    @ColumnWidth(18)
    @ExcelProperty(value = "料单名称", order = 4)
    private String productName;
    @ColumnWidth(24)
    @ExcelProperty(value = "技改文件下发工序",order = 6)
    private String issuanceCraftSection;
    @ColumnWidth(24)
    @ExcelProperty(value = "技改管控主工序", order = 8)
    private String craftSection;
    @ExcelProperty(value = "条码", order = 7)
    private String sn;
    @ExcelProperty(value = "解锁类型", order = 10)
    private String unlockTypeDesc;
    private String unlockType;
    /**
     * 技改班组
     */
    @ExcelProperty(value = "技改班组",order = 12)
    private String technicalTeam;
    /**
     * 技改部门
     */
    @ExcelProperty(value = "技改部门", order = 13)
    private String technicalDepartment;
    @ExcelProperty(value = "备注", order = 14)
    private String remark;
    @ExcelProperty(value = "解锁工序", order = 9)
    private String unlockCraftSection;
    private Integer unlockStatus;
    @ExcelProperty(value = "解锁状态", order = 11)
    private String unlockStatusDesc;
    @ExcelProperty(value = "执行人", order = 15)
    private String operator;
    @ExcelProperty(value = "执行时间", order = 16)
    @ColumnWidth(18)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date operatorDate;
    @ExcelProperty(value = "QC确认人", order = 17)
    private String confirmedBy;
    @ExcelProperty(value = "驳回原因", order = 18)
    private String rejectionReason;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @ExcelProperty(value = "QC确认时间", order = 19)
    @ColumnWidth(18)
    private Date confirmedDate;
    private String enabledFlag;
    @ExcelProperty(value = "创建人", order = 20)
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间", order = 21)
    @ColumnWidth(18)
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @ExcelProperty(value = "最后更新时间", order = 23)
    @ColumnWidth(18)
    private Date lastUpdatedDate;
    @ExcelProperty(value = "最后更新人", order = 22)
    private String lastUpdatedBy;
    @ApiModelProperty("技改状态")
    private String technicalStatus;
    private List<String> prodplanIdList;

    private List<String> snList;

    private String taskNo;
    private String factoryId;
    private String id;
    private String changeType;
    private BigDecimal changeTime;
    private String detailHisId;

    private String sourceSystem;
    //没有执行记录的批次
    private List<TechnicalChangeHeadDTO> noExecList;
    private List<String> chgReqNoList;
    /**
     * error msg
     */
    private String msg;
    private String currProcess;

    @ExcelProperty(value = "制造BOM料单", order = 3)
    private String mbomProductCode;
}

