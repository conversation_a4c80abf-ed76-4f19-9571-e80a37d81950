package com.zte.interfaces.dto.technical;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-09-28 16:53
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocFilePropertiesDTO implements Serializable {
    private static final long serialVersionUID = 6563604151178222032L;
    private String headId;
    private String docDetailId;
    private String docId;
    @ApiModelProperty("主工序")
    private String docName;
    @ApiModelProperty("0技改单，1提前转入技改确认文件，2其他文件")
    private String docType;
    @ApiModelProperty("排序")
    private Integer docSort;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private String lastUpdatedDate;
    private String createdBy;
    private String lastUpdatedBy;
}
