package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ECCpmConfigItemAssembleDTO extends CpmConfigItemAssembleDTO {

    private static final long serialVersionUID = 4447204215454590058L;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date creationDate;

    @ApiModelProperty(value = "父条码")
    private String parentItemBarcode;

    @ApiModelProperty(value = "父ID")
    private Integer parentRecordId;

}
