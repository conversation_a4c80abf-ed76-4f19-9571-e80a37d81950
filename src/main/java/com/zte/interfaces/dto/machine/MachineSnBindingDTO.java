package com.zte.interfaces.dto.machine;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.domain.model.PsWipInfo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-17 13:33
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MachineSnBindingDTO implements Serializable {
    private static final long serialVersionUID = 5208002535394715336L;
    @NotBlank(message = "工序代码不能为空")
    private String processCode;
    @NotBlank(message = "主条码不能为空")
    private String formSn;
    @Valid
    @NotNull
    @Size(min = 1, max = 50, message = "子条码个数必需在1-50")
    private List<MachineSnBindingSubDTO> childSnList;
    private String factoryId;
    private String empNo;
    private String prodplanId;
    private String taskNo;
    private String productCode;
    private PsWipInfo psWipInfo;
}
