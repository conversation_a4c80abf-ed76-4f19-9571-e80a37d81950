package com.zte.interfaces.dto.machine;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-06-17 13:33
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MachineSnBindingSubDTO implements Serializable {
    @NotBlank(message = "条码不能为空")
    private String sn;
    /**
     * 材料代码
     */
    private String subItemNo;
    /**
     * 材料代码名称
     */
    private String subItemName;
    private String itemNo;
    private String itemName;
    private String prodplanId;
    private String assembleFlag;

    //二次装配标识
    private String secAssembleFlag;
    /**
     * 存在绑定清单中
     */
    private Boolean existBindFlag = Boolean.FALSE;
}
