package com.zte.interfaces.dto.approval;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: 10328274
 * @email： <EMAIL>
 * @DateTime: 2022/6/14 19:25
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScrapResetUserDTO extends FlowBaseDTO {
    private String qualitier;
    private String workshoper;
    private String financer;
    private String qualitiyMinister;
    private String productMinister;
    private String productManager;
}
