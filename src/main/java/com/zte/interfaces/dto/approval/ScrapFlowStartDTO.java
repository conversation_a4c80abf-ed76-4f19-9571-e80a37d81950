package com.zte.interfaces.dto.approval;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: 10328274
 * @email： <EMAIL>
 * @DateTime: 2022/6/14 16:46
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScrapFlowStartDTO extends FlowBaseDTO  {
    /**
     * 员工姓名
     */
    private String empName;
    /**
     * 报废类型 1:材料问题;2:非材料问题
     */
    private String scrapType;
    /**
     * 报废类型名称
     */
    private String scrapTypeName;
    /**
     * 金额
     */
    private String amount;
    /**
     * 审批概述
     */
    private String approvalOverview;
    /**
     * 附件
     */
    private List<AppendixDTO> appendix;

    private String qualitier;
    private String workshoper;
    private String financer;
    private String qualitiyMinister;
    private String productMinister;
    private String productManager;

    private  List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList;
    /**
     * 流程代码
     */
    private String flowCode;
}
