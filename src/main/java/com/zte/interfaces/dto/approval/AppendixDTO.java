package com.zte.interfaces.dto.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 附件
 *
 * @author: 10328274
 * @email： <EMAIL>
 * @DateTime: 2022/6/13 16:16
 * @Description:
 */


@NoArgsConstructor
@Data
public class AppendixDTO {


    /**
     * uid
     */
    @ApiModelProperty("员工工号")
    private String uid;
    /**
     * sysCode
     */
    @ApiModelProperty("sysCode,默认OTC")
    private String sysCode = "OTC";
    /**
     * name
     */
    @ApiModelProperty("name,附件名称")
    private String name;
    /**
     * fileUrl
     */
    @ApiModelProperty("附件文件地址")
    private String fileUrl;
    /**
     * md5
     */
    @ApiModelProperty("文件唯一ID")
    private String md5;
    /**
     * size
     */
    @ApiModelProperty("文件大小,必须整数，单位：字节（业务系统传递，MOA统一转换为KB)")
    private String size;
}

