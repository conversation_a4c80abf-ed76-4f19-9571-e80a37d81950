package com.zte.interfaces;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.ECCfgMaterialAssemblyRelService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.ECMaterialAssemblyDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 配置物料装配关系管理Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/ecCfgMaterialAssemblyRel")
@Api(tags = "配置物料装配关系管理API")
public class ECCfgMaterialAssemblyRelController {

    private static final Logger logger = LoggerFactory.getLogger(ECCfgMaterialAssemblyRelController.class);

    @Autowired
    private ECCfgMaterialAssemblyRelService ecCfgMaterialAssemblyRelService;

    /**
     * 查询配置物料装配关系
     *
     * @param serverSnList 服务器SN列表
     * @return 物料装配关系列表
     */
    @ApiOperation("查询配置物料装配关系")
    @PostMapping("/getAssemblyRelationList")
    public ServiceData<List<ECMaterialAssemblyDTO>> getAssemblyRelationList(@RequestBody List<String> serverSnList) {
        try {
            // 数量限制校验
            if (serverSnList != null && serverSnList.size() > 5) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SERVER_SN_MAX_INPUT_LIMIT);
            }
            // 业务处理
            List<ECMaterialAssemblyDTO> assemblyList = ecCfgMaterialAssemblyRelService
                .getAssemblyRelationList(serverSnList);
            // 返回结果
            return ServiceDataBuilderUtil.success(assemblyList);

        } catch (MesBusinessException e) {
            logger.error("查询配置物料装配关系业务异常，参数：{}", JSON.toJSONString(serverSnList), e);
            return ServiceDataBuilderUtil.failure(e.getMessage());
        } catch (Exception e) {
            logger.error("查询配置物料装配关系系统异常，参数：{}", JSON.toJSONString(serverSnList), e);
            return ServiceDataBuilderUtil.failure("系统异常，请联系管理员");
        }
    }
}
