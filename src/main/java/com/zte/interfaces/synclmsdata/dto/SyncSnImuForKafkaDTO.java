package com.zte.interfaces.synclmsdata.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.domain.model.PsWipInfo;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.interfaces.dto.PageDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 00286523
 * @Date: 2022/12/1
 * @Description: TODO
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SyncSnImuForKafkaDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String empNo;

    //全部工厂最新数据
    private List<PsWipInfo> psWipInfoList;
}
