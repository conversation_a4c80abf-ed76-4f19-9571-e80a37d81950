package com.zte.interfaces.scan;

import com.zte.application.scan.FlowControlCommonService;
import com.zte.interfaces.dto.BarcodeLockDetailEntityDTO;
import com.zte.interfaces.dto.scan.BarcodeBindingDTO;
import com.zte.interfaces.dto.scan.BarcodeTestControlDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022-04-07 15:17
 */
@RestController
@ApiOperation("流程管控相关")
@RequestMapping("FlowControlCommonController")
public class FlowControlCommonController {

    @Autowired
    private FlowControlCommonService flowControlCommonService;

    @PostMapping("snLockControl")
    @ApiOperation("流程管控1 批次锁定管控")
    public ServiceData<?> snLockControl(HttpServletRequest request, @RequestBody BarcodeLockDetailEntityDTO barcodeLock) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        flowControlCommonService.snLockControl(barcodeLock);
        return ServiceDataBuilderUtil.success();
    }

    @PostMapping("snBindingControl")
    @ApiOperation("流程管控2 单板绑定管控")
    public ServiceData<?> snBindingControl(HttpServletRequest request,
                                           @RequestBody BarcodeBindingDTO barcodeBindingDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        flowControlCommonService.snBindingControl(barcodeBindingDTO);
        return ServiceDataBuilderUtil.success();
    }

    @PostMapping("/snTestControl")
    @ApiOperation("流程管控3 条码测试管控")
    public ServiceData<?> snTestControl(HttpServletRequest request,
                                        @RequestBody BarcodeTestControlDTO barcodeTestControlDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        flowControlCommonService.snTestControl(barcodeTestControlDTO);
        return ServiceDataBuilderUtil.success();
    }

    @PostMapping("/technicalControl")
    @ApiOperation("流程管控4 技改管控管控")
    public ServiceData<?> technicalControl(HttpServletRequest request,
                                           @RequestBody BarcodeBindingDTO barcodeBindingDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        flowControlCommonService.technicalControl(barcodeBindingDTO);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("条码技改管控接口")
    @PostMapping("/technicalControlBySn")
    public ServiceData<?> technicalControlBySn(HttpServletRequest request, @RequestBody BarcodeBindingDTO barcodeBindingDTO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(flowControlCommonService.technicalControlBySn(barcodeBindingDTO));
    }
}
