package com.zte.interfaces.scan;

import com.zte.application.scan.PmBillRcvScanService;
import com.zte.common.utils.ExcelName;
import com.zte.interfaces.dto.scan.PmBillRcvScanQueryConditionDTO;
import com.zte.interfaces.dto.scan.PmBillRcvVO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.RequestHeaderUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021-12-03 15:48
 */

@RestController
@ApiOperation("物料接收扫描操作类")
@RequestMapping("PmBillRcvScanController")
public class PmBillRcvScanController {
    @Autowired
    private PmBillRcvScanService pmBillRcvScanService;

    @GetMapping("scanBillNo")
    @ApiOperation("通过单据号查询扫描明细数据")
    public ServiceData scanBillNo(HttpServletRequest request, @RequestParam(value = "billNo") String billNo
            , @RequestParam(value = "refresh", required = false) Boolean refresh) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(pmBillRcvScanService.scanBillNo(billNo, refresh));
    }

    @PostMapping("scanSnOrPKGId")
    @ApiOperation("扫描SN/PKGID接口")
    public ServiceData scanSnOrPKGId(HttpServletRequest request, @RequestBody PmBillRcvVO pmBillRcvVO) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        pmBillRcvVO.setEmpNo(pair.getSecond());
        return ServiceDataBuilderUtil.success(pmBillRcvScanService.scanSnOrPKGId(pmBillRcvVO));
    }

    @PostMapping("scanPkIdCheck")
    @ApiOperation("扫描PKGID条码信息")
    public ServiceData scanPkIdCheck(HttpServletRequest request, @RequestBody PmBillRcvVO pmBillRcvVO) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(pmBillRcvScanService.scanPkIdCheck(pmBillRcvVO));
    }


    @GetMapping("refreshBillStatus")
    @ApiOperation("自动刷新单据状态定时任务")
    public ServiceData refreshBillStatus(HttpServletRequest request,
                                         @RequestParam(value = "billNo", required = false) String billNo,
                                         @RequestParam(value = "beforeDatEnd", required = false) Integer beforeDatEnd,
                                         @RequestParam(value = "beforeDateStart", required = false) Integer beforeDateStart)
            throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        pmBillRcvScanService.refreshBillStatus(billNo, pair.getFirst(), beforeDatEnd, beforeDateStart);
        return ServiceDataBuilderUtil.success();
    }


    @PostMapping("scanBillByCondition")
    @ApiOperation("通过组合查询条件查询扫描明细数据")
    public ServiceData scanBillByCondition(HttpServletRequest request, @RequestBody PmBillRcvScanQueryConditionDTO dto) throws Exception {
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        return ServiceDataBuilderUtil.success(pmBillRcvScanService.scanBillNoByCondition(dto));
    }


    /**
     * 扫描结果EXCEL导出
     **/
    @ApiOperation("扫描结果EXCEL导出")
    @GetMapping(value = "/scanBillExportExcel")
    public ServiceData scanBillExportExcel(HttpServletRequest request, HttpServletResponse response, PmBillRcvScanQueryConditionDTO dto) throws Exception {
        RequestHeaderUtil.reflectSetparam(request, SysConst.HTTP_HEADER_X_FACTORY_ID, dto.getFactoryId());
        RequestHeaderUtil.reflectSetparam(request, SysGlobalConst.HTTP_HEADER_X_EMP_NO, dto.getEmpNo());
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        pmBillRcvScanService.scanBillExportExcel(dto, response);
        return null;
    }
}
