package com.zte.interfaces.scan;

import com.zte.application.scan.CommonSnScanService;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.scan.CommonSnScanDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 提供给外部批量通用扫描接口
 *
 * <AUTHOR>
 * @date 2023-01-31 16:16
 */
@RestController
@ApiOperation("外部系统调用通用扫描")
@RequestMapping("commonSnScanCtl")
public class CommonSnScanController {
    @Autowired
    private CommonSnScanService commonSnScanService;

    @PostMapping("snScanBatch")
    @RecordLogAnnotation("ITAC 批量通用扫描服务接口")
    @OpenApi(name = "ITAC 批量通用扫描服务接口", consumer = {"ITAC","SPI过板"})
    public ServiceData<?> snScanBatch(HttpServletRequest request, @RequestBody CommonSnScanDTO commonSnScanDTO) throws Exception {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        commonSnScanDTO.setFactoryId(pair.getFirst());
        commonSnScanDTO.setEntityId(Constant.STR_2);
        commonSnScanDTO.setOperator(pair.getSecond());
        commonSnScanService.commonSnScanBatch(commonSnScanDTO);
        return ServiceDataBuilderUtil.success();
    }

}
