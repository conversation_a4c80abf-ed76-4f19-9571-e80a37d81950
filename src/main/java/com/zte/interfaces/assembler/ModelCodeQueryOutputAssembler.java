/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2020-07-20
 * 修改历史 :
 *   1. [2020-07-20] 创建文件 by 6396000509
 **/
package com.zte.interfaces.assembler;

import com.zte.domain.model.datawb.ModelCodeQueryOutput;
import com.zte.interfaces.dto.ModelCodeQueryOutputDTO;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class ModelCodeQueryOutputAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return ModelCodeQueryOutputDTO
     **/
    public static ModelCodeQueryOutputDTO toDTO(ModelCodeQueryOutput entity) {
        ModelCodeQueryOutputDTO dto = new ModelCodeQueryOutputDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return ModelCodeQueryOutput
     **/
    public static ModelCodeQueryOutput toEntity(ModelCodeQueryOutputDTO dto) {
        ModelCodeQueryOutput entity = new ModelCodeQueryOutput();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<ModelCodeQueryOutputDTO>
     **/
    public static java.util.List<ModelCodeQueryOutputDTO> toModelCodeQueryOutputDTOList(java.util.List<ModelCodeQueryOutput> entityList) {
        List<ModelCodeQueryOutputDTO> dtoList = new ArrayList<ModelCodeQueryOutputDTO>();
        for (ModelCodeQueryOutput entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<ModelCodeQueryOutput>
     **/
    public static java.util.List<ModelCodeQueryOutput> toModelCodeQueryOutputList(java.util.List<ModelCodeQueryOutputDTO> dtoList) {
        List<ModelCodeQueryOutput> entityList = new ArrayList<ModelCodeQueryOutput>();
        for (ModelCodeQueryOutputDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}