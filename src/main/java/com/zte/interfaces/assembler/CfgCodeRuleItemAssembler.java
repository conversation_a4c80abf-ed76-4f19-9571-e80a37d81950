/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2018-08-14
 * 修改历史 :
 *   1. [2018-08-14] 创建文件 by 6396000647
 **/
package com.zte.interfaces.assembler;

import com.zte.domain.model.datawb.CfgCodeRuleItem;
import com.zte.interfaces.dto.CfgCodeRuleItemDTO;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class CfgCodeRuleItemAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return CfgCodeRuleItemDTO
     **/
    public static CfgCodeRuleItemDTO toDTO(CfgCodeRuleItem entity) {
        CfgCodeRuleItemDTO dto = new CfgCodeRuleItemDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return CfgCodeRuleItem
     **/
    public static CfgCodeRuleItem toEntity(CfgCodeRuleItemDTO dto) {
        CfgCodeRuleItem entity = new CfgCodeRuleItem();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<CfgCodeRuleItemDTO>
     **/
    public static java.util.List<CfgCodeRuleItemDTO> toCfgCodeRuleItemDTOList(java.util.List<CfgCodeRuleItem> entityList) {
        List<CfgCodeRuleItemDTO> dtoList = new ArrayList<CfgCodeRuleItemDTO>();
        for (CfgCodeRuleItem entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<CfgCodeRuleItem>
     **/
    public static java.util.List<CfgCodeRuleItem> toCfgCodeRuleItemList(java.util.List<CfgCodeRuleItemDTO> dtoList) {
        List<CfgCodeRuleItem> entityList = new ArrayList<CfgCodeRuleItem>();
        for (CfgCodeRuleItemDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}