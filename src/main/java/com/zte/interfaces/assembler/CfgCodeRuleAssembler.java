/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2018-08-14
 * 修改历史 :
 *   1. [2018-08-14] 创建文件 by 6396000647
 **/
package com.zte.interfaces.assembler;

import com.zte.domain.model.datawb.CfgCodeRule;
import com.zte.interfaces.dto.CfgCodeRuleDTO;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.BeanUtils;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
public class CfgCodeRuleAssembler {

    /**
     * 添加方法功能描述
     * @param entity
     * @return CfgCodeRuleDTO
     **/
    public static CfgCodeRuleDTO toDTO(CfgCodeRule entity) {
        CfgCodeRuleDTO dto = new CfgCodeRuleDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 添加方法功能描述
     * @param dto
     * @return CfgCodeRule
     **/
    public static CfgCodeRule toEntity(CfgCodeRuleDTO dto) {
        CfgCodeRule entity = new CfgCodeRule();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 添加方法功能描述
     * @param entityList
     * @return List<CfgCodeRuleDTO>
     **/
    public static java.util.List<CfgCodeRuleDTO> toCfgCodeRuleDTOList(java.util.List<CfgCodeRule> entityList) {
        List<CfgCodeRuleDTO> dtoList = new ArrayList<CfgCodeRuleDTO>();
        for (CfgCodeRule entity : entityList) { 
        	dtoList.add(toDTO(entity));
    }
    return dtoList;
}

    /**
     * 添加方法功能描述
     * @param dtoList
     * @return List<CfgCodeRule>
     **/
    public static java.util.List<CfgCodeRule> toCfgCodeRuleList(java.util.List<CfgCodeRuleDTO> dtoList) {
        List<CfgCodeRule> entityList = new ArrayList<CfgCodeRule>();
        for (CfgCodeRuleDTO dto : dtoList) { 
        	entityList.add(toEntity(dto));
    }
    return entityList;
}
}