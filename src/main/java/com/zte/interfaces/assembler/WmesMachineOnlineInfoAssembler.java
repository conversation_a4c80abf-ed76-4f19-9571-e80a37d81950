package com.zte.interfaces.assembler;

import com.zte.domain.model.datawb.WmesMachineOnlineInfo;
import com.zte.interfaces.dto.WmesMachineOnlineInfoDTO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class WmesMachineOnlineInfoAssembler {

    public static WmesMachineOnlineInfoDTO toDTO(WmesMachineOnlineInfo entity) {
        WmesMachineOnlineInfoDTO dto = new WmesMachineOnlineInfoDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }


    public static WmesMachineOnlineInfo toEntity(WmesMachineOnlineInfoDTO dto) {
        WmesMachineOnlineInfo entity = new WmesMachineOnlineInfo();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    public static java.util.List<WmesMachineOnlineInfoDTO> toWmesMachineOnlineInfoDTOList(java.util.List<WmesMachineOnlineInfo> entityList) {
        List<WmesMachineOnlineInfoDTO> dtoList = new ArrayList<>();
        for (WmesMachineOnlineInfo entity : entityList) {
            dtoList.add(toDTO(entity));
        }
        return dtoList;
    }

    public static java.util.List<WmesMachineOnlineInfo> toWmesMachineOnlineInfoList(java.util.List<WmesMachineOnlineInfoDTO> dtoList) {
        List<WmesMachineOnlineInfo> entityList = new ArrayList<>();
        for (WmesMachineOnlineInfoDTO dto : dtoList) {
            entityList.add(toEntity(dto));
        }
        return entityList;
    }
}
