-- 清理无用备份表
set lock_timeout='10000ms';
drop table if exists wip_scan_his_extra_bak;
drop table if exists b_smt_bom_detail_0325;
drop table if exists barcode_lock_detail_220919;
drop table if exists bs_common_scan_user_220919;
drop table if exists daily_work_amount_stat_220919;
drop table if exists exception_skip_info_220919;
drop table if exists pm_repair_detail_v12023;
drop table if exists pm_repair_info_v12023;
drop table if exists pm_repair_rcv_bak0910;
drop table if exists pm_repair_rcv_detail_1225;
drop table if exists pm_repair_rcv_detail_220919;
drop table if exists pm_repair_rcv_detail_v12023;
drop table if exists pm_repair_rcv_v12023;
drop table if exists prod_binding_setting_20241115;
drop table if exists prod_binding_setting_20241127;
drop table if exists prod_binding_setting_220919;
drop table if exists scrap_bill_detail_220919;
drop table if exists tmp_1210_del_zjsn;
drop table if exists warehouse_entry_detail_bak1016;
drop table if exists wip_extend_identification_220919;
drop table if exists wip_info_0328;
drop table if exists wip_info_20190107;
drop table if exists wip_info_bak1226;
drop table if exists wip_info_bk20181016;
drop table if exists wip_info_template_20200921;
drop table if exists wip_info_tmp1924;
drop table if exists wip_info_tmp_2;
drop table if exists wip_scan_history_220919;
drop table if exists wip_scan_history_bak12261;
drop table if exists wip_scan_history_bak202201171;
drop table if exists wip_scan_history_bak202201172;
drop table if exists wip_scan_history_bak202201173;
drop table if exists wip_scan_history_old;
drop table if exists wip_scan_history_tmp;
drop table if exists wip_scan_history_tmp1;
drop table if exists wip_test_recode_220919;
drop table if exists workorder_online_tmp1231;
drop table if exists zte_lms_mtl_0523;
drop table if exists zte_lms_mtl_0829;
drop table if exists zte_lms_mtl_temp191128;
drop table if exists zte_lms_mtl_transactions_0530;
