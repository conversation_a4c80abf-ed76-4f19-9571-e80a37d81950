CREATE table if not exists collection_code_scan (
	code_scan_id varchar(64) NOT NULL,
	master_sn varchar(64) NOT NULL,
	sub_sn varchar(64) NOT NULL,
	master_item_code varchar(64) NOT NULL,
	sub_item_code varchar(64) NOT NULL,
	master_lead_type varchar(64) NULL,
	sub_lead_type varchar(64) NULL,
	quantity int4 NOT NULL,
	enabled_flag varchar(1) NOT NULL,
	created_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	created_by varchar(32) NOT NULL,
	last_updated_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	last_updated_by varchar(32) NOT NULL,
	CONSTRAINT pk_collection_code_scan_id PRIMARY KEY (code_scan_id)
);
CREATE INDEX if not exists idx_last_updated_date ON collection_code_scan USING btree (last_updated_date);
CREATE INDEX if not exists idx_master_sn ON collection_code_scan USING btree (master_sn, sub_sn);
CREATE INDEX if not exists idx_sub_sn ON collection_code_scan USING btree (sub_sn, master_sn);

COMMENT ON TABLE collection_code_scan IS '集合码扫描表';

-- Column comments
COMMENT ON COLUMN collection_code_scan.code_scan_id IS '主键';
COMMENT ON COLUMN collection_code_scan.master_sn IS '主条码';
COMMENT ON COLUMN collection_code_scan.sub_sn IS '子条码';
COMMENT ON COLUMN collection_code_scan.master_item_code IS '主物料代码';
COMMENT ON COLUMN collection_code_scan.sub_item_code IS '子物料代码';
COMMENT ON COLUMN collection_code_scan.master_lead_type IS '主条码环保属性';
COMMENT ON COLUMN collection_code_scan.sub_lead_Type IS '子条码环保属性';
COMMENT ON COLUMN collection_code_scan.quantity IS '数量';
COMMENT ON COLUMN collection_code_scan.enabled_flag IS '有效标记';
COMMENT ON COLUMN collection_code_scan.created_date IS '创建时间';
COMMENT ON COLUMN collection_code_scan.created_by IS '创建人';
COMMENT ON COLUMN collection_code_scan.last_updated_date IS '最后更新时间';
COMMENT ON COLUMN collection_code_scan.last_updated_by IS '最后更新人';



