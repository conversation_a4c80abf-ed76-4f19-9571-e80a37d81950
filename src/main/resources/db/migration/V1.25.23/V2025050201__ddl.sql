-- cs_sys_productionmgmt.task_reconfiguration_record definition

-- Drop table

-- DROP TABLE cs_sys_productionmgmt.task_reconfiguration_record;

CREATE TABLE if not exists task_reconfiguration_record (
	id varchar(64) NOT NULL,
	task_no varchar(240) NOT NULL,
	item_code varchar(64) NOT NULL,
	item_barcode varchar(64) NOT NULL,
	enabled_flag varchar(1) DEFAULT 'Y'::character varying NOT NULL,
	create_date timestamp DEFAULT statement_timestamp()::date NULL,
	create_by varchar(32) NULL,
	last_updated_date timestamp DEFAULT statement_timestamp()::date NULL,
	last_updated_by varchar(32) NULL,
	operation_type int4 NOT NULL,
	quantity int4 NOT NULL,
	sn varchar(64) NOT NULL,
	CONSTRAINT task_reconfiguration_record_pk PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_trr_item_barcode ON task_reconfiguration_record USING btree (item_barcode, item_code);
CREATE INDEX if not exists idx_trr_item_code ON task_reconfiguration_record USING btree (item_code, item_barcode);
CREATE INDEX if not exists idx_trr_last_updated_date ON task_reconfiguration_record USING btree (last_updated_date);
CREATE INDEX if not exists idx_trr_task_no ON task_reconfiguration_record USING btree (task_no);
CREATE INDEX if not exists idx_trr_sn ON task_reconfiguration_record USING btree (sn);

COMMENT ON TABLE task_reconfiguration_record IS '任务条码改配记录表';
-- 添加字段注释
COMMENT ON COLUMN task_reconfiguration_record.task_no IS '任务号';
COMMENT ON COLUMN task_reconfiguration_record.item_code IS '物料代码';
COMMENT ON COLUMN task_reconfiguration_record.item_barcode IS '物料条码';
COMMENT ON COLUMN task_reconfiguration_record.operation_type IS '操作类型（1拆解 2装配）';
COMMENT ON COLUMN task_reconfiguration_record.quantity IS '数量';
COMMENT ON COLUMN task_reconfiguration_record.sn IS '条码';