CREATE table if not exists box_scan_record (
	id varchar(50) NOT NULL, -- 主键ID
    box_no varchar(100) NOT NULL, -- 箱码
    barcode varchar(100) NOT NULL, -- 物料条码
    item_code varchar(50) NOT NULL, -- 物料代码
    item_name varchar(300) NULL, -- 物料名称
    qty numeric(10,4) NOT NULL, -- 数量
    unit varchar(200) NULL, -- 单位
    env_attr varchar(10) NULL, -- 环保属性
    source_batch_no varchar(50) NULL, -- 来源批次号¶
    task_no varchar(240) NULL, -- 任务
    "rule" varchar(200) NULL, -- 装箱规则
    header_id varchar(50)  null,
    create_date timestamp NULL, -- 扫描时间
    create_by varchar(12) NULL, -- 扫描人
    last_updated_date timestamp NULL, -- 修改日期
    last_updated_by varchar(20) NULL, -- 修改人
    enabled_flag varchar(1) NULL DEFAULT 'Y'::text, -- 是否有效
    CONSTRAINT box_scan_record_pkey PRIMARY KEY (id)
);
CREATE index if not exists idx_box_scan_barcode ON box_scan_record USING btree (barcode,enabled_flag);
CREATE INDEX if not exists idx_box_scan_box_no ON box_scan_record USING btree (box_no,enabled_flag);
CREATE index if not exists idx_box_scan_create_date ON box_scan_record USING btree (create_date);
COMMENT ON TABLE box_scan_record IS '装箱扫描记录';

-- Column comments

COMMENT ON COLUMN box_scan_record.id IS '主键ID';
COMMENT ON COLUMN box_scan_record.box_no IS '箱码';
COMMENT ON COLUMN box_scan_record.barcode IS '物料条码';
COMMENT ON COLUMN box_scan_record.item_code IS '物料代码';
COMMENT ON COLUMN box_scan_record.item_name IS '物料名称';
COMMENT ON COLUMN box_scan_record.qty IS '数量';
COMMENT ON COLUMN box_scan_record.unit IS '单位';
COMMENT ON COLUMN box_scan_record.env_attr IS '环保属性';
COMMENT ON COLUMN box_scan_record.source_batch_no IS '来源批次号';
COMMENT ON COLUMN box_scan_record.task_no IS '任务';
COMMENT ON COLUMN box_scan_record.create_date IS '扫描时间';
COMMENT ON COLUMN box_scan_record.create_by IS '扫描人';
COMMENT ON COLUMN box_scan_record.last_updated_date IS '修改日期';
COMMENT ON COLUMN box_scan_record.last_updated_by IS '修改人';
COMMENT ON COLUMN box_scan_record.enabled_flag IS '是否有效';
COMMENT ON COLUMN box_scan_record.rule IS '装箱规则';



