SET lock_timeout = '5000ms';

ALTER TABLE pm_repair_detail ADD COLUMN if not exists repair_team varchar(64);
COMMENT ON COLUMN pm_repair_detail.repair_team IS '维修班组';

-- 整机重量信息
CREATE TABLE if not exists pm_machine_weight (
	sn varchar(64) NOT NULL, -- 整机条码(主键)
	task_no varchar(240) NOT NULL, -- 任务号
	weight varchar(64) NOT NULL, -- 重量（单位：kg）
	create_by varchar(32) NOT NULL DEFAULT '', -- 创建人
    create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 创建日期
    last_updated_by varchar(32) NOT NULL DEFAULT '', -- 修改人
    last_updated_date timestamp not NULL DEFAULT LOCALTIMESTAMP, -- 修改日期
	enabled_flag varchar(1) not NULL DEFAULT 'Y'::character varying, -- 是否有效
	CONSTRAINT pk_pm_machine_weight PRIMARY KEY (sn)
);

CREATE INDEX if not exists idx_pmw_task_no ON pm_machine_weight USING btree (task_no);
CREATE INDEX if not exists idx_pmw_last_updated_date ON pm_machine_weight USING btree (last_updated_date);

COMMENT ON TABLE pm_machine_weight IS '整机重量信息';

COMMENT ON COLUMN pm_machine_weight.sn IS '整机条码(主键)';
COMMENT ON COLUMN pm_machine_weight.task_no IS '任务号';
COMMENT ON COLUMN pm_machine_weight.weight IS '重量（单位：kg）';