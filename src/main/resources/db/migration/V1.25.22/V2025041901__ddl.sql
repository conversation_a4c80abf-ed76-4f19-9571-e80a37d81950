-- pm_org_transfer_order definition

-- Drop table

-- DROP TABLE pm_org_transfer_order;

CREATE TABLE if not exists pm_org_transfer_order (
	id varchar(64) NOT NULL, -- 主键
	transfer_order_number varchar(255) NOT NULL, -- 转移单号
	bill_type varchar(255) NOT NULL, -- 单据类型
	bill_status varchar(255) NOT NULL, -- 单据状态
	source_warehouse varchar(255) NOT NULL, -- 源仓库
	target_warehouse varchar(255) NOT NULL, -- 目的仓库
	source_erp_location varchar(255) NULL, -- 源ERP货位
	target_erp_location varchar(255) NULL, -- 目的ERP货位
	source_erp_org varchar(255) NULL, -- 源ERP组织
	target_erp_org varchar(255) NULL, -- 目的ERP组织
	item_code varchar(255) NULL, -- 物料代码
	transfer_quantity int8 NULL, -- 转移数量
	submited_date timestamp NULL, -- 提交时间
	submited_by varchar(32) NULL, -- 提交人
	created_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
	created_by varchar(32) NOT NULL, -- 创建人
	last_updated_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间
	last_updated_by varchar(32) NOT NULL, -- 最后更新人
	enabled_flag bpchar(1) NOT NULL DEFAULT 'Y'::bpchar, -- 有效标记
	tenant_id int8 NOT NULL DEFAULT 10001, -- 租户id
	CONSTRAINT pm_org_transfer_order_pkey PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_item_code ON pm_org_transfer_order USING btree (item_code);
CREATE INDEX if not exists idx_source_warehouse ON pm_org_transfer_order USING btree (source_warehouse);
CREATE INDEX if not exists idx_transfer_order_number ON pm_org_transfer_order USING btree (transfer_order_number);
COMMENT ON TABLE pm_org_transfer_order IS '组织转移单表';

-- Column comments

COMMENT ON COLUMN pm_org_transfer_order.id IS '主键';
COMMENT ON COLUMN pm_org_transfer_order.transfer_order_number IS '转移单号';
COMMENT ON COLUMN pm_org_transfer_order.bill_type IS '单据类型';
COMMENT ON COLUMN pm_org_transfer_order.bill_status IS '单据状态';
COMMENT ON COLUMN pm_org_transfer_order.source_warehouse IS '源仓库';
COMMENT ON COLUMN pm_org_transfer_order.target_warehouse IS '目的仓库';
COMMENT ON COLUMN pm_org_transfer_order.source_erp_location IS '源ERP货位';
COMMENT ON COLUMN pm_org_transfer_order.target_erp_location IS '目的ERP货位';
COMMENT ON COLUMN pm_org_transfer_order.source_erp_org IS '源ERP组织';
COMMENT ON COLUMN pm_org_transfer_order.target_erp_org IS '目的ERP组织';
COMMENT ON COLUMN pm_org_transfer_order.item_code IS '物料代码';
COMMENT ON COLUMN pm_org_transfer_order.transfer_quantity IS '转移数量';
COMMENT ON COLUMN pm_org_transfer_order.submited_date IS '提交时间';
COMMENT ON COLUMN pm_org_transfer_order.submited_by IS '提交人';
COMMENT ON COLUMN pm_org_transfer_order.created_date IS '创建时间';
COMMENT ON COLUMN pm_org_transfer_order.created_by IS '创建人';
COMMENT ON COLUMN pm_org_transfer_order.last_updated_date IS '最后更新时间';
COMMENT ON COLUMN pm_org_transfer_order.last_updated_by IS '最后更新人';
COMMENT ON COLUMN pm_org_transfer_order.enabled_flag IS '有效标记';
COMMENT ON COLUMN pm_org_transfer_order.tenant_id IS '租户id';