-- 创建生产管理-维修异常审批表
CREATE TABLE if not exists repair_approval (
    id VARCHAR(64) NOT NULL,
    approval_type VARCHAR(1) NOT NULL,
    barcode VARCHAR(128) NOT NULL,
    position_number VARCHAR(128),
    application_reason VARCHAR(2000) NOT NULL,
    approval_no varchar(64) NOT NULL DEFAULT '',
    status varchar(10) NOT NULL, -- 审批单状态
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(32) NOT NULL,
    last_updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_by VARCHAR(32) NOT NULL,
    enabled_flag CHAR(1) NOT NULL DEFAULT 'Y',
    CONSTRAINT pk_repair_approval PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE repair_approval IS '生产管理-维修异常审批表';

-- 添加字段注释
COMMENT ON COLUMN repair_approval.id IS '主键';
COMMENT ON COLUMN repair_approval.approval_type IS '审批单类型：0送修审批、1维修审批';
COMMENT ON COLUMN repair_approval.barcode IS '条码';
COMMENT ON COLUMN repair_approval.position_number IS '位号';
COMMENT ON COLUMN repair_approval.application_reason IS '申请原因';
COMMENT ON COLUMN repair_approval.approval_no IS '审批单号';
COMMENT ON COLUMN repair_approval.status IS '审批单状态';
COMMENT ON COLUMN repair_approval.created_date IS '创建时间';
COMMENT ON COLUMN repair_approval.created_by IS '创建人';
COMMENT ON COLUMN repair_approval.last_updated_date IS '最后更新时间';
COMMENT ON COLUMN repair_approval.last_updated_by IS '最后更新人';
COMMENT ON COLUMN repair_approval.enabled_flag IS '有效标记';

-- 创建索引
CREATE INDEX if not exists idx_repair_approval_barcode ON repair_approval (barcode);
CREATE INDEX if not exists idx_repair_approval_created_date ON repair_approval (created_date);