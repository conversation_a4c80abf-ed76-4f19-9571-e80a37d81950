<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="OERP_M04_ImportMaterialTrxInfoSvr" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" name="OERP_M04_ImportMaterialTrxInfoSvrService" targetNamespace="OERP_M04_ImportMaterialTrxInfoSvr" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s0="http://tempuri.org/encodedTypes">
    <wsdl:types>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="OERP_M04_ImportMaterialTrxInfoSvr">
            <xs:element name="ImportMaterialTrxInfoSvrRequest" type="tns:ImportMaterialTrxInfoSvrRequest"/>
            <xs:element name="ImportMaterialTrxInfoSvrResponse" type="tns:ImportMaterialTrxInfoSvrResponse"/>
            <xs:element name="fault" type="tns:fault"/>
            <xs:complexType name="ObjectOfMsgHeader">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_SYSTEM_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_SYSTEM_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="USER_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="USER_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SUBMIT_DATE" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayOfInput">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="PK_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="API_VERSION" type="xs:int"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_TYPE_ID" type="xs:int"/>
                    <xs:element minOccurs="0" nillable="true" name="ACCOUNT_ALIAS" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="WIP_ENTITY_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ORGANIZATION_ID" type="xs:int"/>
                    <xs:element minOccurs="0" nillable="true" name="ORGANIZATION_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SUBINVENTORY_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="LOCATOR_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="LOCATOR_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="ITEM_NUMBER" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="INVENTORY_ITEM_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="ITEM_REVISION" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_QUANTITY" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_DATE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSFER_ORGANIZATION_ID" type="xs:int"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSFER_ORGANIZATION_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSFER_SUBINVENTORY" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSFER_LOCATOR" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSFER_LOCATOR_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="REFRESH_PRICE_FLAG" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="UNIT_PRICE" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_REFERENCE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_HEADER_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_LINE_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="TRX_SOURCE_LINE_ID" type="xs:decimal"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_MODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="EMPLOYEE_NUMBER" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="UOM_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE_CATEGORY" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE1" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE2" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE3" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE4" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE5" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE6" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE7" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE8" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE9" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE10" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE11" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE12" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE13" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE14" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE15" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="P_PARAMETER1" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="P_PARAMETER2" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="P_PARAMETER3" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="P_PARAMETER4" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="P_PARAMETER5" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfInputRec">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" nillable="true" name="Input" type="tns:ArrayOfInput"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ImportMaterialTrxInfoSvrRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="MsgHeader" type="tns:ObjectOfMsgHeader"/>
                    <xs:element minOccurs="0" nillable="true" name="InputRec" type="tns:ObjectOfInputRec"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfOutput">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="RETURN_STATUS" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayOfError">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="ENTITY_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ERROR_MESSAGE" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfErrorRec">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" nillable="true" name="Error" type="tns:ArrayOfError"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ImportMaterialTrxInfoSvrResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="Output" type="tns:ObjectOfOutput"/>
                    <xs:element minOccurs="0" nillable="true" name="ErrorRec" type="tns:ObjectOfErrorRec"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="fault">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="faultcode" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="faultstring" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="faultactor" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="detail" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="ImportMaterialTrxInfoSvrRequest">
        <wsdl:part element="tns:ImportMaterialTrxInfoSvrRequest" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ImportMaterialTrxInfoSvrResponse">
        <wsdl:part element="tns:ImportMaterialTrxInfoSvrResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="fault">
        <wsdl:part element="tns:fault" name="fault">
        </wsdl:part>
    </wsdl:message>
    <wsdl:portType name="OERP_M04_ImportMaterialTrxInfoSvrPort">
        <wsdl:operation name="process">
            <wsdl:input message="tns:ImportMaterialTrxInfoSvrRequest" name="ImportMaterialTrxInfoSvrRequest"/>
            <wsdl:output message="tns:ImportMaterialTrxInfoSvrResponse" name="ImportMaterialTrxInfoSvrResponse"/>
            <wsdl:fault message="tns:fault" name="fault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="OERP_M04_ImportMaterialTrxInfoSvrBinding" type="tns:OERP_M04_ImportMaterialTrxInfoSvrPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="process">
            <soap:operation soapAction="process" style="document"/>
            <wsdl:input name="ImportMaterialTrxInfoSvrRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="ImportMaterialTrxInfoSvrResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="OERP_M04_ImportMaterialTrxInfoSvrService">
        <wsdl:port binding="tns:OERP_M04_ImportMaterialTrxInfoSvrBinding" name="OERP_M04_ImportMaterialTrxInfoSvrPort">
            <soap:address location="https://*************:2443/composerapi/soa-infra/services/OERP_M04_ImportMaterialTrxInfoSvr"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>