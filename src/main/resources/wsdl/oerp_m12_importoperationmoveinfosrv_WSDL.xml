<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="OERP_M12_ImportOperationMoveInfoSrv" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" name="OERP_M12_ImportOperationMoveInfoSrvService" targetNamespace="OERP_M12_ImportOperationMoveInfoSrv" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s0="http://tempuri.org/encodedTypes">
    <wsdl:types>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="OERP_M12_ImportOperationMoveInfoSrv">
            <xs:element name="OERP_M12_ImportOperationMoveInfoSrvRequest" type="tns:OERP_M12_ImportOperationMoveInfoSrvRequest"/>
            <xs:element name="OERP_M12_ImportOperationMoveInfoSrvResponse" type="tns:OERP_M12_ImportOperationMoveInfoSrvResponse"/>
            <xs:element name="fault" type="tns:fault"/>
            <xs:complexType name="ObjectOfMsgHeader">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_SYSTEM_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_SYSTEM_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="USER_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="USER_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SUBMIT_DATE" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayOfInputItem">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="PK_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ORGANIZATION_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ORGANIZATION_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="WIP_ENTITY_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSCATION_QTY" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_UOM" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_DATE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="FM_OPERATION_SEQ_NUM" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="FM_INTRAOPERATION_STEP_TYPE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TO_OPERATION_SEQ_NUM" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="LAST_OPERATION_FLAG" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TO_INTRAOPERATION_STEP_TYPE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="REASON" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="TRANSACTION_REFERENCE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="SOURCE_CODE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE_CATEGORY" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE1" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE2" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE3" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE4" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE5" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE6" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE7" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE8" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE9" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE10" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE11" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE12" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE13" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE14" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ATTRIBUTE15" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED1" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED2" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED3" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED4" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED5" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED6" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED7" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED8" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED9" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RESERVED10" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfInputCollection">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" nillable="true" name="InputItem" type="tns:ArrayOfInputItem"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="OERP_M12_ImportOperationMoveInfoSrvRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="MsgHeader" type="tns:ObjectOfMsgHeader"/>
                    <xs:element minOccurs="0" nillable="true" name="InputCollection" type="tns:ObjectOfInputCollection"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayOfResponseItem">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="REQUEST_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="RECORD_NUMBER" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfResponseCollection">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" nillable="true" name="ResponseItem" type="tns:ArrayOfResponseItem"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayOfErrorItem">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="RECORD_NUMBER" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ENTITY_NAME" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ERROR_MESSAGE" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ObjectOfErrorCollection">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="unbounded" nillable="true" name="ErrorItem" type="tns:ArrayOfErrorItem"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="OERP_M12_ImportOperationMoveInfoSrvResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="PROCESS_STATUS" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="PROCESS_MESSAGE" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="INSTANCE_ID" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="ResponseCollection" type="tns:ObjectOfResponseCollection"/>
                    <xs:element minOccurs="0" nillable="true" name="ErrorCollection" type="tns:ObjectOfErrorCollection"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="fault">
                <xs:sequence>
                    <xs:element minOccurs="0" nillable="true" name="faultcode" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="faultstring" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="faultactor" type="xs:string"/>
                    <xs:element minOccurs="0" nillable="true" name="detail" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </wsdl:types>
    <wsdl:message name="OERP_M12_ImportOperationMoveInfoSrvRequest">
        <wsdl:part element="tns:OERP_M12_ImportOperationMoveInfoSrvRequest" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="OERP_M12_ImportOperationMoveInfoSrvResponse">
        <wsdl:part element="tns:OERP_M12_ImportOperationMoveInfoSrvResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="fault">
        <wsdl:part element="tns:fault" name="fault">
        </wsdl:part>
    </wsdl:message>
    <wsdl:portType name="OERP_M12_ImportOperationMoveInfoSrvPort">
        <wsdl:operation name="process">
            <wsdl:input message="tns:OERP_M12_ImportOperationMoveInfoSrvRequest" name="OERP_M12_ImportOperationMoveInfoSrvRequest"/>
            <wsdl:output message="tns:OERP_M12_ImportOperationMoveInfoSrvResponse" name="OERP_M12_ImportOperationMoveInfoSrvResponse"/>
            <wsdl:fault message="tns:fault" name="fault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="OERP_M12_ImportOperationMoveInfoSrvBinding" type="tns:OERP_M12_ImportOperationMoveInfoSrvPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="process">
            <soap:operation soapAction="process" style="document"/>
            <wsdl:input name="OERP_M12_ImportOperationMoveInfoSrvRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="OERP_M12_ImportOperationMoveInfoSrvResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault name="fault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="OERP_M12_ImportOperationMoveInfoSrvService">
        <wsdl:port binding="tns:OERP_M12_ImportOperationMoveInfoSrvBinding" name="OERP_M12_ImportOperationMoveInfoSrvPort">
            <soap:address location="https://*************:2443/composerapi/soa-infra/services/OERP/OERP_M12_ImportOperationMoveInfoSrv"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>