RetCode.Success=Success
RetCode.ServerError=Server Error
RetCode.AuthFailed=Auth Failed
RetCode.PermissionDenied=Permission Denied
RetCode.ValidationError=Validation Error
RetCode.BusinessError=Business Error
customize.msg={0}
Please.Input.WareHouse=Please Input WareHouse
Please.Input.OrderKey.WaveKey=Please Input OrderKey WaveKey
Please.Input.Id=Please Input Id
Please.Input.OrderKey.ExternalOrderKey2.WaveKey.Id=Please Input OrderKey ExternalOrderKey2 WaveKey Id
Please.Input.OrderKey.ExternalOrderKey2=Please Input OrderKey ExternalOrderKey2
Please.Import.Excel.NoMoreThan.Contractnumber.Fifty=Please Check Excel File Contractnumber No More Than Fifty
Please.Import.Excel.NoMoreThan.Doid.FiveHundred=Please Check Excel File Doid No More Than Five Hundred
Please.Import.Excel.NoMoreThan.BillNumber.FiveHundred=Please Check Excel File BillNumber No More Than Five Hundred
Please.Check.Import.File.Type.Excel=Please Import File Type Of Excel
board.not.belong=This board does not belong to this component
module.barcode.not.exists=This module barcode not exists
workorder.sourcesys.is.null=workorder sourcesys is null
main.sn.prodplan.is.null=main sn prodplan is null
sub.sn.prodplan.is.null = sub sn prodplan is null
sub.sn.is.null = sub sn is null
main.sn.is.null = main sn is null
get_board_assembly_relationship_error= Obtain the sub-barcode of the main bar code. Error :

empty.input.contract.eg=ContractNumber Or ItemCodes Or ItemName Or ItemBarcode. At Least Input One
only.itemname=Please Input More Infomation Or DeliverDay In Ten Days. Can Not Only Input ItemName


data.wb.call.param.miss=data wb call param  {0}  miss
emp.no.is.null=emp no is null
time.format.error=dataTime format error
sfc.return.cur.value.is.null=sfc return cur value is null
sfc.return.cur.value.is.not.number=sfc return cur value is not number
sfc.return.cur.value.is.more.than.max=sfc return cur value is more than max
Please.Input.query.condition= Please Input Condition
no.data.found=no data found
Please.Input.BillNumber=Please Input BillNumber
Query.More.BillNumber.box=Query more BillNumber
Query.More.BillNumber=Query more BillNumber
item.id.found.error=item id not found
wip.move.mtl.insert.error = ZTE_WIP_MOVE_MTL_TXN insert error
mrp.wip.issue.insert.eror = ZTE_MRP_WIP_ISSUE insert error

task.no.is.null=task no can't be null
update_step_stock_locked=Updating STEP barcode and material stock is executing
delete_param_error=The month parameter must be non-negative before deleting months
itembarcode_stockno_is_null=Barcode and in-transit sub-stock cannot be empty
itembarcode_not_exist=Barcode does not exist

boxNo.no.is.null= box code no can't be null
itemType.no.is.null=item type no can't be null
itemName.no.is.null=item name no can't be null
itemCode.no.is.null=item code no can't be null
MFGSITE.ID.IS.NULL=mfg site id can't be null
CREATE.BARCODE.QTY.BETWEEN.1.AND.50= create barcode qry between 1 and 50
SEARCH.IS.NULL.BY.ENTITYNAME.MFGSITEID.ITEMCODE.LAYER1.LAYER4=SEARCH IS NULL BY ENTITYNAME MFGSITEID ITEMCODE LAYER1 LAYER4
CREATE.BARCODE.FAILURE=CREATE BARCODE FAILURE
PTO.ITEMCODE.IS_NOT.PDVM=ONE LAYER ITEMCODE IS NOT PDVM
FOURE.LAYER.ITEMCODE.DO.NOT.MARK=FOURE LAYER ITEMCODE DO NOT MARK
data.is.null=data is null
month.compute.time=No operation at the end of the month
barcode.is.no.stock=Barcode does not exist in barcode inventory table
asset.no.stock=Asset issue doc asset does not exist in inventory table
barcode.insufficient.inventory=Insufficient barcode inventory
moldassets.insufficient.inventory=Insufficient inventory of mold assets
main.component.prodplanid.is.empty=main component is empty
correct.main.component.prodplanid=please input correct main component prodplanid
can.not.find.data=can not find data
main.component.barcode.is.empty=main component barcode is empty
main.component.barcode.must.be.number=main component barcode must be number
main.component.barcode.bits.is.incorrect=main component barcode bits is incorrect
the.subcomponent.barcode.has.letters=the subcomponent barcode has letters
the.input.value.of.the.check.box.is.null=the input value of the check box is null
the.incoming.value.is.null=the incoming value is null
subcomponent.barcode.is.empty=subcomponent barcode is empty
operate.person.is.empty=operate person is empty
the.quantity.must.be.number=the quantity must be number
verification.mode.must.be.number=verification mode must be number
the.mainpart.batch.not.meet.the.input.value.of.the.check.box=the mainpart batch not meet the input value of the check box
the.query.record.id.is.null=the query record id is null
the.material.list.code.should.contain.15.characters=the material list code should contain 15 characters
no.bom.information.is.found=no bom information is found
maximum.of.%d.batch.numbers.are.allowed=maximum of %d batch numbers are allowed
operate.success=operate success
the.batch.numbers.do.not.belong.to.the.same.bom=the batch numbers do not belong to the same bom
prodplanid.does.not.exist=prodplanid does not exist
params.can.not.empty=params can not empty
params.error=params error:{0}
please.enter.a.correct.parameter.and.a.valid.number=please enter a correct parameter and a valid number
the.number.of.components.to.be.queried.is.zero=the number of components to be queried is zero
querying.the.subboard.bound.to.a.component.plan.number.is.0=querying the subboard bound to a component plan number is 0
this.barcode.has.technical.modification.requirements=this barcode has technical modification requirements
querying.the.subBoard.bound.to.a.component.plan.info.is.empty=querying the subBoard bound to a component plan info is empty
the.number.of.components.queried.is.null=the number of components queried is null
incorrect.input.information=incorrect input information
subboard=subboard
collect.materials.first=collect materials first
failed.to.obtain.batch.information=failed to obtain batch information
the.batch.information.to.be.queried.is.null=the batch information to be queried is null
id.is.empty=id is empty
please.enter.the.correct.id.no=please enter the correct id no
please.enter.the.correct.legal.number.id.([1-5])=please enter the correct legal number id ([1-5])
input.params.is.illegal=input params is illegal
failed.to.obtain.the.whitelist.data=failed to obtain the whitelist data
failed.to.obtain.the.soldering.scan.data=failed to obtain the soldering scan data
the.relationships.between.mother.boards.and.modules.are.not.scanned=the relationships between mother boards and modules are not scanned
failed.to.get.the.bimu.of.the.mount.welding.management.control.node.or.the.current.scanning.node=failed to get the bimu of the mount welding management control node or the current scanning node
obtain.that.the.bimu.corresponding.to.the.welded.pipe.control.node.is.null=obtain that the bimu corresponding to the welded pipe control node is null
failed.to.obtain.the.soldering.scan.management.and.control.information=failed to obtain the soldering scan.management and control information
the.batch.has.been.locked.on.the.scan.node=the batch has been locked on the scan node
there.is.no.material.receiving.record=there is no material receiving record
obtain.that.the.bimu.corresponding.to.the.current.node.is.null=obtain that the bimu corresponding to the current node is null
the.result.is.empty=the result is empty
the.imu.is.controled=the imu is controled
current.time=current time
synchronizing.and.submitting.tables.and.result.tables.to.the.boardonlinezq=synchronizing and submitting tables and result tables to the boardonlinezq error
the.file.format.is.wrong.please.use.excel.to.import.it=the file format is wrong please use excel to import it
query.success=query success
query.failed=query failed
download.address=download address
address.may.be=address may be
automatic.delete=automatic delete
click.download.address=click download address
settop.box.delivery.information.query=settop box delivery information query and the query result excel is generated successfully
whseid.can.not.be.empty=whseid can not be empty
invoke.success=invoke success
updata.success=updata success
billnumber.isEmpty=billnumber isEmpty
device.site.code.isEmpty=DEVICE_SITE_CODE isEmpty
calculating.the.number.of.board.scanning.records=calculating the number of board scanning records
the.scheduled.calculation.of.boards.and.batch.material.preparation.and.production.cycle.invoking.are.abnormal=the scheduled calculation of boards and batch material preparation and production cycle invoking are abnormal
the.board.and.batch.material.preparation.and.production.cycle.are.being.calculated.and.this.step.is.skipped=the board and batch material preparation and production cycle are being calculated and this step is skipped
the.635.organization.cannot.generate.the.box.number=the 635 organization cannot generate the box number
the.number.of.boxes.cannot.be.less.than.1=the number of boxes cannot be less than 1
the.data.in.the.table.is.deleted.successfully.and.the.data.is.inserted.successfully=the data in the table is deleted successfully and the data is inserted successfully
the.service.is.abnormal=the service is abnormal
please.enter.the.material.requisition.form=please enter the material requisition form
the.length.of.the.requisition.order.exceeds.the.upper.limit=the length of the requisition order exceeds the upper limit
the.number.of.current.forward.lines.is.larger.than.the.maximum.number.of.data.lines=the number of current forward lines is larger than the maximum number of data lines
the.current.page.or.row.number.is.not.a.positive.integer=the current page or row number is not a positive integer
the.warehouse.is.wrong.and.only.warehouses.*******.***********.and.18.have.board.data=the warehouse is wrong and only warehouses 6 7 8 9 10 13 16 17 nd 18 have board data
the.batch.can.contain.only.seven.digits=the batch can contain only seven digits
enter.the.batch.number=enter the batch number
please.enter.the.correct.batch.number=please enter the correct batch number
the.length.of.the.batch.number.exceeds.100=the length of the batch number exceeds 100
task.no.is.not.exists=task no is not exists
info.edit.success=info edit success
the.excel.is.being.generated.please.notice.the.mail=the excel is being generated please notice the mail
task.no.is.empty=task no is empty
data.insert.successfully=data insert successfully
data.is.existed=data is existed
bomcode.is.not.empty=bomcode is not empty
bind.success=bind success
bind.failed=bind failed
insert=insert:
barcode.data.success=barcode data success
barcode.data.failed=barcode data failed
select.data.err= select data failed
The.query.result.does.not.meet.the.requirements=The input parameters are invalid. Check whether the required parameters are entered and whether the number of query parameters is too large.
Query.parameters.are.not.set=Query parameters are not set
input.pagerows=Please input pagerows and rows no greater than 200
masgcode.with.entityname=ntityName must be passed when mfgSiteCode parameters
query.condition.empty=query condition empty!
erp.subinventory.code.empty=erp subinventoryCode empty!
erp.locator.id.empty=erp locatorId empty!
transaction.date.empty=transactionDate empty!
transaction.type.empty=transactionType empty!


entitySiteCode.list.length.should.less.10 = The entitySiteCOde number cannot exceed 10 
The.system.is.abnormal.please.upload.again=The system is abnormal, all data is rolled back, please upload again
The.upload.failed.the.boxNum.does.not.exist=The upload failed for the following reasons: the boxNum does not exist!
The.upload.failed.check.the.limit.weight=The unsuccessful upload billNum is as follows: Please check whether the packing weight limit is not maintained or whether the weight value is greater than the packing weight and less than the packing weight limit!
No.parameters.were.entered=No parameters were entered!
The.case.number.exceeds.the.allowed.maximum.input.quantity=The case number exceeds the allowed maximum input quantity
input.number.is.not.exists = input number is not exists
input.not.belongs.to.this.taskno=input not belongs to this taskno
this.box.has.been.scaned=this box has been scaned
the.number.exceed.can.be.scaned.number=the number exceed can be scaned number
item.info.is.not.exists=item info is not exists
pallet.info.is.not.exists=pallet info is not exists
is.creating.please.wait=contract data is creating please wait
item.info.not.exists.in.item.list=item info not exists in item list
paramNo.were.empty=The paramNo is empty! 

look.up.type.service.error =look up type service error
more.than.contractNumber.no.or.entityNumber.no=number of contractNumber or entityNumber is too much!
The.number.of.contract.numbers.should.between.zero.and.ten=The contract number cannot be empty, and the number of contract numbers cannot be greater than 10

sys.get.Null=Data dictionary query is abnormal
to.wms.error=Unsuccessful synchronization of WMS interface {0}
post.infor.error=Failed to add abnormal INFOR information to the backup table {0}
post.his.error=post his error

save.infor.error=Failed to save abnormal INFOR information to backup table {0}
please.input.one.condition=Please input at least one condition
get.no.stencil.list.to.be.sychronizationed =get mull stencil list to be sychronizationed  
get.item.id.null=The item ID with item code {0} is not found

get.warehousd.null=get warehousd null

request.param.is.error=request param is error
request.path.is.null.or.url.is.error=request path is null or url is error
find.exception.please.input.bill.id.again=find exception,please input bill id again
bill.id.is.null.or.not.exieit.please.input.apain=bill id is null or not exieit,please input apain
billid.billno.is.null.or.not.exieit.please.input.apain=billid billno is null or not exieit,please input apain
from.bill.id.can.not.find.box.info=from bill id,can not find box info
success.get.box.info=success get box info
Each.input.cannot.exceed.three.contract.numbers=Each input cannot exceed three contract numbers
Each.input.cannot.exceed.hundred.box.numbers=Each input cannot exceed hundred box numbers
tasklist.is.null=tasklist is null
update.flow.status.error=update flow status error
sys.not.set=sys.not.set:{0}

find.fail.because.input.subbarcode.is.null=find fail because input subbarcode is null
find.fail.because.input.subbarcode.is.more.than.200=find fail because input subbarcode is more than 200
data.success.find=data success find
data.find.operation.is.ok.but.not.find.data=data find operation is ok but not find data
find.fail.because.input.data.type.is.not.C=find fail because input data type is not C
find.fail.because.input.data.should.be.english=find fail because input data should be english
no.more.than.200.barcode.of.production.material.can.be.input.each.time=no more than 200 barcode of production material can be input each time
input.entityname.is.null.please.check.entityname=input entityname is null please check entityname
input.palltedesc.is.null.please.check.palltedesc=input palltedesc is null please check palltedesc
input.productdesc.is.null.please.check.productdesc=input productdesc is null please check productdesc
input.boxtype.is.null.please.check.boxtype=input boxtype is null please check boxtype
input.stacknum.is.null.please.check.stacknum=input stacknum is null please check stacknum
input.createby.is.null.please.check.createby=input createby is null please check createby
input.organizationid.is.null.please.check.organizationid=input organizationid is null please check organizationid
billno.and.palletno.input.only.one=billno and palletno input only one
billno.no.pallet.no.get.bill.detail.info=billno:{0} no pallet no get bill detail info
pallet.exists.no.scan.end.bill=pallet{0} exists no scan end bill{1}，not get detail info
input.palltedesc.number.is.over.two.hundred=input palltedesc number is more than two hundred please check
input.entityname.not.find.please.check.entityname=input entityname not find please check entityname
boxtype.not.find.width.and.length.please.check=input boxtype not find width and length please check
palletid.not.find.please.check=palletid not find please check
palletno.not.find.please.check=palletno not find please check
save.pallet.error.please.check=save pallet error please check
save.pallet.seccess=save pallet seccess
input.createby.not.eight.please.cheack=input createby not eight please cheack
unknown.error.handler=There is an unknown error in the system, the error log number is: {0}, please contact the operation and maintenance personnel to deal with it, thank you!

operation.success=operation success
entityname.or.materialcode.at.least.one=entityname or materialcode at least one,please check
barcodes.can.not.gt.50=barcodes can not gt 50
input.param.can.not.find.data=input param can not find data
can.not.find.entity.or.site.infor=can not find eitity or site infor

input.productdesc.not.find.please.check.productdesc=input.productdesc.not.find.please.check.productdesc
pallet.information.is.no=pallet information is no
pallet.information.is.not.scaned=pallet information is not scaned
the.height.of.pallet.must.be.more.than.zero=the of height must be more than zero
the.height.of.pallet.is.no.more.than.three.thousand=the height of pallet is no more than three thousand 
only.two.decimal.places.are.allowed.for.palletizing.height=only two decimal places are allowed for palletizing height
the.total.weight.of.pallet.must.be.more.than.zero=the total weight of pallet must be more than zero 
The.maximum.total.weight.of.pallet.cannot.exceed.9999.9=The maximum total weight of pallet cannot exceed 9999.9
only.one.decimal.place.is.allowed.for.the.total.weight.of.pallet=Only one decimal place is allowed for the total weight of pallet
pallett.is.not.empty=pallet is not empty
pallet.is=The maintenance of total weight of pallet is wrong
job.number.does.not.exist.please.re-enter=job number does not exist,please re-enter

billno.already.bind.palletno=billno already bind palletno
not.find.pallet.info=not find pallet info,can not scan,please check
failed.to.save.scan.info=err,failed to save pallet scan info,please check
failed.to.update.scan.info=err,failed to update pallet scan info,please check
pallet.mfg.site.address.different=pallet mfg site address is different,please check
validate.number.err=pallet number scan num must be equal 
box.weight.is.zero=box weight is zero,please check
box.and.pallet.entityname.is.diff=box and pallet of entityname is different,please check
not.find.pallet.info.please.check=not find pallet info,please check input pallet number
fill.empty.box.bill.count.err=fill empty box bill count not equal scan count,please check
stack.count.err=stack count not equal box bill count,please check
pallet.end.flag.is.yes=current pallet status is yes,can not scan,please check
pallet.had.already.scaned=pallet has been scaned,can not scan,please check
pallet.had.been.scan.in.current=current input box num has been scan in current pallet,please check
real.box.count.err=pallet has been scaned,can not scan,please check
box.bill.count.err=pallet has been scaned,can not scan,please check
pallet.number.not.exists=pallet not exists,can not scan,please check
pallet.site.diff.with.box.bill.site=pallet site address is different with box bill site address,can not scan,please check
bill.box.is.null=input box number is empty,please check
pallet.is.empty=input pallet number is empty,please check
oranization.is.empty=input orgazition is empty,please check
scanby.is.empty=input scanby is empty,please check
scanby.is.empty.gt20=input scanby is empty or gt 20,please check
scandate.is.empty=input scandate is empty,please check
position.is.empty=input position is empty,please check
entry.bill.detailids.not.null=input entry bill detailids not null or can not gt 1000,please check
update.rows.is.zero=update rows is zero
entry.bill.status.not.null=input entry bill status not null or can not gt 10,please check
entry.bill.return.reason.gt200=input entry bill return reason can not gt 200,please check
entry.bill.postby.gt20=input entry bill postby can not gt 20,please check
entry.bill.submitby.gt20=input entry bill submitby can not gt 20,please check
entry.bill.receiveby.gt20=input entry bill receiveby can not gt 20,please check
entry.bill.returnby.gt20=input entry bill returnby can not gt 20,please check
entry.bill.not.null=input entry bill not null,please check
stockno.not.null=input stockno not null,please check
billno.or.palletno.not.null=input billno or palletno not null,please check
scanby.is.err=input scanby is err,please check
pallet.id.is.err=pallet id can not get value
wis.id.list.is.empty.or.exceed.max=wisidList is empty or exceed max count: {0}
sku.list.is.empty.or.exceed.max=skuList is empty or exceed max count: {0}
wis.id.can.only.contains.number.letter.underline=wisId can only contains number letter underline

contract.is.null=contract is null,please check
entity.is.null=entity is null,please check
barcode.is.null=barcode is null,please check
itemcode.is.null=itemcode is null,please check
boxno.is.null=boxno is null,please check
updateman.is.null=updateman is null,please check
input.param.is.batch=can not input batch datas

pallet.itemNo.not.same.to.input.itemNo=The pallet number corresponds to the material and the scanned material code is inconsistent
Job.number.does.not.exist=job number does not exist 
any.thing.no=any paramater is not null 
task.no=the task is no exist 
no.site=the entity has no site 
there.are.no.materials.configured.for.this.site=There are no materials configured for this site information
task.number.does.not.match.with.environmental.attributes=The environmental protection attribute of this material barcode is not consistent with the environmental protection attribute of task number
barcode.information.is.not.found=barcode information is not found
the.barcode.has.been.bound=The barcode has been bound in other sites or task numbers. To bind, please unbind the barcode
The.barcode.under.this.configuration.has.been.scanned=The barcode under this configuration has been scanned
Barcode.under.configuration.modification.has.been.scanned=Barcode under configuration modification has been scanned
The.number.of.scanned.bar.code.is.greater.than.the.number.of.configured.bar.code=The number of scanned bar code is greater than the number of configured bar code
The.main.bar.code.is.controlled.by.technical.transformation=The main bar code is controlled by technical transformation
Insert.main.bar.code.to.report.error=Insert main bar code to report error
Operation.successful=Operation successful
No.corresponding.configuration.information.was.found.for.barcode=No corresponding configurationinformation was found for barcode
dqas.verification.failed=Dqas verification failed
no.find.box.info=not find the load box info,please check
no.bom.information.from.boxinfo=not find the bom information from the box information,please check
template.info.is.null=template information can not be null,please check
bom.not.loaded=the box is not loaded,please check
box.is.virture=the box is virture,can not print,please check
no.cbom.information.from.boxinfo=not find the cbom information from the box information,please check
many.mateial.Information=The material code is matched to multiple configured material information

stock.no.is.null=Stock in transit cannot be empty
warehouseId.is.null=Online side warehouse ID cannot be empty
get.flow.qty.error=Failed to obtain warehousing transaction information

num.must.over.zero=input query page and page num must over zero
page.is.null=input query page is null,please check
rows.is.null=input query each page num is null,please check
rows.is.over.200=input query each page num can not over 200,please check
item.code.num.is.over.50=input query item code num can not over 50,please check
input.data.contain.point=input query page and page num must be integer
Each.parameter.must.be.entered=Each parameter must be entered
Softtaskno.startimeendtime.atleastone=Softtaskno startimeendtime at least one，others must be entered
Per.org.cannot.repeat.in.8000220=Exist org have many softwate subInventory，please check data dictionary 8000220
Paging.parameter.cannot.be.greater.than.200=Paging parameter cannot be greater than 200
The.number.of.entries.per.page.cannot.be.less.than.or.equalled.to.0=The number of entries per page cannot be less than or equalled to  0
The.requested.page.number.cannot.be.less.than.or.equalled.to.0=The requested page number cannot be less than or equalled to  0
The.start.time.of.query.cannot.be.greater.than.the.end.time=The start time of query cannot be greater than the end time
TASK.SOFT.NO.LESS.100=Task soft no count must less than 100
There.is.no.data.for.pushing.PDM.during.this.period.or.the.push.fails=There is no data for pushing PDM during this period, or the push fails
Box.list.quantity.is.too.large=Box list quantity is too large

task.no.is.batch=task.no.can.not.batch
job.stop.sysn=fail to job sysn

timer.synchronize.fail=timer synchronization wmes head table failed
timer.synchronize.wmes.fail=timer synchronization wmes line table failed
timer.synchronize.fail.head=timer synchronization app mes head table failed
timer.synchronize.fail.line=timer synchronization app mes line table failed

entity.trace.input.is.null=entity trace input is null
erp.start.time.and.end.time=erp.start.time.and.end.time.no.over.3day
pagesize.is.empty=pagesize or currentPage is null



Organization.Id.must.be.entered=Organization Id must be entered
Sub.bar.codes.of=The sub barcodes controlled by technical transformation are
main.barcode.of=The main  barcodes controlled by technical transformation are 
Material.barcode.must.be.entered=Material barcode must be entered
The.function.module.must.be.entered=The function module must be entered
The.page.is.too.big=The page is too big
query.param.is.empty=Query time Start time The end time cannot exceed 15 days.
pallet.number.is.over.50= the number of pallet is over 50
query.task.list.is.empty=the array of task list is empty
query.task.is.no.more.than.200=the array of  task list is no more than 200
entity.name.not.null=The task number cannot be empty!
boxup.is.not.null.or.gt.set.upper.limit=boxup is not null or gt set upper limit
SN.is.null.or.exceeds.the.set.upper.limit=SN is null or exceeds the set upper limit
auto.flag.is.not.null=Auto flag is not null
boxup.at.least.one.is.exists=boxup at least one is exists
entity.name.list.length.should.less.50=You can only enter up to 50 task numbers, please confirm!
current.page.not.null=When entering multiple task numbers, you need to enter the current page for query!
page.size.not.greater.than.200=When entering multiple task numbers, the number of pages per page cannot be greater than 200
validate.input.type.of.check.binding.error=The input type is null or the format is incorrect, please confirm!
params.list.is.null=The parameter list is empty, please confirm!
size.out.bound.params.list.of.check.binding=The number of parameter lists exceeds 50, please confirm!
entity.name.not.all.exist=Task number {0} is not in the system, please confirm!
exist.not.bound.entity.name=There are task numbers that must be bound but not bound. Please confirm!
exist.not.bound.mfg.site.id=There is a production distribution ID that must be bound but not bound. Please confirm!


The.barcode.is.larger.than.five.layers.and.cannot.be.bound=The barcode is larger than five layers and cannot be bound
Same.code.same.material.code.with.substitution.relationship=Same code/same material code with substitution relationship（
The.cannot.be.bound.hierarchically.as.a.primary.sub.relationship=）The cannot be bound hierarchically as a primary sub relationship
This.barcode.has.been.bound.to=This barcode has been bound to
No.binding.is.allowed.on.the.barcode=No binding is allowed on the barcode
entityName=entity name
This.barcode.is.a.primary.barcode.and.cannot.be.bound.to.its.own.sub.barcode=This barcode is a primary barcode and cannot be bound to its own sub barcode
If.this.subassembly.is.bound.to.this.parent.subassembly.the.binding.level.will.be.greater.than.5=If this subassembly is bound to this parent subassembly, the binding level will be greater than 5
Primary.barcode.and.sub.barcode.cannot.be.the.same.barcode=Primary barcode and sub barcode cannot be the same barcode
The.barcode.is.bound.to.the.task.number=The barcode is bound to the task number
Next.to.bind.please.unbind.the.barcode=）Next, to bind, please unbind the barcode
Verification.succeeded=Verification succeeded
This.barcode.has.been.bound.in.other.sites.To.bind.please.unbind.the.barcode=This barcode has been bound in other sites ,To bind please unbind the barcode
The.barcode.has.been.scanned=The barcode has been scanned
this.barcode.dt.barcode=this barcode
itemCode.dt.itemCode=itemCode
messageID.dt.messageID=item ID
repleacedID.dt.repleacedID=Alternative material code does not exist
diction.is.null=Data dictionary is empty
scan.no.end={0} sacn no end,not get bill number detail info
bill.and.orgid.cannot.empty=bill and orgid cannot empty
no.bill.info=No bill info
token.is.not.null=Token value validation failed
sessionid.is.not.null=Session id is not null
no.taskname=no.taskname
Abnormal.verification.of.job.number=Abnormal verification of job number
The.maximum.value.of.the.box.list.cannot.exceed=The maximum value of the box list cannot exceed
The.page.or.The.CurrentPage.is.no.negative=The page or The CurrentPage is no negative
the.param.is.empty=the param is empty
the.box.is.empty=Pallet and case cannot be empty at the same time
the.box.is.not.empty=Pallet and case cannot be input at the same time 
the.entity.is.not.exist=the entity is not exist
the.SubInvCode.is.not.exist=the subInvCode is not exist
boxnumber=box number
auax.stimulate.summit=It has the relationship between primary and secondary containers and must be submitted at the same time
virtual.box.number=The volume or weight of some of box number is 0, and it is not a virtual box. It is not allowed to enter the warehouse
map.box.pallet=Some boxes are not allowed to enter the warehouse if they are not on the pallet
entry.ware.house=The box has not been put into storage, please check
Pallet.not.canned.end=Pallet not canned end
not.canned.end=box number:[%s] Unscan End
No.termail.task.is.must.approval.by.factory=No termail task is must approval by factory,billno:{0},dealer:{1}
Bill.status.not.is.submitted=Bill status is not submitted,cannot receive
Taskno.is.not.move.in.erp=Taskno is not move in erp
Taskno.is.not.exists.in.erp=Taskno is not exists in erp
Taskno.status.isnot.pack=Taskno status is not pack
The.sub.barcode.cannot.be.a.barcode.starting.with.21CA=The sub barcode cannot be a barcode starting with 21CA
Under.the.whole.machine.task.the.sub.barcode.can.only.have.four.layers.of.material.codes=Under the whole machine task the sub barcode can only have four layers of material codes
Under.the.task.of.the.whole.machine.the.primary.barcode.cannot.be.a.two.layer.and.three.layer.material.code=Under the task of the whole machine, the primary barcode cannot be a two-layer and three-layer material code
Box.no.item.no.scaned=The box has no material information
pallent.no.scan.end=Pallent scan not end,can not warehouse
do.not.enter.empty.box=Empty containers are not allowed to enter the warehouse
box.pallet.is.not.exist=box or pallet is not exist or under not current entity 
cannot.input.box=cannot input boxnumber 
the.gross.and.netweight.is.not.zero=the gross and netweight is not zero
exists.in.order=exists in order
Itembarcode.between.0.and.50=Itembarcode between 0 and 50
Pagesize.between.0.and.%d=Pagesize between 0 and %d
The.main.barcode.is.21CA.which.must.be.a.complete.machine.site=The main barcode is 21CA, which must be a complete machine site
query.count.cannot.be.greater.than.500=once cannot greater than 500 prodplan
prodplan.id.null=prodplan id can not null
redis.lock.fail={0} Operation is in progress. Please try again later!
sn.miss.board.online=The {0} barcode does not exist in the board online. Please try again later!
customer.name.isnot.null=customer name isnot null
sn.not.more.than.500=sn not more than 500
taskno.and.sn.isnot.null=taskno and sn isnot null
submit.time.interval.more.than.180.days=submit time interval can not more than 180 days
verify.time.interval.more.than.180.days=verify time interval can not more than 180 days
query.param.error=one of prodplanId,submitDate and verifyDate must be filled in
failed.to.get.redis.lock=failed to get redis lock
call.service.error=Exception calling service: {0} -- {1}
failed_to_obtain_customer_basic_information=Failed to obtain customer basic information based on material code
failed_to_call_mds_interface=Failed to call MDS interface:{0}
failed_to_get_barcode_center_url=Failed to obtain the data dictionary configuration barcode center interface URL
call.barCode.center.expandQuery.barCode.falied=Failed to retrieve barcode extension information from barcode center: {0}
sys.look.not.config = Data dictionary {0} is not configured
entity.name.need.user.address = Input entity name requires carrying user address
get.zs.log.station.error=get.zs.log.station.error
autoflag.is.not.null=autoflag.is.not.null
tasks_cannot_exceed_20=Tasks cannot exceed 20
archive.extract.task.executing=The data synchronization task to be archived is currently being executed
archive.task.executing=Archive task is currently executing
archive.semiprod.null=Calling the service to obtain the management document information of the semi-finished product to be archived returned empty
archive.semiprod.detail.null=Calling the service to obtain the detailed information of the semi-finished product order management document to be archived returned empty
archive.payable.null=Calling the service to obtain the information of accounts payable settlement documents to be archived returned empty
archive.manufacturingorder.server.error=Calling the service to obtain the manufacturing notification returned an error
unarchive.manufacturingorder.server.error=Calling the service to obtain the pending manufacturing notification document and returning an error message
archive.manufacturingorder.download.error=File download error
archive.testprocess.server.error=Calling the service to obtain the testing process sheet returned an error
unarchive.testprocess.server.error=Calling the service to obtain the test process documents to be archived returned an error
b2b.return.cn=Hello, there are B2B return failure batches in document {0}. Please check.
task.no.not.exist=task no {0} not exist
task.no.not.more.than.100=task no not more than 100
type.no.is.null=type no is null
regulation.is.null=regulation is null
master.type.is.null=master type can not null
small.type.is.null=small type can not null
sub.type.is.null=sub type can not null
is.item.is.null=item type can not null
category.is.null=category can not null
defects.qty.is.null=defects qty can not null
contract.and.task.is.null=contract and task can not null
choose.no.imei.repeat=Duplicate delivery IMEI: {0}
choose.no.repeat=Duplicate delivery choose No：{0}
choose.no.exist=The delivery notice No. already exists: {0}
imei.box.not.find=The box number of serial number {0} is not found.
box.info.not.find={0} box Information Not Found
box.imei.item.diff=The {0} box material code {1} is different from the serial number {2}.
box.imei.sub.diff=The {0} box inventory {1} is different from the serial number {2}.
box.subinventory.not.find=The sub-inventory of {0} small box number is empty and cannot be scanned!
box.warehouse.not.in=The imei {0} box {1} is not in the warehouse.
imei.scaned=The serial number {0} has been scanned.
bill.no.exists = bill no. {0} already exists. Please confirm!
issue.date.not.null=issue date can not be empty
tray.code.is.null=tray code is null
outer.box.list.is.null=outer box list is null
en.code.list.is.null=enCode list is null
mes.info.is.null=mes info is null
forward.and.mtp.test.upload.success.not.exists=forward and mtp test upload success not exists
discrete.param.name.error = If the parameter type is data segment, the length of the paramList list must be two.
exist.not.param.name = The parameter name does not match the database.
param.type.is.incorrect = The parameter type is incorrect.
param.list.is.null = The parameter value cannot be null.
exist.not.data.type = The data type is incorrect.
exist.not.param.Repeated=The data parameter name must be unique.
item.code.of.mes.is.different= itemCode of mes is different
mes.packing.info.is.null=mes packing info is null
failed_to_specified_ps_task=Failed to obtain the interface for querying the planned task information of the workshop.
item_control_entry_fail=Special Materials {0} Control:Issuance of materials for Task {1} is prohibited.Please contact the business representative Zhang Xiao for coordination.
required_fields_cannot_be_empty=Required fields {0} cannot be empty.