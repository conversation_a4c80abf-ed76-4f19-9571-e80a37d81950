RetCode.Success=操作成功
RetCode.ServerError=服务器错误
RetCode.AuthFailed=认证失败
RetCode.PermissionDenied=没有权限
RetCode.ValidationError=验证失败
RetCode.BusinessError=业务异常
customize.msg={0}
task.no.is.batch=输入任务号不能支持批量查询
job.stop.sysn=同步失败

empty.input.contract.eg=合同号、物料代码、物料名称、物料条码，至少输入一个，请重新输入
only.itemname=只输入了物料名称，请输入其它精确查询条件或发货日期范围设定到10天以内


entity.trace.input.is.null=ERP排产开始日、ERP排产结束日、计划组、子状态都必须输入，请确认
erp.start.time.and.end.time=ERP排产开始日、ERP排产结束日时间间隔不能超过{0}天
pagesize.is.empty=输入查询页码或者每页展示数量不能为空，请确认

Please.Input.WareHouse=请输入仓库
Please.Input.OrderKey.WaveKey=订单号,波次号,至少要有一个输入条件
Please.Input.Id=请输入箱号
Please.Input.OrderKey.ExternalOrderKey2.WaveKey.Id=SO单号,外部单号2,波次号,落放ID,至少要有一个输入条件
Please.Input.OrderKey.ExternalOrderKey2=订单号,外部订单号2,至少要有一个输入条件
Please.Import.Excel.NoMoreThan.Contractnumber.Fifty=合同号超过50行，请检查谢谢！
Please.Import.Excel.NoMoreThan.Doid.FiveHundred=发货指令号超过500行，请检查谢谢！
Please.Import.Excel.NoMoreThan.BillNumber.FiveHundred=装箱单号超过500行，请检查谢谢！
Please.Check.Import.File.Type.Excel=文件格式错误，请使用excel导入！
board.not.belong=该单板不属于本部件
module.barcode.not.exists=该模块条码不存在
workorder.sourcesys.is.null=指令来源不能为空
main.sn.prodplan.is.null=主条码批次不能为空
sub.sn.prodplan.is.null = 子条码批次不能为空
sub.sn.is.null = 子条码不能为空
main.sn.is.null=  主条码不能为空
get_board_assembly_relationship_error=获取主条码子条码接口异常:


data.wb.call.param.miss=调用数据回写服务缺少 {0} 参数
emp.no.is.null=工号为空
time.format.error=时间格式错误
sfc.return.cur.value.is.null=sfc返回当前条码值为空
sfc.return.cur.value.is.not.number=sfc返回当前条码值非数字
sfc.return.cur.value.is.more.than.max=sfc返回当前条码值超过最大值
Please.Input.query.condition=请输入查询条件
Please.Input.BillNumber=请输入箱号
Query.More.BillNumber=限定查询100个箱号，箱号超出
item.id.found.error=未找到itemId,erp同步失败
wip.move.mtl.insert.error = ZTE_WIP_MOVE_MTL_TXN 插入失败
mrp.wip.issue.insert.eror = ZTE_MRP_WIP_ISSUE 插入失败

entitySiteCode.list.length.should.less.10  = 输入的任务号站点编码数量不能超过10
task.no.is.null=任务号不能为空
update_step_stock_locked=更新STEP条码和物料库存正在执行
delete_param_error=删除几个月之前月份参数必须要为非负数
itembarcode_stockno_is_null=条码，在途库子库存,线边仓ID,不能为空
itembarcode_not_exist=条码在途库库存不存在

boxNo.no.is.null=箱号不能为空
itemType.no.is.null=物料类型不能为空
itemName.no.is.null=物料名称不能为空
itemCode.no.is.null=物料代码不能为空
MFGSITE.ID.IS.NULL=生产站点Id不能为空
CREATE.BARCODE.QTY.BETWEEN.1.AND.50=打印条码数量应该大于0小于51
SEARCH.IS.NULL.BY.ENTITYNAME.MFGSITEID.ITEMCODE.LAYER1.LAYER4=按任务号和站点ID查询1层、4层物料代码时为空
CREATE.BARCODE.FAILURE=生成条码失败
PTO.ITEMCODE.IS_NOT.PDVM=一层物料不是整机
FOURE.LAYER.ITEMCODE.DO.NOT.MARK=四层物料未标定
data.is.null=未输入数据
month.compute.time=月结时候，禁止操作
barcode.is.no.stock=条码在条码库存表不存在
asset.no.stock=资产出库单资产在库存表不存在
barcode.insufficient.inventory=条码库存不够
moldassets.insufficient.inventory=模具资产库存不够
main.component.prodplanid.is.empty=主部件批次为空
correct.main.component.prodplanid=请输入正确的主部件批次
can.not.find.data=查找不到数据
main.component.barcode.is.empty=主部件条码为空
main.component.barcode.must.be.number=主部件条码必须都为数字
main.component.barcode.bits.is.incorrect=主部件条码传入位数有误
the.subcomponent.barcode.has.letters=子部件条码存在字母，不符合复选框传入的值
the.input.value.of.the.check.box.is.null=复选框传入值为空
the.incoming.value.is.null=单选框传入值为空
subcomponent.barcode.is.empty=子部件条码传入值为空
operate.person.is.empty=操作人传入值为空
the.quantity.must.be.number=数量必须为数字
verification.mode.must.be.number=验证方式必须为数字
the.mainpart.batch.not.meet.the.input.value.of.the.check.box=主部件批次不符合复选框传入的值
the.query.record.id.is.null=查询记录id为空
the.material.list.code.should.contain.15.characters=料单代码长度应为15个字符，请检查！
no.bom.information.is.found=未找到料单信息，请检查！
maximum.of.%d.batch.numbers.are.allowed=批次号最多不超过%d个，请检查！
operate.success=操作成功
the.batch.numbers.do.not.belong.to.the.same.bom=批次号不属于同一料单，请检查！
prodplanid.does.not.exist=批次号[%s]不存在，请检查！
params.can.not.empty=参数不可为空
params.error=参数异常:{0}
please.enter.a.correct.parameter.and.a.valid.number=请正确输入参数，合法数字
the.number.of.components.to.be.queried.is.zero=查询部件数量为零
querying.the.subboard.bound.to.a.component.plan.number.is.0=查询部件计划绑定子板数量为零
this.barcode.has.technical.modification.requirements=该条码有技改要求，请确认技改单
querying.the.subBoard.bound.to.a.component.plan.info.is.empty=输入的信息,经查询计划绑定子板信息为空!
the.number.of.components.queried.is.null=查询部件数量结果为空!
incorrect.input.information=输入信息有错，请检查后重新输入（查询子板是否有收料时）!
subboard=子板
collect.materials.first=未收料,请先点收料，保证所有子板收料完成！
failed.to.obtain.batch.information=获取批次信息出错！
the.batch.information.to.be.queried.is.null=查询批次信息为空,不能绑定
id.is.empty=id为空
please.enter.the.correct.id.no=请输入正确的id号
please.enter.the.correct.legal.number.id.([1-5])=请输入正确的id号（[1-5]）合法数字
input.params.is.illegal=输入参数不合法,请检查后重新输入!
failed.to.obtain.the.whitelist.data=获取白名单数据失败!
failed.to.obtain.the.soldering.scan.data=获取装焊扫描管控数据失败
the.relationships.between.mother.boards.and.modules.are.not.scanned=未建立子母卡/模块对应关系扫描，请先完成对应扫描
failed.to.get.the.bimu.of.the.mount.welding.management.control.node.or.the.current.scanning.node=获取装焊管控节点或当前扫描节点BIMU失败
obtain.that.the.bimu.corresponding.to.the.welded.pipe.control.node.is.null=获取装焊管控节点对应的BIMU为空
obtain.that.the.bimu.corresponding.to.the.current.node.is.null=获取当前扫描节点对应的BIMU为空
failed.to.obtain.the.soldering.scan.management.and.control.information=获取装焊扫描管控信息失败！
the.batch.has.been.locked.on.the.scan.node=该批次在该扫描节点已经被锁定，禁止扫描！
there.is.no.material.receiving.record=没有填收料记录，不允许扫描
the.result.is.empty=查询结果为空
the.imu.is.controled=IMU已管控，请确认处理
current.time=当前时间：
synchronizing.and.submitting.tables.and.result.tables.to.the.boardonlinezq=定时同步提交表和结果表数据到Board_Online_ZQ表出现异常，异常为:
the.file.format.is.wrong.please.use.excel.to.import.it=文件格式错误，请使用excel导入！
query.success=生成查询结果excel成功
query.failed=生成查询结果excel失败
download.address=下载地址
address.may.be=此地址将在
automatic.delete=后自动删除,请尽快下载
click.download.address=点击下面的下载地址（下载导出文件）
settop.box.delivery.information.query=机顶盒发货信息查询，生成查询结果excel成功
whseid.can.not.be.empty=whseid不能为空，请输入
invoke.success=调用成功
updata.success=更新成功
billnumber.isEmpty=billnumber isEmpty
device.site.code.isEmpty=DEVICE_SITE_CODE isEmpty
calculating.the.number.of.board.scanning.records=计算单板扫描记录条数：
the.scheduled.calculation.of.boards.and.batch.material.preparation.and.production.cycle.invoking.are.abnormal=定时计算单板及批次备料、生产周期调用异常
the.board.and.batch.material.preparation.and.production.cycle.are.being.calculated.and.this.step.is.skipped=WARNNING:单板及批次备料、生产周期计算进行中，本次执行跳过
the.635.organization.cannot.generate.the.box.number=组织635无法生成箱号
the.number.of.boxes.cannot.be.less.than.1=箱数不能小于1
the.data.in.the.table.is.deleted.successfully.and.the.data.is.inserted.successfully=删除表内数据执行成功，插入数据成功！
the.service.is.abnormal=服务异常
please.enter.the.material.requisition.form=请输入领料单
the.length.of.the.requisition.order.exceeds.the.upper.limit=领料单的长度超出上限
the.number.of.current.forward.lines.is.larger.than.the.maximum.number.of.data.lines=当前行大于最大数据行数
the.current.page.or.row.number.is.not.a.positive.integer=当前页或行数不为正整数
the.warehouse.is.wrong.and.only.warehouses.*******.***********.and.18.have.board.data=仓库不对，只有6、7、8、9、10、13、16、17、18仓库才有单板数据
the.batch.can.contain.only.seven.digits=批次只能是七位的数字
enter.the.batch.number=请输入批次号
please.enter.the.correct.batch.number=请输入正确的批次号
the.length.of.the.batch.number.exceeds.100=批次号的长度超出100
task.no.is.not.exists=要插入的任务号不存在
info.edit.success=信息修改成功并且添加日志！
the.excel.is.being.generated.please.notice.the.mail=正在生成excel，请留意邮件
task.no.is.empty=未传入任务号！
data.insert.successfully=数据插入成功
data.is.existed=数据已存在，不需要插入
bomcode.is.not.empty=料单代码不能为空
bind.success=绑定成功
bind.failed=绑定失败
insert=插入:
barcode.data.success=条码数据成功！
barcode.data.failed=条码数据失败！
select.data.err = 查询不到数据!

sys.get.Null=数据字典查询异常
to.wms.error=同步WMS接口不成功{0}
post.infor.error=新增异常INFOR信息到备份表失败{0}
save.infor.error=保存异常INFOR信息到备份表失败{0}
post.his.error=新增历史表数据失败

The.query.result.does.not.meet.the.requirements=输入参数不合法，请检查必输参数是否输入，批量查询的查询参数个数是否过多
Query.parameters.are.not.set=未设置查询参数
input.pagerows=请输入分页参数，每页不大于200条
masgcode.with.entityname=传入生产布点参数时须传任务号
query.condition.empty=��ѯ����Ϊ�գ�
erp.subinventory.code.empty=ERP��Ϊ�գ�
erp.locator.id.empty=ERP��λΪ�գ�
transaction.date.empty=��������Ϊ�գ�
transaction.type.empty=��������Ϊ�գ�
look.up.type.service.error =调用数据字典服务错误
more.than.contractNumber.no.or.entityNumber.no=输入的合同号或任务号的个数或服务并发数大于限制数!
The.number.of.contract.numbers.should.between.zero.and.ten=合同号不能为空，且合同号个数不能大于10
The.system.is.abnormal.please.upload.again=系统异常，数据全部回滚，请全部重新上传！
The.upload.failed.the.boxNum.does.not.exist=上传失败，原因如下：单据号不存在！
The.upload.failed.check.the.limit.weight=本次上传未成功箱号如下：请核对装箱限重是否未维护或该重量值是否大于箱重且小于装箱限重值！
No.parameters.were.entered=未输入参数
no.data.found=没有查询到数据
The.case.number.exceeds.the.allowed.maximum.input.quantity=箱号超出允许最大输入数量
input.number.is.not.exists = 输入的箱号不存在，请确认后重新输入！
input.not.belongs.to.this.taskno=输入的箱号不属于当前的任务号，请确认后重新输入！
this.box.has.been.scaned=该箱号已经扫描并录入数据，请确认后重新输入！
the.number.exceed.can.be.scaned.number=栈板号的数量超出了可扫描数量！
item.info.is.not.exists=箱号对应的物料信息不存在
pallet.info.is.not.exists=栈板号信息不存在
pallet.itemNo.not.same.to.input.itemNo=栈板号对应物料和扫描物料代码不一致
is.creating.please.wait=合同任务表征数据正在生成中。请稍候重试
item.info.not.exists.in.item.list=栈板号对应的物料代码不在箱号的物料清单中
paramNo.were.empty=参数编号不能为空
please.input.one.condition=请输入至少一个查询条件
get.no.stencil.list.to.be.sychronizationed = 没有需要同步的钢网信息
get.item.id.null=未查询到物料代码{0}的物料ID

get.warehousd.null=当前工厂下没有线边仓

request.param.is.error=请求参数没有填好
request.path.is.null.or.url.is.error=请求路径没有或页面跳转路径不对
find.exception.please.input.bill.id.again=发现异常，请重新输入装箱单ID!
bill.id.is.null.or.not.exieit.please.input.apain=装箱单ID为空或不存在，请重新输入
billid.billno.is.null.or.not.exieit.please.input.apain=装箱单ID装箱单NO都为空或不存在，请重新输入
from.bill.id.can.not.find.box.info=装箱单ID,对应的装箱单信息不存在!
success.get.box.info=成功获得装箱单信息！
Each.input.cannot.exceed.three.contract.numbers=每一次输入不能超过三个个合同号
Each.input.cannot.exceed.hundred.box.numbers=每一次输入不能超过100个装箱单号
tasklist.is.null=交易明细为空
update.flow.status.error= 更新交易标志失败
sys.not.set=数据字典:{0}未配置

find.fail.because.input.subbarcode.is.null=查询失败，因为输入的子条码数据为空
find.fail.because.input.subbarcode.is.more.than.200=查询失败，因为输入的子条码数据个数超过200
data.success.find=查询成功
data.find.operation.is.ok.but.not.find.data=查询完成，但是未找到相关数据
find.fail.because.input.data.type.is.not.C=查询失败，因为输入数据类型不是字符C
find.fail.because.input.data.should.be.english =查询失败，输入分割符包含中文逗号
no.more.than.200.barcode.of.production.material.can.be.input.each.time=每一次输入的生产物料条码不能超过200个
input.entityname.is.null.please.check.entityname=输入的任务号不能为空，请检查
input.palltedesc.is.null.please.check.palltedesc=输入的托盘描述不能为空，请检查
input.productdesc.is.null.please.check.productdesc=输入的产品描述不能为空，请检查
input.boxtype.is.null.please.check.boxtype=输入的箱型代码不能为空，请检查
input.stacknum.is.null.please.check.stacknum=输入的托盘堆叠数量不能为空，请检查
input.createby.is.null.please.check.createby=输入的创建人不能为空，请检查
input.organizationid.is.null.please.check.organizationid=输入的组织ID不能为空，请检查
billno.and.palletno.input.only.one=箱号和托盘号只能输入一个
pallet.exists.no.scan.end.bill=托盘{0}存在未扫描结束的箱{1}，无法获取托盘明细数据
billno.no.pallet.no.get.bill.detail.info=箱:{0}未装托盘，无法获取托盘明细数据
input.palltedesc.number.is.over.two.hundred=输入的托盘描述不能超过200字，请检查
input.entityname.not.find.please.check.entityname=输入的任务号不存在，请检查
boxtype.not.find.width.and.length.please.check=输入的箱型代码查询对应的长、宽不存在，请检查。
palletid.not.find.please.check=发生异常，托盘ID不存在，请联系相关负责人员
palletno.not.find.please.check=发生异常，托盘编号不存在，请联系相关负责人员
save.pallet.error.please.check=发生异常，托盘信息维护失败，请联系相关负责人员
save.pallet.seccess=托盘信息维护成功
input.createby.not.eight.please.cheack=输入的创建人不是14位工号的后8位，请检查
unknown.error.handler=系统出现未知错误，错误日志编号为：{0},请联系运维人员处理，谢谢！

operation.success=操作成功
entityname.or.materialcode.at.least.one=操作失败，输入任务号或者物料代码至少一个，请确认
barcodes.can.not.gt.50=条码不能大于50个
input.param.can.not.find.data=操作失败，输入数据未找到相关信息，请确认
can.not.find.entity.or.site.infor=操作失败，未找到任务或站点相关信息，请确认

input.productdesc.not.find.please.check.productdesc=操作失败，输入的产品描述校验不通过
pallet.information.is.no=无托盘维护信息，请确认
pallet.information.is.not.scaned=托盘未扫描结束，请确认
the.height.of.pallet.must.be.more.than.zero=托盘码垛高度必须是大于零的数字
the.height.of.pallet.is.no.more.than.three.thousand=托盘码垛高度不能超过3000.00
only.two.decimal.places.are.allowed.for.palletizing.height=托盘码垛高度只允许保留两位小数
the.total.weight.of.pallet.must.be.more.than.zero=托盘总重量必须是大于零数值
The.maximum.total.weight.of.pallet.cannot.exceed.9999.9=托盘总重量最大值不能超过9999.9
only.one.decimal.place.is.allowed.for.the.total.weight.of.pallet=托盘总重量只允许带一位小数
pallet.and.height.and.weight.and.lastUpdateby.are.not.empty=托盘号、高度、重量和最后更新人不能为空
pallet.is=托盘总重量维护有误,，请检查顶层箱定义中托盘箱型代码对应的箱重及扫描的每箱重量是否正确！
job.number.does.not.exist.please.re-enter=工号不存在请重新输入

billno.already.bind.palletno=箱号已经绑定托盘
not.find.pallet.info=没有托盘信息，不能进行装箱单扫描，请重新输入
failed.to.save.scan.info=发生异常，保存扫描信息失败
failed.to.update.scan.info=发生异常，托盘信息更新失败
pallet.mfg.site.address.different=按站型交付模式下，同一托盘上的箱号站点地址应该相同，请检查！
validate.number.err=托盘号{0}已扫描数量必须等于需扫描数量，不允许扫描结束
box.weight.is.zero=箱重量不能为空或者为0，请维护，谢谢！
box.and.pallet.entityname.is.diff=装箱单号对应的任务号，必须与托盘号所对应该的任务号相匹配
not.find.pallet.info.please.check=没有托盘信息，请确认托盘号是否正确
fill.empty.box.bill.count.err=托盘号{0}的托盘填充空箱数量为{1}，已经扫描了{2}个空箱单，不允许再扫描，请检查
stack.count.err=托盘号{0}的托盘堆叠数量为{1}，已经扫描了{2}个装箱单，不允许再扫描，请检查
pallet.end.flag.is.yes=当前托盘号的“是否扫描结束”为“是”，不允许进行装箱单扫描
pallet.had.already.scaned=输入的装箱单号已经扫描，不允许重复扫描
pallet.had.been.scan.in.current=该装箱单号{0}已经扫描至托盘号{1}中，不允许重复扫描
real.box.count.err=托盘号{0}的托盘正常箱号数量为{1}，已经扫描了{2}个正常箱号单，不允许再扫描，请检查
box.bill.count.err=托盘号{0}的托盘堆叠数量为{1}，已经扫描了{2}个装箱单，不允许再扫描，请检查
pallet.number.not.exists=装箱单号不存在，无法扫描
pallet.site.diff.with.box.bill.site=托盘站点与装箱单号站点不一致，不允许扫描
bill.box.is.null=输入的箱号不能为空，请检查
pallet.is.empty=输入的托盘号不能为空，请检查
oranization.is.empty=输入的组织ID不能为空，请检查
scanby.is.empty=输入的扫描人不能为空，请检查
scanby.is.empty.gt20=输入的扫描人不能为空或大于20，请检查
scandate.is.empty=输入的扫描时间不能为空，请检查
position.is.empty=输入的位置码不能为空，请检查
entry.bill.detailids.not.null=输入的入库单明细号不能为空或大于1000，请检查
update.rows.is.zero=更新的行数为0
entry.bill.status.not.null=输入的入库单状态不能为空或大于10，请检查
entry.bill.return.reason.gt200=输入的入库单回退原因不能大于200，请简单
entry.bill.postby.gt20=输入的入库单记账人不能大于20，请检查
entry.bill.submitby.gt20=输入的入库单提交人不能大于20，请检查
entry.bill.receiveby.gt20=输入的入库单接收人不能大于20，请检查
entry.bill.returnby.gt20=输入的入库单回退人不能大于20，请检查
entry.bill.not.null=输入的入库单号不能为空，请检查
stockno.not.null=输入的子库存不能为空，请检查
billno.or.palletno.not.null=输入的箱号或托盘号不能为空，请检查
scanby.is.err=输入的扫描人不能查到对应的用户ID，请检查
pallet.id.is.err=发生异常，托盘ID获取失败
wis.id.list.is.empty.or.exceed.max=wisidList为空或超过最大数量: {0}
sku.list.is.empty.or.exceed.max=skuList为空或超过最大数量: {0}
wis.id.can.only.contains.number.letter.underline=wisId只能包含数字字母下划线

contract.is.null=DQAS系统扫描校验失败,合同号为空
entity.is.null=DQAS系统扫描校验失败,任务号为空
barcode.is.null=DQAS系统扫描校验失败,物料条码为空
itemcode.is.null=DQAS系统扫描校验失败,物料代码为空
boxno.is.null=DQAS系统扫描校验失败,箱号为空
updateman.is.null=DQAS系统扫描校验失败,操作人为空
input.param.is.batch=输入参数包含逗号，不支持批量输入，请重新输入

Job.number.does.not.exist=工号不存在
any.thing.no=每一个参数都不能为空
task.no=任务号不存在
no.site=任务号下没有站点
there.are.no.materials.configured.for.this.site=此站点没有配置物料信息
task.number.does.not.match.with.environmental.attributes=此物料条码的环保属性与任务号的环保属性不相符合
barcode.information.is.not.found=条码信息查不到
the.barcode.has.been.bound=该条码已经在其他站点或者任务号绑定，若要绑定，请解绑该条码
The.barcode.under.this.configuration.has.been.scanned=该配置下的条码已经扫完
Barcode.under.configuration.modification.has.been.scanned=该配置下的条码已经扫描结束
The.number.of.scanned.bar.code.is.greater.than.the.number.of.configured.bar.code=该条码扫描数大于配置数
The.main.bar.code.is.controlled.by.technical.transformation=该条码受技改控制
Insert.main.bar.code.to.report.error=插入主条码报错
Operation.successful=操作成功
No.corresponding.configuration.information.was.found.for.barcode=条码没有对应的配置物料信息
dqas.verification.failed=DQAS校验不通过

many.mateial.Information=物料代码匹配到多个配置物料信息

no.find.box.info=未查询到装箱信息，请确认
no.bom.information.from.boxinfo=未找到箱对应的BOQ信息，无法打印
template.info.is.null=模板语言不能为空
bom.not.loaded=该箱未完成装箱扫描，不允许打印
box.is.virture=此箱为虚拟箱，无法打印，请确认
no.cbom.information.from.boxinfo=未找到箱对应的CBOM信息，无法打印
stock.no.is.null=在途库存不能为空
warehouseId.is.null=线边仓ID不能为空
get.flow.qty.error=获取仓储交易信息失败


num.must.over.zero=输入每页展示数量和页码数量必须大于0的整数
page.is.null=输入的查询页数为空，请确认
rows.is.null=输入的查询每页的数量为空，请确认
rows.is.over.200=查询失败，输入的查询每页的数量最大为200，请确认
item.code.num.is.over.50=物料代码批量查询数量最大为50，请确认
input.data.contain.point=输入每页展示数量和页码数量必须是整数，不能小数

Each.parameter.must.be.entered=每个参数必须输入
Softtaskno.startimeendtime.atleastone=软件任务号(开始时间结束时间)至少输入一个，其它必输
Per.org.cannot.repeat.in.8000220=有组织存在多个软件子库存，请重新维护数据字典编号：8000220
Paging.parameter.cannot.be.greater.than.200=分页参数不能大于200
The.number.of.entries.per.page.cannot.be.less.than.or.equalled.to.0=每页的行数不能小于或者等于0
The.requested.page.number.cannot.be.less.than.or.equalled.to.0=请求的页码不能小于或者等于0
The.start.time.of.query.cannot.be.greater.than.the.end.time=查询的开始时间不能大于结束时间
TASK.SOFT.NO.LESS.100=软件任务号数据必须小于等于100
There.is.no.data.for.pushing.PDM.during.this.period.or.the.push.fails=推送PDM这段时间内，没有数据或者推送失败
Box.list.quantity.is.too.large=箱列表数量过大



timer.synchronize.fail=informatica-CMS-WMES-cdm_contract_headers synchronization stop
timer.synchronize.wmes.fail=informatica-CMS-WMES-cdm_contract_lines synchronization stop
timer.synchronize.fail.head=informatica-app-mes-cdm_contract_headers synchronization stop
timer.synchronize.fail.line=informatica-app-mes-cdm_contract_lines synchronization stop
Organization.Id.must.be.entered=组织id必须输入
Sub.bar.codes.of=受技改管控的子条码为:
main.barcode.of=受技改管控的主条码为:
Material.barcode.must.be.entered=条码必须输入
The.function.module.must.be.entered=功能模块必须输入
The.page.is.too.big=每页数量太大
query.param.is.empty=查询时间起始时间结束时间不能超过15天.
pallet.number.is.over.50=托盘输入的数量不能超过50
query.task.list.is.empty=输入的任务号列表不能为空
query.task.is.no.more.than.200=输入的任务列表不能超过200个
entity.name.not.null=任务号不能为空！
boxup.is.not.null.or.gt.set.upper.limit=箱号不能为空或大于设置的上限
SN.is.null.or.exceeds.the.set.upper.limit=SN不能为空或大于设置的上限
auto.flag.is.not.null=自动上传标识不能为空
boxup.at.least.one.is.exists=输入的箱号至少有一个不存在
entity.name.list.length.should.less.50=任务号最多只能输入50个，请确认！
current.page.not.null=输入多个任务号时，需输入当前页进行查询！
page.size.not.greater.than.200=输入多个任务号时，每一页的页数不能大于200
validate.input.type.of.check.binding.error=输入类型是为空或格式不正确，请确认！
params.list.is.null=参数列表为空，请确认！
size.out.bound.params.list.of.check.binding=参数列表个数超过50,请确认！
entity.name.not.all.exist=存在任务号{0}不在系统中，请确认！
exist.not.bound.entity.name=存在必绑物料未绑的任务号，请确认！
exist.not.bound.mfg.site.id=存在必绑物料未绑的生产布点ID，请确认！
The.barcode.is.larger.than.five.layers.and.cannot.be.bound=条码大于五层，不能绑定
Same.code.same.material.code.with.substitution.relationship=相同代码/有替代关系的相同物料代码(
The.cannot.be.bound.hierarchically.as.a.primary.sub.relationship=)的不能做主子关系分层次绑定
This.barcode.has.been.bound.to=该条码已经绑定到
No.binding.is.allowed.on.the.barcode=条码上，不允许再绑定
entityName=任务
This.barcode.is.a.primary.barcode.and.cannot.be.bound.to.its.own.sub.barcode=该条码是主条码不能绑定到自己的子条码
If.this.subassembly.is.bound.to.this.parent.subassembly.the.binding.level.will.be.greater.than.5=该子部件若绑定该父部件，则绑定层次会大于5层
Primary.barcode.and.sub.barcode.cannot.be.the.same.barcode=主条码和子条码不能是同一条码
The.barcode.is.bound.to.the.task.number=条码绑定在任务号(
Next.to.bind.please.unbind.the.barcode=)下，若要绑定，请解绑该条码
Verification.succeeded=校验成功
This.barcode.has.been.bound.in.other.sites.To.bind.please.unbind.the.barcode=该条码已经在其他站点绑定，若要绑定，请解绑该条码
The.barcode.has.been.scanned=该条码已扫完
this.barcode.dt.barcode=该条码
itemCode.dt.itemCode=的物料代码
messageID.dt.messageID=物料id
repleacedID.dt.repleacedID=替代物料代码不存在
diction.is.null=数据字典配置为空
scan.no.end={0}未扫描结束，无法获取箱明细数据
bill.and.orgid.cannot.empty=装箱单号和组织不能为空
no.bill.info=没有装箱信息
token.is.not.null=token值验证失败 
sessionid.is.not.null=sessionid不能为空
no.taskname=没有任务号（导入任务&&未扫描任务&&BOQ任务）
Abnormal.verification.of.job.number=工号验证失败
The.maximum.value.of.the.box.list.cannot.exceed=箱列表的最大值:
The.page.number.is.no.more.then=页码不能超过
The.page.or.The.CurrentPage.is.no.negative=页码、当前页必须大于0
the.param.is.empty=参数为空
the.box.is.empty=托盘和箱不能同时为空
the.box.is.not.empty=托盘和箱不能同时输入
the.entity.is.not.exist=任务号不存在
the.SubInvCode.is.not.exist=库位不存在
boxnumber=箱号:
auax.stimulate.summit=箱具有主辅箱关系，必须同时提交
virtual.box.number=有部分箱号的体积或重量为0，且不是虚拟箱，不允许入库
map.box.pallet=输入的箱号存在托盘不允许入库
ENTRY_WARE_HOUSE=已经入库
Pallet.not.canned.end=托盘未扫描结束
No.termail.task.is.must.approval.by.factory=不是类终端任务必须检查存在出厂审批信息,单据号:{0},会签人:{1}
Bill.status.not.is.submitted=单据状态不为已提交，不能接收！
Taskno.is.not.move.in.erp=任务号未在ERP做工序移动
Taskno.is.not.exists.in.erp=ERP中无该任务号
Taskno.status.isnot.pack=该任务子状态不为包装
not.canned.end=存在箱未扫描结束
The.sub.barcode.cannot.be.a.barcode.starting.with.21CA=子条码不能是21CA开头的条码
Under.the.whole.machine.task.the.sub.barcode.can.only.have.four.layers.of.material.codes=整机站点下，子条码只能是四层物料代码
Under.the.task.of.the.whole.machine.the.primary.barcode.cannot.be.a.two.layer.and.three.layer.material.code=整机站点下，主条码不能是二层三层物料代码
Box.no.item.no.scaned=箱没有物料信息
There.is.no.case.number.on.the.pallet.number=托盘上没有箱号
entry.ware.house=有箱已入库请检查
pallent.no.scan.end=托盘未扫描结束，不得入库
do.not.enter.empty.box=空箱不准入库
box.pallet.is.not.exist=箱号托盘不存在，或者托盘箱号不在当前任务下
cannot.input.box=不能输入箱号
the.gross.and.netweight.is.not.zero=箱的毛重净重不能为零
exists.in.order=已有入库单
Itembarcode.between.0.and.50=条码数量大于0小于等于50
Pagesize.between.0.and.%d=页码大小大于0小于等于%d
query.count.cannot.be.greater.than.500=一次最多查询500个批次！
prodplan.id.null=批次不能为空
The.main.barcode.is.21CA.which.must.be.a.complete.machine.site=非整机站点，不能扫描21CA条码
redis.lock.fail={0} 正在操作，请稍后再试!
sn.miss.board.online={0}条码在board online 不存在，请稍后再试!
customer.name.isnot.null=用户地址不能为空
sn.not.more.than.500=服务器条码不能超过500
taskno.and.sn.isnot.null=任务号和服务器条码不能同时为空
submit.time.interval.more.than.180.days=提交时间跨度不能超过180天
verify.time.interval.more.than.180.days=确认时间跨度不能超过180天
query.param.error=批次和提交时间和确认时间三者必填其一
failed.to.get.redis.lock=获取redis锁资源失败，请重试
call.service.error=调用服务异常：{0}--{1}
failed_to_obtain_customer_basic_information=根据物料代码获取客户基础信息失败
sys.look.not.config = 数据字典 {0} 未配置
entity.name.need.user.address = 输入任务名称需要携带用户地址
failed_to_call_mds_interface=调MDS接口失败:{0}
failed_to_get_barcode_center_url=获取数据字典配置条码中心接口URL失败
call.barCode.center.expandQuery.barCode.falied=调条码中心获取条码扩展信息失败:{0}
get.zs.log.station.error=工站日志接口异常
autoflag.is.not.null=自动上传标识不能为空
tasks_cannot_exceed_20=任务不能超过20个
archive.extract.task.executing=待归档数据同步任务正在执行中
archive.task.executing=归档任务正在执行中
archive.semiprod.null=调用服务获取待归档半成品单管理单据信息返回空
archive.semiprod.detail.null=调用服务获取待归档半成品单管理单据明细信息返回空
archive.payable.null=调用服务获取待归档应付款结算单据信息返回空
archive.manufacturingorder.server.error=调用服务获取制造通知单返回报错
unarchive.manufacturingorder.server.error=调用服务获取待归档制造通知单据返回报错
archive.manufacturingorder.download.error=文件下载出错
archive.testprocess.server.error=调用服务获取测试工艺单返回报错
unarchive.testprocess.server.error=调用服务获取待归档测试工艺单据返回报错
b2b.return.cn=你好，单据{0}存在b2b返回失败批次，请检查。
task.no.not.exist=任务号{0}不存在
task.no.not.more.than.100=任务号不能超过100个
type.no.is.null=未查询到分类编码
regulation.is.null=未查询到不合格品性质规则
master.type.is.null=材料大类不能为空
small.type.is.null=材料小类不能为空
sub.type.is.null=材料此小类不能为空
is.item.is.null=是否材料类型不能为空
category.is.null=分类不能为空
defects.qty.is.null=不合格数量不能为空
contract.and.task.is.null=合同号和任务号不能都为空
choose.no.imei.repeat=发货通知单号IMEI:{0} 重复
choose.no.repeat=发货通知单号重复：{0}
choose.no.exist=发货通知单号已经存在：{0}
imei.box.not.find=没有找到串号{0}的箱号
box.info.not.find={0}箱信息未找到
box.imei.item.diff={0}箱物料代码{1}与串号{2}物料代码不一致
box.imei.sub.diff={0}箱子库存{1}与串号{2}子库存不一致
box.subinventory.not.find={0}小箱号的子库存为空，不能扫描!
box.warehouse.not.in=串号{0}对应的箱{1}不是在库状态
imei.scaned=串号{0}已经扫描
bill.no.exists = 单据号已经存在 {0}，请确认！
issue.date.not.null=发文日期不能为空
tray.code.is.null=托盘码不能为空
outer.box.list.is.null=外箱列表不能为空
en.code.list.is.null=EN列表不能为空
mes.info.is.null=MES返回的装箱信息为空
forward.and.mtp.test.upload.success.not.exists=不存在正向数据及MPT测试日志上传成功的记录
discrete.param.name.error = 参数类型为数据段时，paramList列表长度必须为2
exist.not.param.name =  参数名称与数据库不匹配
param.list.is.null = 参数值不能为空
param.type.is.incorrect = 参数类型不正确
exist.not.data.type = 数据类型输入有误
exist.not.param.Repeated = 数据参数名称不能重复
item.code.of.mes.is.different=所选物料代码与MES物料代码不一致
mes.packing.info.is.null=MES返回的装箱信息为空
failed_to_specified_ps_task=查询车间计划_任务信息获取接口失败
item_control_entry_fail=特殊物料{0}管控，{1}任务禁止发料，请联系业务代表张晓沟通；
required_fields_cannot_be_empty=必填字段{0}不能为空
server.sn.not.in.first.two.layers=输入的SERVER_SN在配置物料绑定关系中不属于前两层，SN={0}
server.sn.max.input.limit=最多支持输入5个服务器SN